package com.example.doancuoikyjava.model;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Model class for chat messages between students and teachers
 */
public class ChatMessage {
    
    public enum MessageType {
        TEXT,           // Regular text message
        JOIN,           // User joined chat
        LEAVE,          // User left chat
        TYPING,         // User is typing
        SYSTEM          // System message
    }
    
    private String messageId;
    private String senderId;
    private String senderName;
    private String receiverId;
    private String receiverName;
    private String content;
    private MessageType type;
    private LocalDateTime timestamp;
    private boolean isRead;
    private String courseId;        // Optional: for course-specific chats
    private String courseName;      // Optional: for course-specific chats
    
    // Constructors
    public ChatMessage() {
        this.timestamp = LocalDateTime.now();
        this.isRead = false;
        this.type = MessageType.TEXT;
    }
    
    public ChatMessage(String senderId, String senderName, String receiverId, String receiverName, String content) {
        this();
        this.senderId = senderId;
        this.senderName = senderName;
        this.receiverId = receiverId;
        this.receiverName = receiverName;
        this.content = content;
        this.messageId = generateMessageId();
    }
    
    public ChatMessage(String senderId, String senderName, String receiverId, String receiverName, 
                      String content, MessageType type) {
        this(senderId, senderName, receiverId, receiverName, content);
        this.type = type;
    }
    
    // Generate unique message ID
    private String generateMessageId() {
        return "MSG_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 10000);
    }
    
    // Getters and Setters
    public String getMessageId() {
        return messageId;
    }
    
    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }
    
    public String getSenderId() {
        return senderId;
    }
    
    public void setSenderId(String senderId) {
        this.senderId = senderId;
    }
    
    public String getSenderName() {
        return senderName;
    }
    
    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }
    
    public String getReceiverId() {
        return receiverId;
    }
    
    public void setReceiverId(String receiverId) {
        this.receiverId = receiverId;
    }
    
    public String getReceiverName() {
        return receiverName;
    }
    
    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public MessageType getType() {
        return type;
    }
    
    public void setType(MessageType type) {
        this.type = type;
    }
    
    public LocalDateTime getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }
    
    public boolean isRead() {
        return isRead;
    }
    
    public void setRead(boolean read) {
        isRead = read;
    }
    
    public String getCourseId() {
        return courseId;
    }
    
    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }
    
    public String getCourseName() {
        return courseName;
    }
    
    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }
    
    // Utility methods
    public String getFormattedTimestamp() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm");
        return timestamp.format(formatter);
    }
    
    public String getTimeOnly() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        return timestamp.format(formatter);
    }
    
    public boolean isSentByUser(String userId) {
        return senderId != null && senderId.equals(userId);
    }
    
    public boolean isReceivedByUser(String userId) {
        return receiverId != null && receiverId.equals(userId);
    }
    
    public boolean involvesUser(String userId) {
        return isSentByUser(userId) || isReceivedByUser(userId);
    }
    
    public String getOtherUserId(String currentUserId) {
        if (isSentByUser(currentUserId)) {
            return receiverId;
        } else if (isReceivedByUser(currentUserId)) {
            return senderId;
        }
        return null;
    }
    
    public String getOtherUserName(String currentUserId) {
        if (isSentByUser(currentUserId)) {
            return receiverName;
        } else if (isReceivedByUser(currentUserId)) {
            return senderName;
        }
        return null;
    }
    
    @Override
    public String toString() {
        return String.format("[%s] %s -> %s: %s", 
                           getFormattedTimestamp(), 
                           senderName, 
                           receiverName, 
                           content);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        ChatMessage that = (ChatMessage) obj;
        return messageId != null ? messageId.equals(that.messageId) : that.messageId == null;
    }
    
    @Override
    public int hashCode() {
        return messageId != null ? messageId.hashCode() : 0;
    }
}
