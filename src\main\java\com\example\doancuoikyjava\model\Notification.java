package com.example.doancuoikyjava.model;

import java.time.LocalDateTime;

public class Notification {
    
    public enum NotificationType {
        GENERAL,    // Thông báo chung
        ACADEMIC,   // Thông báo học tập
        EXAM,       // Thông báo thi cử
        EVENT,      // Thông báo sự kiện
        URGENT      // Thông báo khẩn cấp
    }
    
    public enum TargetAudience {
        ALL,        // Tất cả
        STUDENTS,   // Chỉ sinh viên
        TEACHERS,   // Chỉ giảng viên
        SPECIFIC    // Đối tượng cụ thể
    }
    
    public enum Priority {
        LOW,        // Thấp
        NORMAL,     // B<PERSON><PERSON> thường
        HIGH,       // Cao
        URGENT      // Khẩn cấp
    }
    
    private String notificationId;
    private String id; // For AdvancedNotificationService compatibility
    private String recipientId; // For AdvancedNotificationService compatibility
    private String title;
    private String content;
    private String message; // For AdvancedNotificationService compatibility
    private NotificationType type;
    private String typeString; // For AdvancedNotificationService compatibility
    private TargetAudience targetAudience;
    private Priority priority;
    private String priorityString; // For AdvancedNotificationService compatibility
    private String createdBy;
    private LocalDateTime createdAt;
    private LocalDateTime readAt; // For AdvancedNotificationService compatibility
    private LocalDateTime expiryDate;
    private boolean isActive;
    private boolean read; // For AdvancedNotificationService compatibility
    private String attachmentPath;
    private int viewCount;
    
    // Constructors
    public Notification() {
        this.createdAt = LocalDateTime.now();
        this.isActive = true;
        this.viewCount = 0;
        this.read = false;
    }

    public Notification(String title, String content, NotificationType type,
                       TargetAudience targetAudience, Priority priority, String createdBy) {
        this();
        this.title = title;
        this.content = content;
        this.message = content; // Sync with AdvancedNotificationService
        this.type = type;
        this.targetAudience = targetAudience;
        this.priority = priority;
        this.createdBy = createdBy;
    }
    
    // Getters and Setters
    public String getNotificationId() {
        return notificationId;
    }
    
    public void setNotificationId(String notificationId) {
        this.notificationId = notificationId;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public NotificationType getType() {
        return type;
    }
    
    public void setType(NotificationType type) {
        this.type = type;
    }
    
    public TargetAudience getTargetAudience() {
        return targetAudience;
    }
    
    public void setTargetAudience(TargetAudience targetAudience) {
        this.targetAudience = targetAudience;
    }
    
    public Priority getPriority() {
        return priority;
    }
    
    public void setPriority(Priority priority) {
        this.priority = priority;
    }
    
    public String getCreatedBy() {
        return createdBy;
    }
    
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getExpiryDate() {
        return expiryDate;
    }
    
    public void setExpiryDate(LocalDateTime expiryDate) {
        this.expiryDate = expiryDate;
    }
    
    public boolean isActive() {
        return isActive;
    }
    
    public void setActive(boolean active) {
        isActive = active;
    }
    
    public String getAttachmentPath() {
        return attachmentPath;
    }
    
    public void setAttachmentPath(String attachmentPath) {
        this.attachmentPath = attachmentPath;
    }
    
    public int getViewCount() {
        return viewCount;
    }
    
    public void setViewCount(int viewCount) {
        this.viewCount = viewCount;
    }

    // AdvancedNotificationService compatibility methods
    public String getId() { return id; }
    public void setId(String id) {
        this.id = id;
        this.notificationId = id; // Keep in sync
    }

    public String getRecipientId() { return recipientId; }
    public void setRecipientId(String recipientId) { this.recipientId = recipientId; }

    public String getMessage() { return message; }
    public void setMessage(String message) {
        this.message = message;
        this.content = message; // Keep in sync
    }

    public String getTypeString() { return typeString; }
    public void setTypeString(String type) { this.typeString = type; }

    public String getPriorityString() { return priorityString; }
    public void setPriorityString(String priority) { this.priorityString = priority; }

    public LocalDateTime getReadAt() { return readAt; }
    public void setReadAt(LocalDateTime readAt) { this.readAt = readAt; }

    public boolean isRead() { return read; }
    public void setRead(boolean read) { this.read = read; }

    // Utility methods
    public boolean isExpired() {
        return expiryDate != null && LocalDateTime.now().isAfter(expiryDate);
    }
    
    public void incrementViewCount() {
        this.viewCount++;
    }
    
    public String getTypeIcon() {
        return switch (type) {
            case GENERAL -> "📢";
            case ACADEMIC -> "📚";
            case EXAM -> "📝";
            case EVENT -> "🎉";
            case URGENT -> "🚨";
        };
    }
    
    public String getPriorityIcon() {
        return switch (priority) {
            case LOW -> "🔵";
            case NORMAL -> "🟡";
            case HIGH -> "🟠";
            case URGENT -> "🔴";
        };
    }
    
    public String getTargetIcon() {
        return switch (targetAudience) {
            case ALL -> "👥";
            case STUDENTS -> "👨‍🎓";
            case TEACHERS -> "👨‍🏫";
            case SPECIFIC -> "🎯";
        };
    }
    
    public String getFormattedCreatedAt() {
        return createdAt.format(java.time.format.DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm"));
    }
    
    public String getFormattedExpiryDate() {
        return expiryDate != null ? 
               expiryDate.format(java.time.format.DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")) : 
               "Không giới hạn";
    }
    
    public String getStatusText() {
        if (!isActive) return "Đã tắt";
        if (isExpired()) return "Đã hết hạn";
        return "Đang hoạt động";
    }
    
    public String getStatusColor() {
        if (!isActive) return "#95a5a6";
        if (isExpired()) return "#e74c3c";
        return "#27ae60";
    }
    
    @Override
    public String toString() {
        return "Notification{" +
                "id='" + notificationId + '\'' +
                ", title='" + title + '\'' +
                ", type=" + type +
                ", target=" + targetAudience +
                ", priority=" + priority +
                ", active=" + isActive +
                ", views=" + viewCount +
                '}';
    }
}
