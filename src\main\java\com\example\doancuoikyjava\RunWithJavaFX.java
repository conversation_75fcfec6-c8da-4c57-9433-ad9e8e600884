package com.example.doancuoikyjava;

public class RunWithJavaFX {
    public static void main(String[] args) {
        // Kiểm tra JavaFX runtime bằng cách thử nạp một class JavaFX
        try {
            Class.forName("javafx.application.Application");
        } catch (ClassNotFoundException e) {
            System.err.println("❌ Thiếu thành phần JavaFX runtime!");
            System.err.println("➡ Vui lòng thêm VM options sau khi chạy:");
            System.err.println("   --module-path \"đường_dẫn\\tới\\javafx-sdk-XX\\lib\" --add-modules javafx.controls,javafx.fxml");
            System.err.println("➡ Thay \"đường_dẫn\\tới\\javafx-sdk-XX\\lib\" bằng đường dẫn thư mục lib của JavaFX SDK trên máy bạn.");
            System.err.println("➡ Ví dụ với IntelliJ: Vào Run > Edit Configurations > VM options");
            System.exit(1);
        }

        // Chọn dashboard để chạy (bỏ comment dòng bạn muốn chạy)
        // AdminLauncher.main(args);
        // TeacherLauncher.main(args);
        // StudentLauncher.main(args);
        MainApplication.main(args);
    }
}
