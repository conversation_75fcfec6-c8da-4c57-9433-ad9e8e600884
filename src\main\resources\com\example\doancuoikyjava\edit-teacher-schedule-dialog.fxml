<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.example.doancuoikyjava.controller.EditTeacherScheduleDialogController">
   <children>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="10.0" style="-fx-background-color: #FF9800; -fx-padding: 15px 20px;">
         <children>
            <Label text="✏️" style="-fx-font-size: 20px; -fx-text-fill: white;" />
            <Label fx:id="titleLabel" text="CHỈNH SỬA LỊCH DẠY" style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: white;" />
         </children>
      </HBox>
      
      <!-- Content -->
      <ScrollPane fitToWidth="true" prefHeight="500.0">
         <content>
            <VBox spacing="20.0" style="-fx-padding: 20px;">
               <children>
                  <!-- Course Information (Read-only) -->
                  <VBox spacing="15.0" style="-fx-background-color: #F5F5F5; -fx-background-radius: 8px; -fx-padding: 15px;">
                     <children>
                        <Label text="Thông tin môn học" style="-fx-font-weight: bold; -fx-font-size: 16px; -fx-text-fill: #2c3e50;" />
                        
                        <HBox spacing="20.0">
                           <children>
                              <VBox spacing="5.0" HBox.hgrow="ALWAYS">
                                 <children>
                                    <Label text="Tên môn học" style="-fx-font-weight: bold; -fx-font-size: 14px;" />
                                    <Label fx:id="courseNameLabel" text="Lập trình Java" style="-fx-font-size: 14px; -fx-text-fill: #2196F3;" />
                                 </children>
                              </VBox>
                              
                              <VBox spacing="5.0">
                                 <children>
                                    <Label text="Số tín chỉ" style="-fx-font-weight: bold; -fx-font-size: 14px;" />
                                    <Label fx:id="creditsLabel" text="3" style="-fx-font-size: 14px; -fx-text-fill: #4CAF50;" />
                                 </children>
                              </VBox>
                              
                              <VBox spacing="5.0">
                                 <children>
                                    <Label text="Số sinh viên" style="-fx-font-weight: bold; -fx-font-size: 14px;" />
                                    <Label fx:id="studentsCountLabel" text="25" style="-fx-font-size: 14px; -fx-text-fill: #FF5722;" />
                                 </children>
                              </VBox>
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Schedule Editing -->
                  <VBox spacing="15.0" style="-fx-background-color: white; -fx-border-color: #E0E0E0; -fx-border-radius: 8px; -fx-background-radius: 8px; -fx-padding: 20px;">
                     <children>
                        <Label text="Chỉnh sửa lịch dạy" style="-fx-font-weight: bold; -fx-font-size: 16px; -fx-text-fill: #2c3e50;" />
                        
                        <!-- Day and Time Selection -->
                        <HBox spacing="20.0">
                           <children>
                              <!-- Day Selection -->
                              <VBox spacing="10.0" HBox.hgrow="ALWAYS">
                                 <children>
                                    <Label text="Thứ trong tuần *" style="-fx-font-weight: bold; -fx-font-size: 14px;" />
                                    <ComboBox fx:id="dayComboBox" prefWidth="180.0" promptText="Chọn thứ" />
                                 </children>
                              </VBox>
                              
                              <!-- Start Time -->
                              <VBox spacing="10.0" HBox.hgrow="ALWAYS">
                                 <children>
                                    <Label text="Giờ bắt đầu *" style="-fx-font-weight: bold; -fx-font-size: 14px;" />
                                    <ComboBox fx:id="startTimeComboBox" prefWidth="120.0" promptText="Giờ bắt đầu" />
                                 </children>
                              </VBox>
                              
                              <!-- End Time -->
                              <VBox spacing="10.0" HBox.hgrow="ALWAYS">
                                 <children>
                                    <Label text="Giờ kết thúc *" style="-fx-font-weight: bold; -fx-font-size: 14px;" />
                                    <ComboBox fx:id="endTimeComboBox" prefWidth="120.0" promptText="Giờ kết thúc" />
                                 </children>
                              </VBox>
                           </children>
                        </HBox>
                        
                        <!-- Classroom and Week Type -->
                        <HBox spacing="20.0">
                           <children>
                              <!-- Classroom -->
                              <VBox spacing="10.0" HBox.hgrow="ALWAYS">
                                 <children>
                                    <Label text="Phòng học *" style="-fx-font-weight: bold; -fx-font-size: 14px;" />
                                    <ComboBox fx:id="classroomComboBox" prefWidth="150.0" promptText="Chọn phòng học" />
                                 </children>
                              </VBox>
                              
                              <!-- Week Type -->
                              <VBox spacing="10.0" HBox.hgrow="ALWAYS">
                                 <children>
                                    <Label text="Loại tuần" style="-fx-font-weight: bold; -fx-font-size: 14px;" />
                                    <ComboBox fx:id="weekTypeComboBox" prefWidth="150.0" promptText="Chọn loại tuần" />
                                 </children>
                              </VBox>
                           </children>
                        </HBox>
                        
                        <!-- Status -->
                        <VBox spacing="10.0">
                           <children>
                              <Label text="Trạng thái" style="-fx-font-weight: bold; -fx-font-size: 14px;" />
                              <ComboBox fx:id="statusComboBox" prefWidth="200.0" promptText="Chọn trạng thái" />
                           </children>
                        </VBox>
                     </children>
                  </VBox>
                  
                  <!-- Conflict Check -->
                  <VBox fx:id="conflictCheckBox" spacing="10.0" style="-fx-background-color: #FFF3E0; -fx-border-color: #FF9800; -fx-border-radius: 8px; -fx-background-radius: 8px; -fx-padding: 15px;" visible="false">
                     <children>
                        <Label text="Kiểm tra xung đột lịch học" style="-fx-font-weight: bold; -fx-font-size: 14px;" />
                        <Label fx:id="conflictResultLabel" text="Nhấn 'Kiểm tra xung đột' để xác minh lịch học" style="-fx-font-size: 12px; -fx-wrap-text: true;" />
                     </children>
                  </VBox>
                  
                  <!-- Preview -->
                  <VBox spacing="10.0" style="-fx-background-color: #E8F5E8; -fx-border-color: #4CAF50; -fx-border-radius: 8px; -fx-background-radius: 8px; -fx-padding: 15px;">
                     <children>
                        <Label text="Xem trước lịch học" style="-fx-font-weight: bold; -fx-font-size: 14px;" />
                        <Label fx:id="previewLabel" text="Chọn thông tin để xem trước" style="-fx-font-size: 12px; -fx-wrap-text: true;" />
                     </children>
                  </VBox>
                  
                  <!-- Notes -->
                  <VBox spacing="10.0">
                     <children>
                        <Label text="Ghi chú" style="-fx-font-weight: bold; -fx-font-size: 14px;" />
                        <TextArea fx:id="notesTextArea" prefHeight="80.0" promptText="Nhập ghi chú về việc thay đổi lịch dạy..." style="-fx-background-radius: 8px; -fx-border-radius: 8px;" />
                     </children>
                  </VBox>
               </children>
            </VBox>
         </content>
      </ScrollPane>
      
      <!-- Action Buttons -->
      <HBox alignment="CENTER_RIGHT" spacing="15.0" style="-fx-background-color: #F5F5F5; -fx-border-color: #E0E0E0; -fx-border-width: 1px 0 0 0; -fx-padding: 15px 20px;">
         <children>
            <Button fx:id="checkConflictBtn" onAction="#checkConflicts" text="🔍 Kiểm tra xung đột" style="-fx-background-color: #FF9800; -fx-text-fill: white; -fx-background-radius: 5px; -fx-padding: 10px 20px; -fx-font-weight: bold;" />
            <Button fx:id="cancelBtn" onAction="#cancel" text="❌ Hủy" style="-fx-background-color: #6c757d; -fx-text-fill: white; -fx-background-radius: 5px; -fx-padding: 10px 20px; -fx-font-weight: bold;" />
            <Button fx:id="saveBtn" onAction="#save" text="💾 Lưu thay đổi" style="-fx-background-color: #4CAF50; -fx-text-fill: white; -fx-background-radius: 5px; -fx-padding: 10px 20px; -fx-font-weight: bold;" />
         </children>
      </HBox>
   </children>
</VBox>
