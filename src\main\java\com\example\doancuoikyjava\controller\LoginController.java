package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.User;
import com.example.doancuoikyjava.service.UserService;
import com.example.doancuoikyjava.util.SceneManager;
import com.example.doancuoikyjava.util.SessionManager;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.input.KeyCode;
import javafx.scene.input.KeyEvent;

import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;

public class LoginController implements Initializable {
    
    @FXML private TextField usernameField;
    @FXML private PasswordField passwordField;
    @FXML private ComboBox<String> roleComboBox;
    @FXML private Button loginButton;
    @FXML private Label errorLabel;
    
    private final UserService userService = new UserService();
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        // Setup role options
        roleComboBox.getItems().addAll("ADMIN", "TEACHER", "STUDENT");
        
        // Hide error label initially
        errorLabel.setVisible(false);
        
        // Add key event handlers
        usernameField.setOnKeyPressed(this::handleKeyPressed);
        passwordField.setOnKeyPressed(this::handleKeyPressed);
        roleComboBox.setOnKeyPressed(this::handleKeyPressed);
        
        // Clear error when user starts typing
        usernameField.textProperty().addListener((obs, oldText, newText) -> clearError());
        passwordField.textProperty().addListener((obs, oldText, newText) -> clearError());
        roleComboBox.valueProperty().addListener((obs, oldValue, newValue) -> clearError());
        
        System.out.println("✅ Login Controller initialized");
    }
    
    @FXML
    private void handleLogin() {
        String username = usernameField.getText().trim();
        String password = passwordField.getText();
        String role = roleComboBox.getValue();
        
        // Validate input
        if (username.isEmpty()) {
            showError("Vui lòng nhập tên đăng nhập!");
            usernameField.requestFocus();
            return;
        }
        
        if (password.isEmpty()) {
            showError("Vui lòng nhập mật khẩu!");
            passwordField.requestFocus();
            return;
        }
        
        if (role == null) {
            showError("Vui lòng chọn vai trò!");
            roleComboBox.requestFocus();
            return;
        }
        
        // Authenticate user
        User user = userService.authenticate(username, password, User.UserRole.valueOf(role));
        
        if (user != null) {
            // Set current user in both managers
            SceneManager.setCurrentUser(user);
            SessionManager.setCurrentUser(user);

            System.out.println("✅ User set in session: " + user.getFullName() + " (" + user.getRole() + ")");

            try {
                // Navigate to appropriate dashboard based on role
                switch (user.getRole()) {
                    case ADMIN:
                        SceneManager.switchScene("/com/example/doancuoikyjava/admin-dashboard.fxml", 
                                               "Quản trị viên - " + user.getFullName());
                        break;
                    case TEACHER:
                        SceneManager.switchScene("/com/example/doancuoikyjava/teacher-dashboard.fxml", 
                                               "Giáo viên - " + user.getFullName());
                        break;
                    case STUDENT:
                        SceneManager.switchScene("/com/example/doancuoikyjava/student-dashboard.fxml", 
                                               "Sinh viên - " + user.getFullName());
                        break;
                }
            } catch (IOException e) {
                showError("Lỗi khi chuyển trang: " + e.getMessage());
                e.printStackTrace();
            }
        } else {
            showError("Tên đăng nhập, mật khẩu hoặc vai trò không đúng!");
            passwordField.clear();
            passwordField.requestFocus();
        }
    }
    
    private void handleKeyPressed(KeyEvent event) {
        if (event.getCode() == KeyCode.ENTER) {
            handleLogin();
        }
    }
    
    private void showError(String message) {
        errorLabel.setText(message);
        errorLabel.setVisible(true);
    }
    
    private void clearError() {
        errorLabel.setVisible(false);
    }

    // Quick login methods for demo accounts
    @FXML
    private void quickLoginAdmin() {
        usernameField.setText("admin");
        passwordField.setText("admin123");
        roleComboBox.setValue("ADMIN");
        handleLogin();
    }

    @FXML
    private void quickLoginTeacher() {
        usernameField.setText("teacher");
        passwordField.setText("teacher123");
        roleComboBox.setValue("TEACHER");
        handleLogin();
    }

    @FXML
    private void quickLoginStudent() {
        usernameField.setText("student");
        passwordField.setText("student123");
        roleComboBox.setValue("STUDENT");
        handleLogin();
    }
}
