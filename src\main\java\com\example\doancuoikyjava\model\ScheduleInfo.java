package com.example.doancuoikyjava.model;

/**
 * Model class for schedule information
 */
public class ScheduleInfo {
    private String day;
    private String time;
    private String courseName;
    private String classroom;
    private int studentsCount;
    private int credits;
    private String status;
    
    public ScheduleInfo() {
    }
    
    public ScheduleInfo(String day, String time, String courseName, String classroom, 
                       int studentsCount, int credits, String status) {
        this.day = day;
        this.time = time;
        this.courseName = courseName;
        this.classroom = classroom;
        this.studentsCount = studentsCount;
        this.credits = credits;
        this.status = status;
    }
    
    // Getters and setters
    public String getDay() { 
        return day; 
    }
    
    public void setDay(String day) { 
        this.day = day; 
    }
    
    public String getTime() { 
        return time; 
    }
    
    public void setTime(String time) { 
        this.time = time; 
    }
    
    public String getCourseName() { 
        return courseName; 
    }
    
    public void setCourseName(String courseName) { 
        this.courseName = courseName; 
    }
    
    public String getClassroom() { 
        return classroom; 
    }
    
    public void setClassroom(String classroom) { 
        this.classroom = classroom; 
    }
    
    public int getStudentsCount() { 
        return studentsCount; 
    }
    
    public void setStudentsCount(int studentsCount) { 
        this.studentsCount = studentsCount; 
    }
    
    public int getCredits() { 
        return credits; 
    }
    
    public void setCredits(int credits) { 
        this.credits = credits; 
    }
    
    public String getStatus() { 
        return status; 
    }
    
    public void setStatus(String status) { 
        this.status = status; 
    }
    
    @Override
    public String toString() {
        return "ScheduleInfo{" +
                "day='" + day + '\'' +
                ", time='" + time + '\'' +
                ", courseName='" + courseName + '\'' +
                ", classroom='" + classroom + '\'' +
                ", studentsCount=" + studentsCount +
                ", credits=" + credits +
                ", status='" + status + '\'' +
                '}';
    }
}
