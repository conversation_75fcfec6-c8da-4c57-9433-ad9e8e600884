<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.example.doancuoikyjava.controller.TeacherStudentsController">
   <top>
      <HBox alignment="CENTER_LEFT" styleClass="nav-bar" spacing="20.0">
         <children>
            <Button fx:id="backButton" onAction="#goBack" styleClass="nav-button" text="← Quay lại" />
            <Label styleClass="nav-button" text="👨‍🎓 DANH SÁCH SINH VIÊN">
               <font>
                  <Font name="System Bold" size="16.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Label fx:id="welcomeLabel" styleClass="nav-button" text="Xin chào, Giáo viên">
               <font>
                  <Font size="14.0" />
               </font>
            </Label>
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </HBox>
   </top>
   <center>
      <VBox styleClass="content-container" spacing="20.0">
         <children>
            <HBox alignment="CENTER_LEFT" spacing="20.0">
               <children>
                  <Label styleClass="label-title" text="Danh sách sinh viên">
                     <font>
                        <Font name="System Bold" size="24.0" />
                     </font>
                  </Label>
                  <Region HBox.hgrow="ALWAYS" />
                  <Button fx:id="refreshBtn" onAction="#refreshData" styleClass="btn,btn-secondary" text="🔄 Làm mới" />
               </children>
            </HBox>
            
            <HBox spacing="15.0">
               <children>
                  <ComboBox fx:id="courseFilterComboBox" promptText="Chọn môn học" />
                  <TextField fx:id="searchField" promptText="Tìm kiếm sinh viên..." HBox.hgrow="ALWAYS" />
                  <Button fx:id="searchBtn" onAction="#searchStudents" styleClass="btn,btn-warning" text="Tìm kiếm" />
               </children>
            </HBox>
            
            <TableView fx:id="studentsTableView" VBox.vgrow="ALWAYS">
               <columns>
                  <TableColumn fx:id="studentIdColumn" prefWidth="100.0" text="MSSV" />
                  <TableColumn fx:id="fullNameColumn" prefWidth="200.0" text="Họ và tên" />
                  <TableColumn fx:id="classNameColumn" prefWidth="100.0" text="Lớp" />
                  <TableColumn fx:id="majorColumn" prefWidth="150.0" text="Ngành" />
                  <TableColumn fx:id="yearColumn" prefWidth="80.0" text="Năm" />
                  <TableColumn fx:id="emailColumn" prefWidth="200.0" text="Email" />
                  <TableColumn fx:id="phoneColumn" prefWidth="120.0" text="Điện thoại" />
                  <TableColumn fx:id="courseColumn" prefWidth="200.0" text="Môn học đăng ký" />
                  <TableColumn fx:id="actionsColumn" prefWidth="120.0" text="Thao tác" />
               </columns>
            </TableView>
            
            <HBox spacing="20.0">
               <children>
                  <VBox styleClass="card" spacing="15.0" HBox.hgrow="ALWAYS">
                     <children>
                        <Label styleClass="card-header" text="Thống kê lớp học">
                           <font>
                              <Font name="System Bold" size="18.0" />
                           </font>
                        </Label>
                        <HBox spacing="20.0">
                           <children>
                              <VBox styleClass="stats-card">
                                 <children>
                                    <Label fx:id="totalStudentsLabel" styleClass="stats-number" text="0">
                                       <font>
                                          <Font name="System Bold" size="24.0" />
                                       </font>
                                    </Label>
                                    <Label styleClass="stats-label" text="TỔNG SINH VIÊN">
                                       <font>
                                          <Font name="System Bold" size="12.0" />
                                       </font>
                                    </Label>
                                 </children>
                              </VBox>
                              
                              <VBox styleClass="stats-card">
                                 <children>
                                    <Label fx:id="totalCoursesLabel" styleClass="stats-number" text="0">
                                       <font>
                                          <Font name="System Bold" size="24.0" />
                                       </font>
                                    </Label>
                                    <Label styleClass="stats-label" text="MÔN HỌC">
                                       <font>
                                          <Font name="System Bold" size="12.0" />
                                       </font>
                                    </Label>
                                 </children>
                              </VBox>
                              
                              <VBox styleClass="stats-card">
                                 <children>
                                    <Label fx:id="averageGpaLabel" styleClass="stats-number" text="0.00">
                                       <font>
                                          <Font name="System Bold" size="24.0" />
                                       </font>
                                    </Label>
                                    <Label styleClass="stats-label" text="GPA TRUNG BÌNH">
                                       <font>
                                          <Font name="System Bold" size="12.0" />
                                       </font>
                                    </Label>
                                 </children>
                              </VBox>
                           </children>
                        </HBox>
                     </children>
                  </VBox>
               </children>
            </HBox>
            
            <HBox alignment="CENTER_LEFT" spacing="20.0">
               <children>
                  <Label fx:id="filteredStudentsLabel" text="Hiển thị: 0 sinh viên" />
                  <Region HBox.hgrow="ALWAYS" />
                  <Button fx:id="exportBtn" onAction="#exportStudentList" styleClass="btn,btn-success" text="📊 Xuất danh sách" />
                  <Button fx:id="sendEmailBtn" onAction="#sendEmailToStudents" styleClass="btn,btn-primary" text="📧 Gửi email" />
               </children>
            </HBox>
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>
      </VBox>
   </center>
</BorderPane>
