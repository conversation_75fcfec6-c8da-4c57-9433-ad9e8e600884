<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.example.doancuoikyjava.controller.SimpleProfileController">
   <top>
      <VBox styleClass="header-section">
         <children>
            <HBox alignment="CENTER_LEFT" spacing="20.0" style="-fx-background-color: linear-gradient(to right, #667eea, #764ba2); -fx-padding: 20;">
               <children>
                  <Button fx:id="backButton" onAction="#goBack" text="← Quay lại" 
                          style="-fx-background-color: rgba(255,255,255,0.2); -fx-text-fill: white; -fx-border-color: white; -fx-border-radius: 5; -fx-background-radius: 5; -fx-font-weight: bold;" />
                  <Label fx:id="welcomeLabel" text="👤 Thông tin cá nhân" 
                         style="-fx-text-fill: white; -fx-font-size: 24px; -fx-font-weight: bold;" />
               </children>
            </HBox>
         </children>
      </VBox>
   </top>
   <center>
      <ScrollPane fitToWidth="true" style="-fx-background-color: linear-gradient(to bottom, #f5f7fa, #c3cfe2);">
         <content>
            <VBox spacing="20.0">
               <children>
                  <!-- Control Buttons -->
                  <HBox spacing="15.0" alignment="CENTER_LEFT" style="-fx-background-color: white; -fx-background-radius: 15; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2); -fx-padding: 20;">
                     <children>
                        <Button fx:id="editButton" onAction="#editProfile" text="✏️ Chỉnh sửa" 
                                style="-fx-background-color: linear-gradient(to bottom, #3498db, #2980b9); -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 8; -fx-padding: 10 20;" />
                        <Button fx:id="saveButton" onAction="#saveProfile" text="💾 Lưu" visible="false"
                                style="-fx-background-color: linear-gradient(to bottom, #27ae60, #229954); -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 8; -fx-padding: 10 20;" />
                        <Button fx:id="cancelButton" onAction="#cancelEdit" text="❌ Hủy" visible="false"
                                style="-fx-background-color: linear-gradient(to bottom, #e74c3c, #c0392b); -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 8; -fx-padding: 10 20;" />
                     </children>
                  </HBox>
                  
                  <!-- Personal Information -->
                  <VBox spacing="20.0" style="-fx-background-color: white; -fx-background-radius: 15; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);">
                     <children>
                        <Label text="📋 Thông tin cá nhân" style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" />
                        
                        <GridPane hgap="20.0" vgap="15.0">
                           <columnConstraints>
                              <ColumnConstraints hgrow="NEVER" minWidth="150.0" />
                              <ColumnConstraints hgrow="ALWAYS" />
                              <ColumnConstraints hgrow="NEVER" minWidth="150.0" />
                              <ColumnConstraints hgrow="ALWAYS" />
                           </columnConstraints>
                           <children>
                              <Label text="ID người dùng:" style="-fx-font-weight: bold; -fx-text-fill: #34495e;" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                              <TextField fx:id="userIdField" editable="false" style="-fx-background-color: #ecf0f1; -fx-border-color: #bdc3c7; -fx-border-radius: 5; -fx-background-radius: 5;" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                              
                              <Label text="Họ và tên:" style="-fx-font-weight: bold; -fx-text-fill: #34495e;" GridPane.columnIndex="2" GridPane.rowIndex="0" />
                              <TextField fx:id="fullNameField" style="-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-radius: 5; -fx-background-radius: 5;" GridPane.columnIndex="3" GridPane.rowIndex="0" />
                              
                              <Label text="Email:" style="-fx-font-weight: bold; -fx-text-fill: #34495e;" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                              <TextField fx:id="emailField" style="-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-radius: 5; -fx-background-radius: 5;" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                              
                              <Label text="Số điện thoại:" style="-fx-font-weight: bold; -fx-text-fill: #34495e;" GridPane.columnIndex="2" GridPane.rowIndex="1" />
                              <TextField fx:id="phoneField" style="-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-radius: 5; -fx-background-radius: 5;" GridPane.columnIndex="3" GridPane.rowIndex="1" />
                              
                              <Label text="Ngày sinh:" style="-fx-font-weight: bold; -fx-text-fill: #34495e;" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                              <DatePicker fx:id="birthDatePicker" style="-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-radius: 5; -fx-background-radius: 5;" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                              
                              <Label text="Địa chỉ:" style="-fx-font-weight: bold; -fx-text-fill: #34495e;" GridPane.columnIndex="2" GridPane.rowIndex="2" />
                              <TextArea fx:id="addressArea" prefRowCount="2" style="-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-radius: 5; -fx-background-radius: 5;" GridPane.columnIndex="3" GridPane.rowIndex="2" />
                           </children>
                        </GridPane>
                     </children>
                     <padding>
                        <Insets bottom="25.0" left="25.0" right="25.0" top="25.0" />
                     </padding>
                  </VBox>
                  
                  <!-- Student Information -->
                  <VBox fx:id="studentSection" spacing="20.0" style="-fx-background-color: white; -fx-background-radius: 15; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);">
                     <children>
                        <Label fx:id="studentSectionLabel" text="🎓 Thông tin sinh viên" style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" />
                        
                        <GridPane hgap="20.0" vgap="15.0">
                           <columnConstraints>
                              <ColumnConstraints hgrow="NEVER" minWidth="150.0" />
                              <ColumnConstraints hgrow="ALWAYS" />
                              <ColumnConstraints hgrow="NEVER" minWidth="150.0" />
                              <ColumnConstraints hgrow="ALWAYS" />
                           </columnConstraints>
                           <children>
                              <Label text="Lớp:" style="-fx-font-weight: bold; -fx-text-fill: #34495e;" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                              <TextField fx:id="classNameField" style="-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-radius: 5; -fx-background-radius: 5;" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                              
                              <Label text="Ngành:" style="-fx-font-weight: bold; -fx-text-fill: #34495e;" GridPane.columnIndex="2" GridPane.rowIndex="0" />
                              <TextField fx:id="majorField" style="-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-radius: 5; -fx-background-radius: 5;" GridPane.columnIndex="3" GridPane.rowIndex="0" />
                              
                              <Label text="GPA:" style="-fx-font-weight: bold; -fx-text-fill: #34495e;" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                              <TextField fx:id="gpaField" editable="false" style="-fx-background-color: #ecf0f1; -fx-border-color: #bdc3c7; -fx-border-radius: 5; -fx-background-radius: 5;" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                              
                              <Label text="Năm nhập học:" style="-fx-font-weight: bold; -fx-text-fill: #34495e;" GridPane.columnIndex="2" GridPane.rowIndex="1" />
                              <TextField fx:id="enrollmentYearField" editable="false" style="-fx-background-color: #ecf0f1; -fx-border-color: #bdc3c7; -fx-border-radius: 5; -fx-background-radius: 5;" GridPane.columnIndex="3" GridPane.rowIndex="1" />
                           </children>
                        </GridPane>
                     </children>
                     <padding>
                        <Insets bottom="25.0" left="25.0" right="25.0" top="25.0" />
                     </padding>
                  </VBox>
                  
                  <!-- Teacher Information -->
                  <VBox fx:id="teacherSection" spacing="20.0" style="-fx-background-color: white; -fx-background-radius: 15; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);">
                     <children>
                        <Label fx:id="teacherSectionLabel" text="👨‍🏫 Thông tin giáo viên" style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" />
                        
                        <GridPane hgap="20.0" vgap="15.0">
                           <columnConstraints>
                              <ColumnConstraints hgrow="NEVER" minWidth="150.0" />
                              <ColumnConstraints hgrow="ALWAYS" />
                              <ColumnConstraints hgrow="NEVER" minWidth="150.0" />
                              <ColumnConstraints hgrow="ALWAYS" />
                           </columnConstraints>
                           <children>
                              <Label text="Khoa:" style="-fx-font-weight: bold; -fx-text-fill: #34495e;" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                              <TextField fx:id="departmentField" style="-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-radius: 5; -fx-background-radius: 5;" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                              
                              <Label text="Chức vụ:" style="-fx-font-weight: bold; -fx-text-fill: #34495e;" GridPane.columnIndex="2" GridPane.rowIndex="0" />
                              <TextField fx:id="positionField" style="-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-radius: 5; -fx-background-radius: 5;" GridPane.columnIndex="3" GridPane.rowIndex="0" />
                              
                              <Label text="Kinh nghiệm (năm):" style="-fx-font-weight: bold; -fx-text-fill: #34495e;" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                              <TextField fx:id="experienceField" style="-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-radius: 5; -fx-background-radius: 5;" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                              
                              <Label text="Lương:" style="-fx-font-weight: bold; -fx-text-fill: #34495e;" GridPane.columnIndex="2" GridPane.rowIndex="1" />
                              <TextField fx:id="salaryField" editable="false" style="-fx-background-color: #ecf0f1; -fx-border-color: #bdc3c7; -fx-border-radius: 5; -fx-background-radius: 5;" GridPane.columnIndex="3" GridPane.rowIndex="1" />
                           </children>
                        </GridPane>
                     </children>
                     <padding>
                        <Insets bottom="25.0" left="25.0" right="25.0" top="25.0" />
                     </padding>
                  </VBox>
               </children>
               <padding>
                  <Insets bottom="25.0" left="25.0" right="25.0" top="25.0" />
               </padding>
            </VBox>
         </content>
      </ScrollPane>
   </center>
</BorderPane>
