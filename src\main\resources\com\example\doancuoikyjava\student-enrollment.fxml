<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.example.doancuoikyjava.controller.StudentEnrollmentController">
   <top>
      <HBox alignment="CENTER_LEFT" styleClass="nav-bar" spacing="20.0">
         <children>
            <Button fx:id="backButton" onAction="#goBack" styleClass="nav-button" text="← Quay lại" />
            <Label styleClass="nav-button" text="📋 ĐĂNG KÝ MÔN HỌC">
               <font>
                  <Font name="System Bold" size="16.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Label fx:id="welcomeLabel" styleClass="nav-button" text="Xin chào, Sinh viên">
               <font>
                  <Font size="14.0" />
               </font>
            </Label>
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </HBox>
   </top>
   <center>
      <VBox styleClass="content-container" spacing="20.0">
         <children>
            <HBox alignment="CENTER_LEFT" spacing="20.0">
               <children>
                  <Label styleClass="label-title" text="Đăng ký môn học">
                     <font>
                        <Font name="System Bold" size="24.0" />
                     </font>
                  </Label>
                  <Region HBox.hgrow="ALWAYS" />
                  <Button fx:id="refreshBtn" onAction="#refreshData" styleClass="btn,btn-secondary" text="🔄 Làm mới" />
               </children>
            </HBox>
            
            <!-- Thông tin sinh viên -->
            <VBox styleClass="card" spacing="15.0">
               <children>
                  <Label styleClass="card-header" text="Thông tin sinh viên">
                     <font>
                        <Font name="System Bold" size="18.0" />
                     </font>
                  </Label>
                  <HBox spacing="20.0">
                     <children>
                        <VBox spacing="5.0">
                           <children>
                              <Label text="MSSV:" />
                              <Label fx:id="studentIdLabel" styleClass="label-info" text="STU001" />
                           </children>
                        </VBox>
                        <VBox spacing="5.0">
                           <children>
                              <Label text="Họ tên:" />
                              <Label fx:id="studentNameLabel" styleClass="label-info" text="Nguyễn Văn A" />
                           </children>
                        </VBox>
                        <VBox spacing="5.0">
                           <children>
                              <Label text="Lớp:" />
                              <Label fx:id="classNameLabel" styleClass="label-info" text="CNTT01" />
                           </children>
                        </VBox>
                        <VBox spacing="5.0">
                           <children>
                              <Label text="Tín chỉ đã đăng ký:" />
                              <Label fx:id="registeredCreditsLabel" styleClass="label-info" text="0" />
                           </children>
                        </VBox>
                        <VBox spacing="5.0">
                           <children>
                              <Label text="Tín chỉ tối đa:" />
                              <Label fx:id="maxCreditsLabel" styleClass="label-info" text="24" />
                           </children>
                        </VBox>
                     </children>
                  </HBox>
               </children>
            </VBox>
            
            <!-- Bộ lọc -->
            <HBox spacing="15.0">
               <children>
                  <ComboBox fx:id="departmentFilterComboBox" promptText="Lọc theo khoa" />
                  <ComboBox fx:id="creditsFilterComboBox" promptText="Lọc theo tín chỉ" />
                  <ComboBox fx:id="timeFilterComboBox" promptText="Lọc theo thời gian" />
                  <TextField fx:id="searchField" promptText="Tìm kiếm môn học..." HBox.hgrow="ALWAYS" />
                  <Button fx:id="searchBtn" onAction="#searchCourses" styleClass="btn,btn-warning" text="Tìm kiếm" />
               </children>
            </HBox>
            
            <!-- Danh sách môn học có thể đăng ký -->
            <VBox styleClass="card" spacing="15.0">
               <children>
                  <Label styleClass="card-header" text="Danh sách môn học có thể đăng ký">
                     <font>
                        <Font name="System Bold" size="18.0" />
                     </font>
                  </Label>
                  <TableView fx:id="availableCoursesTableView" prefHeight="250.0">
                     <columns>
                        <TableColumn fx:id="courseIdColumn" prefWidth="80.0" text="Mã MH" />
                        <TableColumn fx:id="courseNameColumn" prefWidth="200.0" text="Tên môn học" />
                        <TableColumn fx:id="creditsColumn" prefWidth="60.0" text="Tín chỉ" />
                        <TableColumn fx:id="teacherColumn" prefWidth="120.0" text="Giáo viên" />
                        <TableColumn fx:id="dayColumn" prefWidth="80.0" text="Ngày học" />
                        <TableColumn fx:id="scheduleColumn" prefWidth="120.0" text="Giờ học" />
                        <TableColumn fx:id="classroomColumn" prefWidth="80.0" text="Phòng" />
                        <TableColumn fx:id="enrolledColumn" prefWidth="80.0" text="Đã ĐK" />
                        <TableColumn fx:id="maxStudentsColumn" prefWidth="80.0" text="Sĩ số" />
                        <TableColumn fx:id="actionsColumn" prefWidth="100.0" text="Thao tác" />
                     </columns>
                  </TableView>
               </children>
            </VBox>
            
            <!-- Môn học đã đăng ký -->
            <VBox styleClass="card" spacing="15.0">
               <children>
                  <Label styleClass="card-header" text="Môn học đã đăng ký">
                     <font>
                        <Font name="System Bold" size="18.0" />
                     </font>
                  </Label>
                  <TableView fx:id="enrolledCoursesTableView" prefHeight="200.0">
                     <columns>
                        <TableColumn fx:id="enrolledCourseIdColumn" prefWidth="80.0" text="Mã MH" />
                        <TableColumn fx:id="enrolledCourseNameColumn" prefWidth="180.0" text="Tên môn học" />
                        <TableColumn fx:id="enrolledCreditsColumn" prefWidth="60.0" text="Tín chỉ" />
                        <TableColumn fx:id="enrolledTeacherColumn" prefWidth="120.0" text="Giáo viên" />
                        <TableColumn fx:id="enrolledDayColumn" prefWidth="80.0" text="Ngày học" />
                        <TableColumn fx:id="enrolledScheduleColumn" prefWidth="120.0" text="Giờ học" />
                        <TableColumn fx:id="enrolledClassroomColumn" prefWidth="80.0" text="Phòng" />
                        <TableColumn fx:id="enrollmentDateColumn" prefWidth="100.0" text="Ngày ĐK" />
                        <TableColumn fx:id="enrolledActionsColumn" prefWidth="100.0" text="Thao tác" />
                     </columns>
                  </TableView>
               </children>
            </VBox>
            
            <HBox alignment="CENTER_LEFT" spacing="20.0">
               <children>
                  <Label fx:id="totalCoursesLabel" text="Tổng môn học có thể đăng ký: 0" />
                  <Label fx:id="enrolledCoursesCountLabel" text="Đã đăng ký: 0 môn" />
                  <Region HBox.hgrow="ALWAYS" />
                  <Button fx:id="exportBtn" onAction="#exportEnrollment" styleClass="btn,btn-success" text="📊 Xuất danh sách" />
               </children>
            </HBox>
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>
      </VBox>
   </center>
</BorderPane>
