// IMPORTANT: If you see "JavaFX runtime components are missing", you must add VM options:
// --module-path "path\to\javafx-sdk-XX\lib" --add-modules javafx.controls,javafx.fxml
// Replace "path\to\javafx-sdk-XX\lib" with the path to your JavaFX SDK lib folder.
// In IntelliJ: Run > Edit Configurations > VM options
// In command line: java --module-path ... --add-modules ...
package com.example.doancuoikyjava;

import com.example.doancuoikyjava.model.Student;
import com.example.doancuoikyjava.model.User;
import com.example.doancuoikyjava.service.UserService;
import com.example.doancuoikyjava.util.SceneManager;
import javafx.application.Application;
import javafx.stage.Stage;

import java.time.LocalDate;

/**
 * Student Launcher - Direct access to Student Dashboard
 */
public class StudentLauncher extends Application {

    private UserService userService;

    @Override
    public void start(Stage primaryStage) {
        try {
            this.userService = new UserService();
            
            // Initialize default student if not exists
            initializeDefaultStudent();
            
            // Get student user
            User student = userService.getUserById("SV001");
            if (student == null) {
                System.err.println("❌ Could not find or create student user!");
                return;
            }

            // Set up SceneManager
            SceneManager.setPrimaryStage(primaryStage);
            SceneManager.setCurrentUser(student);
            
            // Launch student dashboard directly
            SceneManager.switchScene("/com/example/doancuoikyjava/student-dashboard.fxml", 
                                   "🎓 STUDENT DASHBOARD - Hệ thống quản lý học tập");
            
            System.out.println("🎓 Student Dashboard launched successfully!");
            System.out.println("👤 Logged in as: " + student.getFullName());
            
        } catch (Exception e) {
            System.err.println("❌ Error launching Student Dashboard: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void initializeDefaultStudent() {
        try {
            if (userService.getUserById("SV001") == null) {
                Student student = new Student();
                student.setUserId("SV001");
                student.setUsername("student");
                student.setPassword("student123");
                student.setFullName("Nguyễn Văn Học");
                student.setEmail("<EMAIL>");
                student.setPhone("0123987654");
                student.setRole(User.UserRole.STUDENT);
                student.setStudentId("SV001");
                student.setMajor("Khoa học máy tính");
                student.setYear(2024);
                student.setDateOfBirth(LocalDate.of(2002, 5, 15));
                student.setAddress("123 Đường ABC, Quận 1, TP.HCM");
                student.setGpa(3.5);
                
                userService.addUser(student);
                System.out.println("✅ Created default student user: student/student123");
            }
        } catch (Exception e) {
            System.err.println("❌ Error creating default student: " + e.getMessage());
        }
    }

    public static void main(String[] args) {
        // NOTE: If you use Java 11+ and JavaFX SDK, add VM options:
        // --module-path "path\to\javafx-sdk-XX\lib" --add-modules javafx.controls,javafx.fxml
        // Replace "path\to\javafx-sdk-XX\lib" with your JavaFX SDK lib folder.
        // Example for IntelliJ IDEA: Run/Debug Configurations > VM options
        System.out.println("🎓 Starting Student Dashboard");
        System.out.println("════════════════════════════════════════");
        System.out.println("👤 Auto-login as Student");
        System.out.println("🎯 Direct access to learning functions");
        System.out.println("════════════════════════════════════════");
        
        launch(args);
    }
}