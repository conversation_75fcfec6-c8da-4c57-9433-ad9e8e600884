package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.Teacher;
import com.example.doancuoikyjava.model.User;
import com.example.doancuoikyjava.service.UserService;
import com.example.doancuoikyjava.util.SceneManager;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;

import java.io.IOException;
import java.net.URL;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

public class TeacherManagementController implements Initializable {
    
    @FXML private Label welcomeLabel;
    @FXML private Button backButton;
    @FXML private Button addTeacherBtn;
    @FXML private Button refreshBtn;
    @FXML private Button searchBtn;
    @FXML private Button exportBtn;
    @FXML private Button deleteSelectedBtn;
    
    @FXML private TextField searchField;
    @FXML private ComboBox<String> departmentFilterComboBox;
    @FXML private ComboBox<String> positionFilterComboBox;
    
    @FXML private TableView<Teacher> teachersTableView;
    @FXML private TableColumn<Teacher, String> teacherIdColumn;
    @FXML private TableColumn<Teacher, String> fullNameColumn;
    @FXML private TableColumn<Teacher, String> departmentColumn;
    @FXML private TableColumn<Teacher, String> positionColumn;
    @FXML private TableColumn<Teacher, String> qualificationColumn;

    @FXML private TableColumn<Teacher, String> teachingCoursesColumn;
    @FXML private TableColumn<Teacher, String> emailColumn;
    @FXML private TableColumn<Teacher, String> phoneColumn;
    @FXML private TableColumn<Teacher, Void> actionsColumn;
    
    @FXML private Label totalTeachersLabel;
    
    private UserService userService;
    private ObservableList<Teacher> allTeachers;
    private ObservableList<Teacher> filteredTeachers;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        userService = new UserService();
        allTeachers = FXCollections.observableArrayList();
        filteredTeachers = FXCollections.observableArrayList();
        
        setupWelcomeMessage();
        setupTableColumns();
        setupFilters();
        loadTeachers();
        
        // Enable multiple selection
        teachersTableView.getSelectionModel().setSelectionMode(SelectionMode.MULTIPLE);
    }
    
    private void setupWelcomeMessage() {
        User currentUser = SceneManager.getCurrentUser();
        if (currentUser != null) {
            welcomeLabel.setText("Xin chào, " + currentUser.getFullName());
        }
    }
    
    private void setupTableColumns() {
        teacherIdColumn.setCellValueFactory(new PropertyValueFactory<>("teacherId"));
        fullNameColumn.setCellValueFactory(new PropertyValueFactory<>("fullName"));
        departmentColumn.setCellValueFactory(new PropertyValueFactory<>("department"));
        positionColumn.setCellValueFactory(new PropertyValueFactory<>("position"));
        qualificationColumn.setCellValueFactory(new PropertyValueFactory<>("qualification"));
        emailColumn.setCellValueFactory(new PropertyValueFactory<>("email"));
        phoneColumn.setCellValueFactory(new PropertyValueFactory<>("phone"));
        


        // Teaching courses column with formatting
        teachingCoursesColumn.setCellValueFactory(cellData -> {
            List<String> courses = cellData.getValue().getTeachingCourses();
            if (courses != null && !courses.isEmpty()) {
                return new SimpleStringProperty(String.join(", ", courses));
            } else {
                return new SimpleStringProperty("Chưa có");
            }
        });
        
        // Actions column with buttons
        actionsColumn.setCellFactory(param -> new TableCell<Teacher, Void>() {
            private final Button editBtn = new Button("Sửa");
            private final Button deleteBtn = new Button("Xóa");
            private final HBox buttons = new HBox(5, editBtn, deleteBtn);
            
            {
                editBtn.setStyle("-fx-background-color: #2196F3; -fx-text-fill: white; -fx-font-size: 10px;");
                deleteBtn.setStyle("-fx-background-color: #F44336; -fx-text-fill: white; -fx-font-size: 10px;");
                
                editBtn.setOnAction(e -> {
                    Teacher teacher = getTableView().getItems().get(getIndex());
                    showEditTeacherDialog(teacher);
                });
                
                deleteBtn.setOnAction(e -> {
                    Teacher teacher = getTableView().getItems().get(getIndex());
                    deleteTeacher(teacher);
                });
            }
            
            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(buttons);
                }
            }
        });
        
        teachersTableView.setItems(filteredTeachers);
    }
    
    private void setupFilters() {
        // Setup search functionality
        searchField.textProperty().addListener((obs, oldText, newText) -> filterTeachers());
        departmentFilterComboBox.valueProperty().addListener((obs, oldValue, newValue) -> filterTeachers());
        positionFilterComboBox.valueProperty().addListener((obs, oldValue, newValue) -> filterTeachers());
    }
    
    private void loadTeachers() {
        List<User> users = userService.getUsersByRole(User.UserRole.TEACHER);
        allTeachers.clear();
        
        for (User user : users) {
            if (user instanceof Teacher) {
                allTeachers.add((Teacher) user);
            }
        }
        
        updateFilters();
        filterTeachers();
        updateTotalLabel();
    }
    
    private void updateFilters() {
        // Update department filter
        List<String> departments = allTeachers.stream()
                .map(Teacher::getDepartment)
                .distinct()
                .sorted()
                .collect(Collectors.toList());
        departmentFilterComboBox.setItems(FXCollections.observableArrayList(departments));
        
        // Update position filter
        List<String> positions = allTeachers.stream()
                .map(Teacher::getPosition)
                .distinct()
                .sorted()
                .collect(Collectors.toList());
        positionFilterComboBox.setItems(FXCollections.observableArrayList(positions));
    }
    
    private void filterTeachers() {
        String searchText = searchField.getText().toLowerCase();
        String selectedDepartment = departmentFilterComboBox.getValue();
        String selectedPosition = positionFilterComboBox.getValue();
        
        List<Teacher> filtered = allTeachers.stream()
                .filter(teacher -> {
                    boolean matchesSearch = searchText.isEmpty() || 
                            teacher.getFullName().toLowerCase().contains(searchText) ||
                            teacher.getTeacherId().toLowerCase().contains(searchText) ||
                            teacher.getEmail().toLowerCase().contains(searchText);
                    
                    boolean matchesDepartment = selectedDepartment == null || 
                            teacher.getDepartment().equals(selectedDepartment);
                    
                    boolean matchesPosition = selectedPosition == null || 
                            teacher.getPosition().equals(selectedPosition);
                    
                    return matchesSearch && matchesDepartment && matchesPosition;
                })
                .collect(Collectors.toList());
        
        filteredTeachers.setAll(filtered);
        updateTotalLabel();
    }
    
    private void updateTotalLabel() {
        totalTeachersLabel.setText("Tổng số giáo viên: " + filteredTeachers.size() + "/" + allTeachers.size());
    }
    
    @FXML
    private void goBack() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/admin-dashboard.fxml", 
                                   "Quản trị viên - " + SceneManager.getCurrentUser().getFullName());
        } catch (IOException e) {
            showAlert("Lỗi", "Không thể quay lại trang chính: " + e.getMessage());
        }
    }
    
    @FXML
    private void showAddTeacherDialog() {
        Dialog<Teacher> dialog = createTeacherDialog(null);
        Optional<Teacher> result = dialog.showAndWait();
        
        result.ifPresent(teacher -> {
            if (userService.addUser(teacher)) {
                showAlert("Thành công", "Thêm giáo viên thành công!");
                loadTeachers();
            } else {
                showAlert("Lỗi", "Không thể thêm giáo viên. Tên đăng nhập có thể đã tồn tại.");
            }
        });
    }
    
    private void showEditTeacherDialog(Teacher teacher) {
        Dialog<Teacher> dialog = createTeacherDialog(teacher);
        Optional<Teacher> result = dialog.showAndWait();
        
        result.ifPresent(updatedTeacher -> {
            if (userService.updateUser(updatedTeacher)) {
                showAlert("Thành công", "Cập nhật giáo viên thành công!");
                loadTeachers();
            } else {
                showAlert("Lỗi", "Không thể cập nhật giáo viên.");
            }
        });
    }
    
    private Dialog<Teacher> createTeacherDialog(Teacher existingTeacher) {
        Dialog<Teacher> dialog = new Dialog<>();
        dialog.setTitle(existingTeacher == null ? "Thêm giáo viên mới" : "Sửa thông tin giáo viên");
        dialog.setHeaderText(null);

        // Create custom buttons with enhanced styling
        ButtonType saveButtonType = new ButtonType("💾 Lưu", ButtonBar.ButtonData.OK_DONE);
        ButtonType cancelButtonType = new ButtonType("❌ Hủy", ButtonBar.ButtonData.CANCEL_CLOSE);
        dialog.getDialogPane().getButtonTypes().addAll(saveButtonType, cancelButtonType);

        // Create main container with compact sizing (removed experience field)
        VBox mainContainer = new VBox();
        mainContainer.setSpacing(15);
        mainContainer.setPadding(new Insets(20, 30, 15, 30));
        mainContainer.setPrefWidth(650);
        mainContainer.setPrefHeight(620);
        mainContainer.setStyle(
            "-fx-background-color: #FAFAFA; " +
            "-fx-background-radius: 10px;"
        );

        // Create compact header section
        HBox headerSection = new HBox();
        headerSection.setSpacing(10);
        headerSection.setAlignment(javafx.geometry.Pos.CENTER_LEFT);
        headerSection.setPadding(new Insets(12, 15, 12, 15));
        headerSection.setStyle(
            "-fx-background-color: linear-gradient(to right, #4CAF50, #45A049); " +
            "-fx-background-radius: 8px; " +
            "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 6, 0, 0, 2);"
        );

        Label headerIcon = new Label("👨‍🏫");
        headerIcon.setStyle("-fx-font-size: 18px;");

        Label headerTitle = new Label(existingTeacher == null ? "THÊM GIÁO VIÊN MỚI" : "SỬA THÔNG TIN GIÁO VIÊN");
        headerTitle.setStyle(
            "-fx-font-size: 14px; " +
            "-fx-font-weight: bold; " +
            "-fx-text-fill: white;"
        );

        headerSection.getChildren().addAll(headerIcon, headerTitle);

        // Create form container with compact sections
        VBox formContainer = new VBox();
        formContainer.setSpacing(12);
        
        // Create enhanced form fields
        TextField usernameField = createEnhancedTextField("Nhập tên đăng nhập...");
        TextField passwordField = createEnhancedTextField("Nhập mật khẩu...");
        TextField fullNameField = createEnhancedTextField("Nhập họ và tên...");
        TextField emailField = createEnhancedTextField("Nhập email...");
        TextField phoneField = createEnhancedTextField("Nhập số điện thoại...");
        TextField addressField = createEnhancedTextField("Nhập địa chỉ...");
        ComboBox<String> departmentComboBox = createEnhancedComboBox();
        setupDepartmentComboBox(departmentComboBox);
        TextField positionField = createEnhancedTextField("Nhập chức vụ...");
        TextField salaryField = createEnhancedTextField("Nhập mức lương...");
        TextField qualificationField = createEnhancedTextField("Nhập trình độ học vấn...");

        ComboBox<String> teachingCoursesComboBox = createEnhancedComboBox();
        setupTeachingCoursesComboBox(teachingCoursesComboBox);
        DatePicker dobPicker = createEnhancedDatePicker();

        dobPicker.setPromptText("Chọn ngày sinh...");

        // Create larger form grid for course list
        GridPane formGrid = new GridPane();
        formGrid.setHgap(25);
        formGrid.setVgap(18);
        formGrid.setPadding(new Insets(25));
        formGrid.setPrefWidth(590);
        formGrid.setStyle(
            "-fx-background-color: white; " +
            "-fx-background-radius: 8px; " +
            "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 4, 0, 0, 2);"
        );

        // Add form fields in a compact 2-column layout
        int row = 0;
        formGrid.add(createCompactLabel("Tên đăng nhập *"), 0, row);
        formGrid.add(usernameField, 1, row++);

        formGrid.add(createCompactLabel("Mật khẩu *"), 0, row);
        formGrid.add(passwordField, 1, row++);

        formGrid.add(createCompactLabel("Họ và tên *"), 0, row);
        formGrid.add(fullNameField, 1, row++);

        formGrid.add(createCompactLabel("Email"), 0, row);
        formGrid.add(emailField, 1, row++);

        formGrid.add(createCompactLabel("Điện thoại"), 0, row);
        formGrid.add(phoneField, 1, row++);

        formGrid.add(createCompactLabel("Địa chỉ"), 0, row);
        formGrid.add(addressField, 1, row++);

        formGrid.add(createCompactLabel("Khoa/Phòng ban"), 0, row);
        formGrid.add(departmentComboBox, 1, row++);

        formGrid.add(createCompactLabel("Chức vụ"), 0, row);
        formGrid.add(positionField, 1, row++);

        formGrid.add(createCompactLabel("Lương"), 0, row);
        formGrid.add(salaryField, 1, row++);

        formGrid.add(createCompactLabel("Trình độ"), 0, row);
        formGrid.add(qualificationField, 1, row++);

        formGrid.add(createCompactLabel("Môn giảng dạy"), 0, row);
        formGrid.add(teachingCoursesComboBox, 1, row++);

        formGrid.add(createCompactLabel("Ngày sinh"), 0, row);
        formGrid.add(dobPicker, 1, row);

        // Load existing data if editing
        if (existingTeacher != null) {
            usernameField.setText(existingTeacher.getUsername());
            passwordField.setText(existingTeacher.getPassword());
            fullNameField.setText(existingTeacher.getFullName());
            emailField.setText(existingTeacher.getEmail());
            phoneField.setText(existingTeacher.getPhone());
            addressField.setText(existingTeacher.getAddress());
            departmentComboBox.setValue(existingTeacher.getDepartment());
            positionField.setText(existingTeacher.getPosition());
            salaryField.setText(String.valueOf(existingTeacher.getSalary()));
            qualificationField.setText(existingTeacher.getQualification());


            // Load teaching courses - set first course as selected for display
            if (existingTeacher.getTeachingCourses() != null && !existingTeacher.getTeachingCourses().isEmpty()) {
                teachingCoursesComboBox.setValue(existingTeacher.getTeachingCourses().get(0));
            }

            dobPicker.setValue(existingTeacher.getDateOfBirth());
        }
        
        // Add form to container
        formContainer.getChildren().add(formGrid);

        // Add all components to main container
        mainContainer.getChildren().addAll(headerSection, formContainer);

        // Create scroll pane to ensure content fits
        ScrollPane scrollPane = new ScrollPane();
        scrollPane.setContent(mainContainer);
        scrollPane.setFitToWidth(true);
        scrollPane.setFitToHeight(true);
        scrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);
        scrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        scrollPane.setStyle("-fx-background-color: transparent;");

        // Set the content
        dialog.getDialogPane().setContent(scrollPane);

        // Set dialog size to ensure buttons are visible (compact without experience field)
        dialog.getDialogPane().setPrefSize(700, 690);
        dialog.getDialogPane().setMinSize(700, 690);
        dialog.getDialogPane().setMaxSize(700, 690);
        dialog.setResizable(false);

        // Apply enhanced styling to dialog
        dialog.getDialogPane().getStylesheets().add(
            getClass().getResource("/com/example/doancuoikyjava/enhanced-styles.css").toExternalForm()
        );
        dialog.getDialogPane().getStyleClass().add("enhanced-dialog");

        // Style the buttons with better visibility
        javafx.application.Platform.runLater(() -> {
            Button saveBtn = (Button) dialog.getDialogPane().lookupButton(saveButtonType);
            Button cancelBtn = (Button) dialog.getDialogPane().lookupButton(cancelButtonType);

            if (saveBtn != null) {
                saveBtn.setText("💾 Lưu");
                saveBtn.setStyle(
                    "-fx-background-color: #4CAF50; " +
                    "-fx-text-fill: white; " +
                    "-fx-font-weight: bold; " +
                    "-fx-font-size: 14px; " +
                    "-fx-background-radius: 8px; " +
                    "-fx-padding: 12px 25px; " +
                    "-fx-cursor: hand; " +
                    "-fx-min-width: 100px; " +
                    "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 4, 0, 0, 2);"
                );
            }

            if (cancelBtn != null) {
                cancelBtn.setText("❌ Hủy");
                cancelBtn.setStyle(
                    "-fx-background-color: #F44336; " +
                    "-fx-text-fill: white; " +
                    "-fx-font-weight: bold; " +
                    "-fx-font-size: 14px; " +
                    "-fx-background-radius: 8px; " +
                    "-fx-padding: 12px 25px; " +
                    "-fx-cursor: hand; " +
                    "-fx-min-width: 100px; " +
                    "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 4, 0, 0, 2);"
                );
            }
        });
        
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == saveButtonType) {
                try {
                    Teacher teacher = existingTeacher != null ? existingTeacher : new Teacher();
                    
                    if (existingTeacher == null) {
                        String teacherId = userService.generateNextUserId(User.UserRole.TEACHER);
                        teacher.setUserId(teacherId);
                        teacher.setTeacherId(teacherId);
                    }
                    
                    teacher.setUsername(usernameField.getText());
                    teacher.setPassword(passwordField.getText());
                    teacher.setFullName(fullNameField.getText());
                    teacher.setEmail(emailField.getText());
                    teacher.setPhone(phoneField.getText());
                    teacher.setAddress(addressField.getText());
                    teacher.setDepartment(departmentComboBox.getValue());
                    teacher.setPosition(positionField.getText());
                    teacher.setSalary(Double.parseDouble(salaryField.getText()));
                    teacher.setQualification(qualificationField.getText());
                    teacher.setExperienceYears(0); // Set default value

                    // Get selected teaching course
                    List<String> teachingCourses = new ArrayList<>();
                    if (teachingCoursesComboBox.getValue() != null) {
                        teachingCourses.add(teachingCoursesComboBox.getValue());
                    }
                    teacher.setTeachingCourses(teachingCourses);

                    teacher.setDateOfBirth(dobPicker.getValue());
                    
                    return teacher;
                } catch (Exception e) {
                    showAlert("Lỗi", "Dữ liệu không hợp lệ: " + e.getMessage());
                    return null;
                }
            }
            return null;
        });
        
        return dialog;
    }
    
    private void deleteTeacher(Teacher teacher) {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Xác nhận xóa");
        alert.setHeaderText("Bạn có chắc chắn muốn xóa giáo viên này?");
        alert.setContentText("Giáo viên: " + teacher.getFullName() + " (" + teacher.getTeacherId() + ")\nUser ID: " + teacher.getUserId());

        Optional<ButtonType> result = alert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            System.out.println("🔍 Controller: Bắt đầu xóa giáo viên - " + teacher.getFullName() + " (ID: " + teacher.getUserId() + ")");

            try {
                boolean deleteResult = userService.deleteUser(teacher.getUserId());
                System.out.println("📊 Controller: Kết quả xóa từ service: " + deleteResult);

                if (deleteResult) {
                    showAlert("Thành công", "Xóa giáo viên thành công!\nGiáo viên: " + teacher.getFullName());
                    loadTeachers();
                } else {
                    showAlert("Lỗi", "Không thể xóa giáo viên.\nVui lòng kiểm tra console để xem chi tiết lỗi.\n\nGiáo viên: " + teacher.getFullName() + "\nUser ID: " + teacher.getUserId());
                }
            } catch (Exception e) {
                System.err.println("❌ Controller: Exception khi xóa giáo viên: " + e.getMessage());
                e.printStackTrace();
                showAlert("Lỗi", "Có lỗi xảy ra khi xóa giáo viên:\n" + e.getMessage());
            }
        }
    }
    
    @FXML
    private void deleteSelectedTeachers() {
        List<Teacher> selectedTeachers = teachersTableView.getSelectionModel().getSelectedItems();
        
        if (selectedTeachers.isEmpty()) {
            showAlert("Thông báo", "Vui lòng chọn giáo viên cần xóa.");
            return;
        }
        
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Xác nhận xóa");
        alert.setHeaderText("Bạn có chắc chắn muốn xóa " + selectedTeachers.size() + " giáo viên đã chọn?");
        
        Optional<ButtonType> result = alert.showAndWait();
        if (result.get() == ButtonType.OK) {
            int deletedCount = 0;
            for (Teacher teacher : selectedTeachers) {
                if (userService.deleteUser(teacher.getUserId())) {
                    deletedCount++;
                }
            }
            
            showAlert("Thành công", "Đã xóa " + deletedCount + "/" + selectedTeachers.size() + " giáo viên.");
            loadTeachers();
        }
    }
    
    @FXML
    private void refreshData() {
        loadTeachers();
        showAlert("Thành công", "Dữ liệu đã được làm mới!");
    }
    
    @FXML
    private void searchTeachers() {
        filterTeachers();
    }
    
    @FXML
    private void exportData() {
        showAlert("Thông báo", "Tính năng xuất Excel đang được phát triển!");
    }
    
    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    // Helper methods for enhanced UI components
    private TextField createEnhancedTextField(String promptText) {
        TextField textField = new TextField();
        textField.setPromptText(promptText);
        textField.setPrefWidth(400);
        textField.setStyle(
            "-fx-background-radius: 6px; " +
            "-fx-border-radius: 6px; " +
            "-fx-border-color: #E0E0E0; " +
            "-fx-border-width: 1px; " +
            "-fx-padding: 12px 15px; " +
            "-fx-font-size: 14px; " +
            "-fx-background-color: white; " +
            "-fx-text-fill: #212121; " +
            "-fx-prompt-text-fill: #757575; " +
            "-fx-pref-height: 40px;"
        );

        // Add focus effects
        textField.focusedProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal) {
                textField.setStyle(
                    "-fx-background-radius: 6px; " +
                    "-fx-border-radius: 6px; " +
                    "-fx-border-color: #4CAF50; " +
                    "-fx-border-width: 1px; " +
                    "-fx-padding: 12px 15px; " +
                    "-fx-font-size: 14px; " +
                    "-fx-background-color: white; " +
                    "-fx-text-fill: #212121; " +
                    "-fx-prompt-text-fill: #757575; " +
                    "-fx-pref-height: 40px; " +
                    "-fx-pref-width: 400px; " +
                    "-fx-effect: dropshadow(gaussian, rgba(76, 175, 80, 0.2), 3, 0, 0, 0);"
                );
            } else {
                textField.setStyle(
                    "-fx-background-radius: 6px; " +
                    "-fx-border-radius: 6px; " +
                    "-fx-border-color: #E0E0E0; " +
                    "-fx-border-width: 1px; " +
                    "-fx-padding: 12px 15px; " +
                    "-fx-font-size: 14px; " +
                    "-fx-background-color: white; " +
                    "-fx-text-fill: #212121; " +
                    "-fx-prompt-text-fill: #757575; " +
                    "-fx-pref-height: 40px; " +
                    "-fx-pref-width: 400px;"
                );
            }
        });

        return textField;
    }

    private Label createCompactLabel(String text) {
        Label label = new Label(text);
        label.setStyle(
            "-fx-font-size: 15px; " +
            "-fx-font-weight: bold; " +
            "-fx-text-fill: #424242; " +
            "-fx-min-width: 150px; " +
            "-fx-pref-width: 150px;"
        );
        // Ensure text is visible
        label.setTextFill(javafx.scene.paint.Color.web("#424242"));
        return label;
    }

    private TextArea createEnhancedTextArea(String promptText) {
        TextArea textArea = new TextArea();
        textArea.setPromptText(promptText);
        textArea.setPrefRowCount(2);
        textArea.setPrefWidth(400);
        textArea.setWrapText(true);
        textArea.setStyle(
            "-fx-background-radius: 6px; " +
            "-fx-border-radius: 6px; " +
            "-fx-border-color: #E0E0E0; " +
            "-fx-border-width: 1px; " +
            "-fx-padding: 12px 15px; " +
            "-fx-font-size: 14px; " +
            "-fx-background-color: white; " +
            "-fx-text-fill: #212121; " +
            "-fx-prompt-text-fill: #757575; " +
            "-fx-pref-height: 80px;"
        );

        // Add focus effects
        textArea.focusedProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal) {
                textArea.setStyle(
                    "-fx-background-radius: 6px; " +
                    "-fx-border-radius: 6px; " +
                    "-fx-border-color: #4CAF50; " +
                    "-fx-border-width: 1px; " +
                    "-fx-padding: 12px 15px; " +
                    "-fx-font-size: 14px; " +
                    "-fx-background-color: white; " +
                    "-fx-text-fill: #212121; " +
                    "-fx-prompt-text-fill: #757575; " +
                    "-fx-pref-height: 80px; " +
                    "-fx-effect: dropshadow(gaussian, rgba(76, 175, 80, 0.2), 3, 0, 0, 0);"
                );
            } else {
                textArea.setStyle(
                    "-fx-background-radius: 6px; " +
                    "-fx-border-radius: 6px; " +
                    "-fx-border-color: #E0E0E0; " +
                    "-fx-border-width: 1px; " +
                    "-fx-padding: 12px 15px; " +
                    "-fx-font-size: 14px; " +
                    "-fx-background-color: white; " +
                    "-fx-text-fill: #212121; " +
                    "-fx-prompt-text-fill: #757575; " +
                    "-fx-pref-height: 80px;"
                );
            }
        });

        return textArea;
    }

    private DatePicker createEnhancedDatePicker() {
        DatePicker datePicker = new DatePicker();
        datePicker.setPrefWidth(400);
        datePicker.setStyle(
            "-fx-background-radius: 6px; " +
            "-fx-border-radius: 6px; " +
            "-fx-border-color: #E0E0E0; " +
            "-fx-border-width: 1px; " +
            "-fx-padding: 10px 15px; " +
            "-fx-font-size: 14px; " +
            "-fx-background-color: white; " +
            "-fx-text-fill: #212121; " +
            "-fx-pref-height: 40px;"
        );

        datePicker.focusedProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal) {
                datePicker.setStyle(
                    "-fx-background-radius: 6px; " +
                    "-fx-border-radius: 6px; " +
                    "-fx-border-color: #4CAF50; " +
                    "-fx-border-width: 1px; " +
                    "-fx-padding: 10px 15px; " +
                    "-fx-font-size: 14px; " +
                    "-fx-background-color: white; " +
                    "-fx-text-fill: #212121; " +
                    "-fx-pref-height: 40px; " +
                    "-fx-effect: dropshadow(gaussian, rgba(76, 175, 80, 0.2), 3, 0, 0, 0);"
                );
            } else {
                datePicker.setStyle(
                    "-fx-background-radius: 6px; " +
                    "-fx-border-radius: 6px; " +
                    "-fx-border-color: #E0E0E0; " +
                    "-fx-border-width: 1px; " +
                    "-fx-padding: 10px 15px; " +
                    "-fx-font-size: 14px; " +
                    "-fx-background-color: white; " +
                    "-fx-text-fill: #212121; " +
                    "-fx-pref-height: 40px;"
                );
            }
        });

        return datePicker;
    }

    private <T> ComboBox<T> createEnhancedComboBox() {
        ComboBox<T> comboBox = new ComboBox<>();
        comboBox.setPrefWidth(400);
        comboBox.setStyle(
            "-fx-background-radius: 6px; " +
            "-fx-border-radius: 6px; " +
            "-fx-border-color: #E0E0E0; " +
            "-fx-border-width: 1px; " +
            "-fx-padding: 10px 15px; " +
            "-fx-font-size: 14px; " +
            "-fx-background-color: white; " +
            "-fx-text-fill: #212121; " +
            "-fx-pref-height: 40px;"
        );

        comboBox.focusedProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal) {
                comboBox.setStyle(
                    "-fx-background-radius: 6px; " +
                    "-fx-border-radius: 6px; " +
                    "-fx-border-color: #4CAF50; " +
                    "-fx-border-width: 1px; " +
                    "-fx-padding: 10px 15px; " +
                    "-fx-font-size: 14px; " +
                    "-fx-background-color: white; " +
                    "-fx-text-fill: #212121; " +
                    "-fx-pref-height: 40px; " +
                    "-fx-effect: dropshadow(gaussian, rgba(76, 175, 80, 0.2), 3, 0, 0, 0);"
                );
            } else {
                comboBox.setStyle(
                    "-fx-background-radius: 6px; " +
                    "-fx-border-radius: 6px; " +
                    "-fx-border-color: #E0E0E0; " +
                    "-fx-border-width: 1px; " +
                    "-fx-padding: 10px 15px; " +
                    "-fx-font-size: 14px; " +
                    "-fx-background-color: white; " +
                    "-fx-text-fill: #212121; " +
                    "-fx-pref-height: 40px;"
                );
            }
        });

        return comboBox;
    }

    private void setupTeachingCoursesComboBox(ComboBox<String> comboBox) {
        comboBox.setItems(FXCollections.observableArrayList(
            "Tiếng Anh 1", "Nhập môn ngành và kỹ năng mềm IT", "Đại số", "Tin học đại cương",
            "Cơ sở dữ liệu", "Lập trình hướng đối tượng và Java cơ bản", "Tiếng Anh chuyên ngành 1 IT",
            "Tiếng Anh 2", "Vật lý", "Cấu trúc dữ liệu và giải thuật", "Kiến trúc máy tính",
            "Nguyên lý hệ điều hành", "Lập trình Java nâng cao", "Tiếng Anh chuyên ngành 2",
            "Công nghệ Web", "Tiếng Anh 3", "Giải tích 1", "Khởi nghiệp và đổi mới sáng tạo",
            "Toán rời rạc", "Công nghệ và lập trình web", "Tiếng Anh chuyên ngành 3",
            "Phân tích và thiết kế hệ thống", "Giải tích 2", "Xác suất thống kê", "Vi điều khiển",
            "Lập trình di động", "Thực tập doanh nghiệp IT", "Tiếng Anh chuyên ngành 4 IT",
            "Mạng máy tính", "Cấu kiện điện tử", "Hình họa", "Cơ sở tạo hình", "Tiếng Hàn 1",
            "Lập trình mạng", "Công nghệ phần mềm", "Linux và phần mềm nguồn mở", "Chuyên đề 1 (IT)",
            "Lập trình hệ thống", "Trí tuệ nhân tạo", "Đồ họa máy tính IT", "Truyền số liệu",
            "Thiết kế và xây dựng hệ thống mạng", "Chuyển mạch và định tuyến", "Kỹ thuật mạch điện tử",
            "Kiến trúc và giao thức IoT", "Kịch bản truyền thông", "Lập trình web nâng cao",
            "Tiếng Hàn 2", "Triết học Mác - Lênin", "Pháp luật đại cương", "Automat và Ngôn ngữ hình thức",
            "Học máy", "Phân tích và thiết kế giải thuật", "Chuyên đề 2", "Quản trị mạng",
            "Giám sát hệ thống mạng", "Hệ thống số", "Thiết kế mạch điện tử với sự trợ giúp của máy tính",
            "Thị giác máy tính", "Kỹ thuật chụp ảnh và quay phim", "Xử lý ảnh cơ bản", "Nghệ thuật chữ",
            "Thiết kế nhân vật 2 chiều", "Lập trình Game", "Kho dữ liệu", "Kinh tế chính trị Mác - Lênin",
            "Chương trình dịch", "Điện toán đám mây", "Phát triển ứng dụng di động đa nền tảng",
            "Chuyên đề 3", "Đảm bảo chất lượng và kiểm thử phần mềm", "Mật mã học", "Xử lý tín hiệu số",
            "Kỹ thuật Robot", "Hệ thống nhúng", "Thiết kế nhân vật 3 chiều", "Học sâu", "Lập trình C++",
            "Mạng SDN (Software-Defined Networking)", "Kiểm thử xâm nhập", "Điện tử ứng dụng",
            "Hệ thống thời gian thực", "Truyền thông đại chúng ứng dụng", "Thiết kế Poster quảng cáo",
            "Thiết kế nhận diện thương hiệu", "Thiết kế phông nền phim Hoạt hình", "Tư tưởng Hồ Chí Minh",
            "Lịch sử Đảng Cộng sản Việt Nam", "Quản trị dự án phần mềm", "Chuyên đề 4",
            "Bảo mật và An toàn hệ thống thông tin", "Chủ nghĩa xã hội khoa học", "Phân tích mã độc",
            "An toàn ứng dụng Web và CSDL", "Robot di động", "Hệ thống thực tế ảo",
            "Thiết kế hình hiệu (TV intro)", "Lập trình game nâng cao", "Biên tập phim kỹ thuật số",
            "Xử lý ngôn ngữ tự nhiên", "Thiết kế UX/UI", "Lập trình Python", "Lập trình C#",
            "Thu thập và phân tích thông tin an ninh mạng", "Giao thức an toàn mạng",
            "Lập trình Python cho hệ thống nhúng", "Vi xử lý", "Mạng cảm biến không dây",
            "Thiết kế truyện tranh", "Thiết kế ấn phẩm báo chí", "Thực tập tốt nghiệp (IT)",
            "Cơ sở lập trình", "Toán cao cấp", "Vật lý đại cương", "Hóa học đại cương",
            "Sinh học đại cương", "Địa lý học", "Lịch sử Việt Nam", "Văn học Việt Nam",
            "Tiếng Anh giao tiếp", "Tiếng Anh thương mại", "Tiếng Nhật cơ bản", "Tiếng Trung cơ bản",
            "Kỹ năng mềm", "Kỹ năng thuyết trình", "Kỹ năng làm việc nhóm", "Kỹ năng giao tiếp",
            "Quản lý thời gian", "Tư duy logic", "Tư duy sáng tạo", "Phương pháp nghiên cứu khoa học"
        ));
        comboBox.setPromptText("Chọn môn giảng dạy...");
    }

    private void setupDepartmentComboBox(ComboBox<String> comboBox) {
        comboBox.setItems(FXCollections.observableArrayList(
            "Khoa Khoa học máy tính",
            "Khoa Kỹ thuật máy tính và Điện tử",
            "Khoa Kinh tế số và Thương mại điện tử"
        ));
        comboBox.setPromptText("Chọn khoa/phòng ban...");
    }



    private VBox createFormSection(String title, HBox... fieldRows) {
        VBox section = new VBox();
        section.setSpacing(15);
        section.setPadding(new Insets(20));
        section.setStyle(
            "-fx-background-color: white; " +
            "-fx-background-radius: 10px; " +
            "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 6, 0, 0, 3);"
        );

        Label sectionTitle = new Label(title);
        sectionTitle.setStyle(
            "-fx-font-size: 16px; " +
            "-fx-font-weight: bold; " +
            "-fx-text-fill: #4CAF50; " +
            "-fx-padding: 0 0 10 0;"
        );

        section.getChildren().add(sectionTitle);
        section.getChildren().addAll(fieldRows);

        return section;
    }

    private HBox createFieldRow(String labelText, javafx.scene.Node field) {
        HBox row = new HBox();
        row.setSpacing(15);
        row.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        Label label = new Label(labelText);
        label.setStyle(
            "-fx-font-size: 14px; " +
            "-fx-font-weight: bold; " +
            "-fx-text-fill: #424242; " +
            "-fx-min-width: 120px;"
        );

        field.setStyle(field.getStyle() + "-fx-pref-width: 300px;");

        row.getChildren().addAll(label, field);
        return row;
    }

    private HBox createFieldRowWithTextArea(String labelText, TextArea textArea) {
        HBox row = new HBox();
        row.setSpacing(15);
        row.setAlignment(javafx.geometry.Pos.TOP_LEFT);

        Label label = new Label(labelText);
        label.setStyle(
            "-fx-font-size: 14px; " +
            "-fx-font-weight: bold; " +
            "-fx-text-fill: #424242; " +
            "-fx-min-width: 120px;"
        );

        textArea.setStyle(textArea.getStyle() + "-fx-pref-width: 300px;");

        row.getChildren().addAll(label, textArea);
        return row;
    }
}
