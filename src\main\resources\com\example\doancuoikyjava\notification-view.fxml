<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.example.doancuoikyjava.controller.NotificationViewController">
   <top>
      <VBox styleClass="header-section">
         <children>
            <HBox alignment="CENTER_LEFT" spacing="20.0" style="-fx-background-color: linear-gradient(to right, #667eea, #764ba2); -fx-padding: 20;">
               <children>
                  <Button fx:id="backButton" onAction="#goBack" text="← Quay lại" 
                          style="-fx-background-color: rgba(255,255,255,0.2); -fx-text-fill: white; -fx-border-color: white; -fx-border-radius: 5; -fx-background-radius: 5; -fx-font-weight: bold;" />
                  <Label fx:id="welcomeLabel" text="📢 Thông báo" 
                         style="-fx-text-fill: white; -fx-font-size: 24px; -fx-font-weight: bold;" />
               </children>
            </HBox>
         </children>
      </VBox>
   </top>
   <center>
      <VBox spacing="20.0" style="-fx-background-color: linear-gradient(to bottom, #f5f7fa, #c3cfe2);">
         <children>
            <!-- Statistics Cards -->
            <HBox spacing="20.0">
               <children>
                  <VBox spacing="10.0" style="-fx-background-color: white; -fx-background-radius: 15; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2); -fx-min-width: 200;">
                     <children>
                        <Label text="📢 Tổng thông báo" style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" />
                        <Label fx:id="totalNotificationsLabel" text="0" style="-fx-font-size: 32px; -fx-font-weight: bold; -fx-text-fill: #3498db;" />
                     </children>
                     <padding>
                        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                     </padding>
                  </VBox>
                  
                  <VBox spacing="10.0" style="-fx-background-color: white; -fx-background-radius: 15; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2); -fx-min-width: 200;">
                     <children>
                        <Label text="📬 Chưa đọc" style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" />
                        <Label fx:id="unreadNotificationsLabel" text="0" style="-fx-font-size: 32px; -fx-font-weight: bold; -fx-text-fill: #e67e22;" />
                     </children>
                     <padding>
                        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                     </padding>
                  </VBox>
                  
                  <VBox spacing="10.0" style="-fx-background-color: white; -fx-background-radius: 15; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2); -fx-min-width: 200;">
                     <children>
                        <Label text="🚨 Khẩn cấp" style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" />
                        <Label fx:id="urgentNotificationsLabel" text="0" style="-fx-font-size: 32px; -fx-font-weight: bold; -fx-text-fill: #e74c3c;" />
                     </children>
                     <padding>
                        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                     </padding>
                  </VBox>
                  
                  <VBox spacing="10.0" style="-fx-background-color: white; -fx-background-radius: 15; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2); -fx-min-width: 200;">
                     <children>
                        <Label text="📅 Hôm nay" style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" />
                        <Label fx:id="todayNotificationsLabel" text="0" style="-fx-font-size: 32px; -fx-font-weight: bold; -fx-text-fill: #27ae60;" />
                     </children>
                     <padding>
                        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                     </padding>
                  </VBox>
               </children>
            </HBox>
            
            <!-- Control Panel -->
            <VBox spacing="20.0" style="-fx-background-color: white; -fx-background-radius: 15; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);">
               <children>
                  <HBox spacing="15.0" alignment="CENTER_LEFT">
                     <children>
                        <Button fx:id="refreshButton" onAction="#refreshData" text="🔄 Làm mới" 
                                style="-fx-background-color: linear-gradient(to bottom, #3498db, #2980b9); -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 8; -fx-padding: 10 20;" />
                        <Label text="📢 Thông báo từ nhà trường và giảng viên" style="-fx-font-size: 14px; -fx-text-fill: #7f8c8d;" />
                     </children>
                  </HBox>
                  
                  <Label text="🔧 Bộ lọc và tìm kiếm" style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" />
                  
                  <HBox spacing="20.0" alignment="CENTER_LEFT">
                     <children>
                        <VBox spacing="5.0">
                           <children>
                              <Label text="Loại:" style="-fx-font-weight: bold; -fx-text-fill: #34495e;" />
                              <ComboBox fx:id="typeFilterComboBox" prefWidth="150.0" 
                                        style="-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-radius: 8; -fx-background-radius: 8;" />
                           </children>
                        </VBox>
                        
                        <VBox spacing="5.0">
                           <children>
                              <Label text="Mức độ:" style="-fx-font-weight: bold; -fx-text-fill: #34495e;" />
                              <ComboBox fx:id="priorityFilterComboBox" prefWidth="150.0" 
                                        style="-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-radius: 8; -fx-background-radius: 8;" />
                           </children>
                        </VBox>
                        
                        <VBox spacing="5.0">
                           <children>
                              <Label text="Trạng thái:" style="-fx-font-weight: bold; -fx-text-fill: #34495e;" />
                              <ComboBox fx:id="statusFilterComboBox" prefWidth="150.0" 
                                        style="-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-radius: 8; -fx-background-radius: 8;" />
                           </children>
                        </VBox>
                        
                        <VBox spacing="5.0" HBox.hgrow="ALWAYS">
                           <children>
                              <Label text="Tìm kiếm:" style="-fx-font-weight: bold; -fx-text-fill: #34495e;" />
                              <TextField fx:id="searchField" promptText="Tìm theo tiêu đề hoặc nội dung..." 
                                         style="-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-radius: 8; -fx-background-radius: 8;" />
                           </children>
                        </VBox>
                     </children>
                  </HBox>
               </children>
               <padding>
                  <Insets bottom="25.0" left="25.0" right="25.0" top="25.0" />
               </padding>
            </VBox>
            
            <!-- Notifications Table -->
            <VBox spacing="15.0" VBox.vgrow="ALWAYS" style="-fx-background-color: white; -fx-background-radius: 15; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);">
               <children>
                  <Label text="📋 Danh sách thông báo" style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" />
                  
                  <TableView fx:id="notificationsTableView" VBox.vgrow="ALWAYS" style="-fx-background-color: transparent;">
                     <columns>
                        <TableColumn fx:id="titleColumn" prefWidth="250.0" text="Tiêu đề" />
                        <TableColumn fx:id="typeColumn" prefWidth="120.0" text="Loại" />
                        <TableColumn fx:id="priorityColumn" prefWidth="100.0" text="Mức độ" />
                        <TableColumn fx:id="statusColumn" prefWidth="120.0" text="Trạng thái" />
                        <TableColumn fx:id="createdAtColumn" prefWidth="120.0" text="Ngày tạo" />
                        <TableColumn fx:id="expiryColumn" prefWidth="120.0" text="Hết hạn" />
                        <TableColumn fx:id="actionsColumn" prefWidth="160.0" text="Thao tác" />
                     </columns>
                  </TableView>
               </children>
               <padding>
                  <Insets bottom="25.0" left="25.0" right="25.0" top="25.0" />
               </padding>
            </VBox>
         </children>
         <padding>
            <Insets bottom="25.0" left="25.0" right="25.0" top="25.0" />
         </padding>
      </VBox>
   </center>
</BorderPane>
