package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.User;
import com.example.doancuoikyjava.service.UserService;
import com.example.doancuoikyjava.service.CourseService;
import com.example.doancuoikyjava.util.SceneManager;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;

import java.net.URL;
import java.util.ResourceBundle;

public class AdminController implements Initializable {
    
    @FXML private Label welcomeLabel;
    @FXML private Button logoutButton;
    @FXML private Button manageStudentsBtn;
    @FXML private Button manageTeachersBtn;
    @FXML private Button manageCoursesBtn;
    @FXML private Button manageSchedulesBtn;
    @FXML private Button manageGradesBtn;


    
    @FXML private Label totalStudentsLabel;
    @FXML private Label totalTeachersLabel;
    @FXML private Label totalCoursesLabel;
    @FXML private Label newStudentsLabel;
    @FXML private Label activeCoursesLabel;
    @FXML private ListView<String> recentActivitiesListView;
    
    private UserService userService;
    private CourseService courseService;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        userService = new UserService();
        courseService = new CourseService();
        
        setupWelcomeMessage();
        loadDashboardData();
        setupRecentActivities();
    }
    
    private void setupWelcomeMessage() {
        User currentUser = SceneManager.getCurrentUser();
        if (currentUser != null) {
            welcomeLabel.setText("Xin chào, " + currentUser.getFullName());
        }
    }
    
    private void loadDashboardData() {
        // Load statistics
        long totalStudents = userService.getTotalStudents();
        long totalTeachers = userService.getTotalTeachers();
        long totalCourses = courseService.getTotalCourses();
        
        totalStudentsLabel.setText(String.valueOf(totalStudents));
        totalTeachersLabel.setText(String.valueOf(totalTeachers));
        totalCoursesLabel.setText(String.valueOf(totalCourses));
        
        // For demo purposes, set some sample data
        newStudentsLabel.setText("5");
        activeCoursesLabel.setText(String.valueOf(totalCourses));
    }
    
    private void setupRecentActivities() {
        ObservableList<String> activities = FXCollections.observableArrayList(
            "📚 Môn học 'Lập trình Java' đã được tạo",
            "👨‍🎓 Sinh viên mới 'Nguyễn Văn C' đã đăng ký",
            "👨‍🏫 Giáo viên 'Trần Thị D' đã cập nhật thông tin",
            "📝 Điểm số môn 'Cơ sở dữ liệu' đã được nhập",
            "🎯 Hệ thống đã được khởi động thành công"
        );
        recentActivitiesListView.setItems(activities);
    }
    
    @FXML
    private void handleLogout() {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Xác nhận đăng xuất");
        alert.setHeaderText("Bạn có chắc chắn muốn đăng xuất?");
        alert.setContentText("Tất cả dữ liệu chưa lưu sẽ bị mất.");
        
        if (alert.showAndWait().get() == ButtonType.OK) {
            SceneManager.logout();
        }
    }
    
    @FXML
    private void showStudentManagement() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/student-management.fxml",
                                   "Quản lý sinh viên - " + SceneManager.getCurrentUser().getFullName());
        } catch (Exception e) {
            showAlert("Lỗi", "Không thể mở trang quản lý sinh viên: " + e.getMessage());
        }
    }
    
    @FXML
    private void showTeacherManagement() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/teacher-management.fxml",
                                   "Quản lý giáo viên - " + SceneManager.getCurrentUser().getFullName());
        } catch (Exception e) {
            showAlert("Lỗi", "Không thể mở trang quản lý giáo viên: " + e.getMessage());
        }
    }
    
    @FXML
    private void showCourseManagement() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/course-management.fxml",
                                   "Quản lý môn học - " + SceneManager.getCurrentUser().getFullName());
        } catch (Exception e) {
            showAlert("Lỗi", "Không thể mở trang quản lý môn học: " + e.getMessage());
        }
    }

    @FXML
    private void showScheduleManagement() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/admin-schedule-management.fxml",
                                   "Quản lý lịch học - " + SceneManager.getCurrentUser().getFullName());
        } catch (Exception e) {
            showAlert("Lỗi", "Không thể mở trang quản lý lịch học: " + e.getMessage());
        }
    }
    

    


    @FXML
    private void showCharts() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/charts.fxml",
                                   "Biểu đồ Thống kê - " + SceneManager.getCurrentUser().getFullName());
        } catch (Exception e) {
            showAlert("Lỗi", "Không thể mở trang biểu đồ: " + e.getMessage());
        }
    }

    @FXML
    private void showNotifications() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/notification-management.fxml",
                                   "Quản lý Thông báo - " + SceneManager.getCurrentUser().getFullName());
        } catch (Exception e) {
            showAlert("Lỗi", "Không thể mở trang quản lý thông báo: " + e.getMessage());
        }
    }

    @FXML
    private void showSettings() {
        showAlert("Thông báo", "Chức năng cài đặt đang được phát triển!");
    }



    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
