package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.Student;
import com.example.doancuoikyjava.model.Teacher;
import com.example.doancuoikyjava.model.User;
import com.example.doancuoikyjava.service.ReportService;
import com.example.doancuoikyjava.service.UserService;
import com.example.doancuoikyjava.util.SceneManager;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Region;
import javafx.scene.layout.VBox;

import java.io.IOException;
import java.net.URL;
import java.util.List;
import java.util.ResourceBundle;

public class ReportController implements Initializable {
    
    @FXML private Label welcomeLabel;
    @FXML private ComboBox<String> reportTypeComboBox;
    @FXML private ComboBox<String> studentComboBox;
    @FXML private ComboBox<String> teacherComboBox;
    @FXML private Button generateReportButton;
    @FXML private Button backButton;
    @FXML private TextArea reportTextArea;
    
    private ReportService reportService;
    private UserService userService;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        reportService = new ReportService();
        userService = new UserService();
        
        setupUI();
        loadData();
    }
    
    private void setupUI() {
        // Thiết lập welcome label
        User currentUser = SceneManager.getCurrentUser();
        if (currentUser != null) {
            welcomeLabel.setText("Báo cáo - " + currentUser.getFullName());
        }
        
        // Thiết lập combo box loại báo cáo
        ObservableList<String> reportTypes = FXCollections.observableArrayList(
            "Báo cáo sinh viên",
            "Báo cáo môn học",
            "Báo cáo điểm theo giáo viên",
            "Bảng điểm sinh viên"
        );
        reportTypeComboBox.setItems(reportTypes);
        
        // Thiết lập sự kiện thay đổi loại báo cáo
        reportTypeComboBox.setOnAction(e -> onReportTypeChanged());
        
        // Thiết lập text area
        reportTextArea.setEditable(true); // Cho phép edit để có thể copy
        reportTextArea.setWrapText(true);
        reportTextArea.setStyle("-fx-font-family: 'Consolas', 'Monaco', monospace; -fx-font-size: 12px;");
        
        // Ẩn combo box ban đầu
        studentComboBox.setVisible(false);
        teacherComboBox.setVisible(false);
    }
    
    private void loadData() {
        // Load danh sách sinh viên
        List<User> students = userService.getUsersByRole(User.UserRole.STUDENT);
        ObservableList<String> studentItems = FXCollections.observableArrayList();
        for (User user : students) {
            if (user instanceof Student) {
                Student student = (Student) user;
                studentItems.add(student.getStudentId() + " - " + student.getFullName());
            }
        }
        studentComboBox.setItems(studentItems);
        
        // Load danh sách giáo viên
        List<User> teachers = userService.getUsersByRole(User.UserRole.TEACHER);
        ObservableList<String> teacherItems = FXCollections.observableArrayList();
        for (User user : teachers) {
            if (user instanceof Teacher) {
                Teacher teacher = (Teacher) user;
                teacherItems.add(teacher.getTeacherId() + " - " + teacher.getFullName());
            }
        }
        teacherComboBox.setItems(teacherItems);
    }
    
    @FXML
    private void onReportTypeChanged() {
        String selectedType = reportTypeComboBox.getValue();
        
        // Ẩn tất cả combo box
        studentComboBox.setVisible(false);
        teacherComboBox.setVisible(false);
        
        // Hiển thị combo box tương ứng
        if ("Bảng điểm sinh viên".equals(selectedType)) {
            studentComboBox.setVisible(true);
        } else if ("Báo cáo điểm theo giáo viên".equals(selectedType)) {
            teacherComboBox.setVisible(true);
        }
        
        // Xóa nội dung báo cáo cũ
        reportTextArea.clear();
    }
    
    @FXML
    private void generateReport() {
        String selectedType = reportTypeComboBox.getValue();
        
        if (selectedType == null) {
            showAlert("Lỗi", "Vui lòng chọn loại báo cáo!");
            return;
        }
        
        String report = "";
        
        try {
            switch (selectedType) {
                case "Báo cáo sinh viên":
                    report = reportService.generateStudentStatisticsReport();
                    break;

                case "Báo cáo môn học":
                    report = reportService.generateCourseStatisticsReport();
                    break;
                    
                case "Báo cáo điểm theo giáo viên":
                    String selectedTeacher = teacherComboBox.getValue();
                    if (selectedTeacher == null) {
                        showAlert("Lỗi", "Vui lòng chọn giáo viên!");
                        return;
                    }
                    String teacherId = selectedTeacher.split(" - ")[0];
                    report = reportService.generateTeacherGradeReport(teacherId);
                    break;
                    
                case "Bảng điểm sinh viên":
                    String selectedStudent = studentComboBox.getValue();
                    if (selectedStudent == null) {
                        showAlert("Lỗi", "Vui lòng chọn sinh viên!");
                        return;
                    }
                    String studentId = selectedStudent.split(" - ")[0];
                    report = reportService.generateStudentTranscript(studentId);
                    break;
                    
                default:
                    showAlert("Lỗi", "Loại báo cáo không được hỗ trợ!");
                    return;
            }

            // Debug logging
            System.out.println("📋 Report generated with length: " + report.length());
            System.out.println("📋 Report preview: " + (report.length() > 100 ? report.substring(0, 100) + "..." : report));

            if (report.trim().isEmpty()) {
                showAlert("Cảnh báo", "Báo cáo được tạo nhưng không có nội dung. Có thể không có dữ liệu.");
                reportTextArea.setText("Không có dữ liệu để hiển thị báo cáo.");
            } else {
                reportTextArea.setText(report);
                showAlert("Thành công", "Báo cáo đã được tạo thành công với " + report.length() + " ký tự!");
            }
            
        } catch (Exception e) {
            showAlert("Lỗi", "Không thể tạo báo cáo: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    @FXML
    private void exportReport() {
        String reportContent = reportTextArea.getText();
        
        if (reportContent.isEmpty()) {
            showAlert("Lỗi", "Không có báo cáo nào để xuất!");
            return;
        }
        
        // Tạo dialog đẹp để hiển thị báo cáo đầy đủ
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("📋 Xuất báo cáo");
        alert.setHeaderText("Báo cáo chi tiết - " + reportTypeComboBox.getValue());

        // Tạo TextArea với styling đẹp
        TextArea fullReportArea = new TextArea(reportContent);
        fullReportArea.setEditable(false);
        fullReportArea.setWrapText(true);
        fullReportArea.setPrefSize(1200, 900);
        fullReportArea.setStyle(
            "-fx-font-family: 'Consolas', 'Monaco', monospace; " +
            "-fx-font-size: 13px; " +
            "-fx-background-color: #f8f9fa; " +
            "-fx-border-color: #dee2e6; " +
            "-fx-border-radius: 8; " +
            "-fx-background-radius: 8; " +
            "-fx-padding: 15;"
        );

        // Tạo VBox container với header đẹp
        VBox container = new VBox(10);
        container.setStyle("-fx-padding: 20; -fx-background-color: white;");

        // Header với thông tin báo cáo
        HBox header = new HBox(15);
        header.setStyle("-fx-background-color: linear-gradient(to right, #667eea, #764ba2); " +
                       "-fx-padding: 15; -fx-background-radius: 8;");

        Label titleLabel = new Label("📊 " + reportTypeComboBox.getValue());
        titleLabel.setStyle("-fx-text-fill: white; -fx-font-size: 16px; -fx-font-weight: bold;");

        Label timeLabel = new Label("🕒 " + java.time.LocalDateTime.now().format(
            java.time.format.DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")));
        timeLabel.setStyle("-fx-text-fill: white; -fx-font-size: 12px;");

        header.getChildren().addAll(titleLabel, new Region(), timeLabel);

        ScrollPane scrollPane = new ScrollPane(fullReportArea);
        scrollPane.setFitToWidth(true);
        scrollPane.setFitToHeight(true);
        scrollPane.setStyle("-fx-background-color: transparent; -fx-border-color: transparent;");

        container.getChildren().addAll(header, scrollPane);

        alert.getDialogPane().setContent(container);
        alert.getDialogPane().setPrefSize(1200, 900);
        alert.getDialogPane().setStyle("-fx-background-color: #f5f7fa;");

        alert.showAndWait();
    }
    
    @FXML
    private void clearReport() {
        reportTextArea.clear();
        reportTypeComboBox.setValue(null);
        studentComboBox.setValue(null);
        teacherComboBox.setValue(null);
        studentComboBox.setVisible(false);
        teacherComboBox.setVisible(false);
    }
    
    @FXML
    private void refreshData() {
        loadData();
        showAlert("Thành công", "Dữ liệu đã được làm mới!");
    }
    
    @FXML
    private void goBack() {
        try {
            User currentUser = SceneManager.getCurrentUser();
            if (currentUser != null) {
                switch (currentUser.getRole()) {
                    case ADMIN:
                        SceneManager.switchScene("/com/example/doancuoikyjava/admin-dashboard.fxml", 
                                               "Quản trị viên - " + currentUser.getFullName());
                        break;
                    case TEACHER:
                        SceneManager.switchScene("/com/example/doancuoikyjava/teacher-dashboard.fxml", 
                                               "Giáo viên - " + currentUser.getFullName());
                        break;
                    case STUDENT:
                        SceneManager.switchScene("/com/example/doancuoikyjava/student-dashboard.fxml", 
                                               "Sinh viên - " + currentUser.getFullName());
                        break;
                }
            }
        } catch (IOException e) {
            showAlert("Lỗi", "Không thể quay lại trang chính: " + e.getMessage());
        }
    }
    
    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
