package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.*;
import com.example.doancuoikyjava.service.*;
import com.example.doancuoikyjava.util.SceneManager;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;

import java.io.IOException;
import java.net.URL;
import java.time.LocalDate;
import java.util.List;
import java.util.ResourceBundle;
import java.util.stream.Collectors;

public class StudentGradesController implements Initializable {
    
    @FXML private Label welcomeLabel;
    @FXML private Button backButton;
    @FXML private Button refreshBtn;
    @FXML private Button searchBtn;
    @FXML private Button exportBtn;
    
    @FXML private Label totalCreditsLabel;
    @FXML private Label gpaLabel;
    @FXML private Label averageScoreLabel;
    
    @FXML private ComboBox<String> semesterFilterComboBox;
    @FXML private ComboBox<String> gradeFilterComboBox;
    @FXML private TextField searchField;
    
    @FXML private TableView<Grade> gradesTableView;
    @FXML private TableColumn<Grade, String> courseIdColumn;
    @FXML private TableColumn<Grade, String> courseNameColumn;
    @FXML private TableColumn<Grade, Integer> creditsColumn;
    @FXML private TableColumn<Grade, Double> scoreColumn;
    @FXML private TableColumn<Grade, String> letterGradeColumn;
    @FXML private TableColumn<Grade, Double> gradePointColumn;
    @FXML private TableColumn<Grade, String> semesterColumn;
    @FXML private TableColumn<Grade, LocalDate> dateColumn;
    
    @FXML private Label totalGradesLabel;
    @FXML private Label passedSubjectsLabel;
    @FXML private Label failedSubjectsLabel;
    
    private GradeService gradeService;
    private UserService userService;
    private Student currentStudent;
    private ObservableList<Grade> allGrades;
    private ObservableList<Grade> filteredGrades;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        gradeService = new GradeService();
        userService = new UserService();
        allGrades = FXCollections.observableArrayList();
        filteredGrades = FXCollections.observableArrayList();
        
        setupCurrentStudent();
        setupWelcomeMessage();
        setupTableColumns();
        setupFilters();
        loadGrades();
    }
    
    private void setupCurrentStudent() {
        User currentUser = SceneManager.getCurrentUser();
        if (currentUser instanceof Student) {
            currentStudent = (Student) currentUser;
        }
    }
    
    private void setupWelcomeMessage() {
        if (currentStudent != null) {
            welcomeLabel.setText("Xin chào, " + currentStudent.getFullName());
        }
    }
    
    private void setupTableColumns() {
        courseIdColumn.setCellValueFactory(new PropertyValueFactory<>("courseId"));
        courseNameColumn.setCellValueFactory(new PropertyValueFactory<>("courseName"));
        creditsColumn.setCellValueFactory(new PropertyValueFactory<>("credits"));
        scoreColumn.setCellValueFactory(new PropertyValueFactory<>("score"));
        letterGradeColumn.setCellValueFactory(new PropertyValueFactory<>("letterGrade"));
        gradePointColumn.setCellValueFactory(new PropertyValueFactory<>("gradePoint"));
        semesterColumn.setCellValueFactory(new PropertyValueFactory<>("semester"));
        dateColumn.setCellValueFactory(new PropertyValueFactory<>("dateRecorded"));
        
        gradesTableView.setItems(filteredGrades);
    }
    
    private void setupFilters() {
        // Setup search functionality
        searchField.textProperty().addListener((obs, oldText, newText) -> filterGrades());
        semesterFilterComboBox.valueProperty().addListener((obs, oldValue, newValue) -> filterGrades());
        gradeFilterComboBox.valueProperty().addListener((obs, oldValue, newValue) -> filterGrades());
        
        // Setup grade filter
        gradeFilterComboBox.setItems(FXCollections.observableArrayList("A", "B", "C", "D", "F"));
        
        // Setup semester filter
        semesterFilterComboBox.setItems(FXCollections.observableArrayList("2024-1", "2024-2", "2023-1", "2023-2"));
    }
    
    private void loadGrades() {
        if (currentStudent == null) return;
        
        // Load grades for current student
        List<Grade> studentGrades = gradeService.getGradesByStudent(currentStudent.getStudentId());
        allGrades.setAll(studentGrades);
        
        filterGrades();
        updateStatistics();
    }
    
    private void filterGrades() {
        String searchText = searchField.getText().toLowerCase();
        String selectedSemester = semesterFilterComboBox.getValue();
        String selectedGrade = gradeFilterComboBox.getValue();
        
        List<Grade> filtered = allGrades.stream()
                .filter(grade -> {
                    boolean matchesSearch = searchText.isEmpty() || 
                            grade.getCourseName().toLowerCase().contains(searchText) ||
                            grade.getCourseId().toLowerCase().contains(searchText);
                    
                    boolean matchesSemester = selectedSemester == null || 
                            grade.getSemester().equals(selectedSemester);
                    
                    boolean matchesGrade = selectedGrade == null || 
                            grade.getLetterGrade().equals(selectedGrade);
                    
                    return matchesSearch && matchesSemester && matchesGrade;
                })
                .collect(Collectors.toList());
        
        filteredGrades.setAll(filtered);
        updateFilteredStatistics();
    }
    
    private void updateStatistics() {
        if (currentStudent == null) return;
        
        // Update overall statistics
        int totalCredits = allGrades.stream()
                .mapToInt(Grade::getCredits)
                .sum();
        totalCreditsLabel.setText(String.valueOf(totalCredits));
        
        // Update GPA
        gpaLabel.setText(String.format("%.2f", currentStudent.getGpa()));
        
        // Calculate average score
        if (!allGrades.isEmpty()) {
            double averageScore = allGrades.stream()
                    .mapToDouble(Grade::getScore)
                    .average()
                    .orElse(0.0);
            averageScoreLabel.setText(String.format("%.2f", averageScore));
        } else {
            averageScoreLabel.setText("0.00");
        }
    }
    
    private void updateFilteredStatistics() {
        totalGradesLabel.setText("Tổng số môn: " + filteredGrades.size());
        
        long passedSubjects = filteredGrades.stream()
                .filter(grade -> grade.getScore() >= 60)
                .count();
        passedSubjectsLabel.setText("Môn đã qua: " + passedSubjects);
        
        long failedSubjects = filteredGrades.size() - passedSubjects;
        failedSubjectsLabel.setText("Môn chưa qua: " + failedSubjects);
    }
    
    @FXML
    private void goBack() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/student-dashboard.fxml", 
                                   "Sinh viên - " + SceneManager.getCurrentUser().getFullName());
        } catch (IOException e) {
            showAlert("Lỗi", "Không thể quay lại trang chính: " + e.getMessage());
        }
    }
    
    @FXML
    private void refreshData() {
        // Reload student data to get updated GPA
        if (currentStudent != null) {
            User updatedUser = userService.getUserById(currentStudent.getUserId());
            if (updatedUser instanceof Student) {
                currentStudent = (Student) updatedUser;
                SceneManager.setCurrentUser(currentStudent);
            }
        }
        
        loadGrades();
        showAlert("Thành công", "Dữ liệu đã được làm mới!");
    }
    
    @FXML
    private void searchGrades() {
        filterGrades();
    }
    
    @FXML
    private void exportGrades() {
        if (filteredGrades.isEmpty()) {
            showAlert("Thông báo", "Không có dữ liệu để xuất.");
            return;
        }
        
        StringBuilder report = new StringBuilder();
        report.append("BẢNG ĐIỂM SINH VIÊN\n");
        report.append("==================\n\n");
        
        if (currentStudent != null) {
            report.append("Sinh viên: ").append(currentStudent.getFullName()).append("\n");
            report.append("MSSV: ").append(currentStudent.getStudentId()).append("\n");
            report.append("Lớp: ").append(currentStudent.getClassName()).append("\n");
            report.append("Ngành: ").append(currentStudent.getMajor()).append("\n");
            report.append("GPA: ").append(String.format("%.2f", currentStudent.getGpa())).append("\n\n");
        }
        
        report.append("CHI TIẾT ĐIỂM:\n");
        report.append("Mã MH\tTên môn học\t\t\tTín chỉ\tĐiểm\tXếp loại\tHọc kỳ\n");
        report.append("----------------------------------------------------------------\n");
        
        for (Grade grade : filteredGrades) {
            report.append(grade.getCourseId()).append("\t");
            report.append(grade.getCourseName()).append("\t\t");
            report.append(grade.getCredits()).append("\t");
            report.append(grade.getScore()).append("\t");
            report.append(grade.getLetterGrade()).append("\t\t");
            report.append(grade.getSemester()).append("\n");
        }
        
        // Show report in a dialog
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Bảng điểm");
        alert.setHeaderText("Bảng điểm của " + (currentStudent != null ? currentStudent.getFullName() : "sinh viên"));
        
        TextArea textArea = new TextArea(report.toString());
        textArea.setEditable(false);
        textArea.setWrapText(true);
        textArea.setPrefSize(600, 400);
        
        alert.getDialogPane().setContent(textArea);
        alert.showAndWait();
    }
    
    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
