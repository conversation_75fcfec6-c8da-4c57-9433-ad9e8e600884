package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.User;
import com.example.doancuoikyjava.service.EnhancedCourseService;
import com.example.doancuoikyjava.service.DatabaseUserService;
import com.example.doancuoikyjava.service.AdvancedNotificationService;
import com.example.doancuoikyjava.service.AdvancedReportingService;
import com.example.doancuoikyjava.util.*;

import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.scene.chart.*;
import javafx.concurrent.Task;
import javafx.application.Platform;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.animation.Timeline;
import javafx.animation.KeyFrame;
import javafx.util.Duration;

import java.net.URL;
import java.util.ResourceBundle;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Enhanced Dashboard Controller with comprehensive features:
 * - Modern UI with theme support
 * - Real-time statistics
 * - Performance monitoring
 * - Cache management
 * - Advanced notifications
 * - Enhanced reporting
 */
public class EnhancedDashboardController implements Initializable {

    @FXML private Label welcomeLabel;
    @FXML private Label currentTimeLabel;
    @FXML private VBox statsContainer;
    @FXML private VBox chartsContainer;
    @FXML private VBox systemInfoContainer;
    @FXML private Button refreshStatsBtn;
    @FXML private Button viewReportsBtn;
    @FXML private Button systemSettingsBtn;
    @FXML private Button themeToggleBtn;
    @FXML private Button performanceBtn;
    @FXML private Button cacheBtn;
    @FXML private Button notificationBtn;
    @FXML private ProgressIndicator loadingIndicator;
    @FXML private Label statusLabel;
    @FXML private ProgressBar systemHealthBar;

    private EnhancedCourseService courseService;
    private DatabaseUserService userService;
    private User currentUser;

    // Enhanced services
    private PerformanceMonitor performanceMonitor;
    private CacheManager cacheManager;
    private AdvancedNotificationService notificationService;
    private AdvancedReportingService reportingService;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        System.out.println("🚀 Enhanced Dashboard: Initializing with all features...");

        try {
            // Initialize enhanced services
            initializeEnhancedServices();

            // Initialize existing services
            courseService = new EnhancedCourseService();
            userService = new DatabaseUserService();
            currentUser = SceneManager.getCurrentUser();

            // Setup UI
            setupWelcomeMessage();
            setupEnhancedUI();
            setupRealTimeUpdates();
            setupEnhancedFeatures();

            // Load data asynchronously
            loadDashboardDataAsync();

            System.out.println("✅ Enhanced Dashboard: Full initialization completed");

        } catch (Exception e) {
            System.err.println("❌ Enhanced Dashboard: Initialization failed: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Initialize enhanced services
     */
    private void initializeEnhancedServices() {
        performanceMonitor = PerformanceMonitor.getInstance();
        cacheManager = CacheManager.getInstance();
        notificationService = AdvancedNotificationService.getInstance();
        reportingService = AdvancedReportingService.getInstance();

        System.out.println("🚀 Enhanced services initialized");
    }
    
    /**
     * Setup welcome message with user info
     */
    private void setupWelcomeMessage() {
        if (currentUser != null && welcomeLabel != null) {
            String greeting = getTimeBasedGreeting();
            welcomeLabel.setText(greeting + ", " + currentUser.getFullName() + "! 👋");
            welcomeLabel.getStyleClass().add("label-title");
        }
    }
    
    /**
     * Setup enhanced UI components
     */
    private void setupEnhancedUI() {
        try {
            // Style buttons
            if (refreshStatsBtn != null) {
                refreshStatsBtn.getStyleClass().addAll("enhanced-button", "primary");
                EnhancedUIComponents.addHoverAnimation(refreshStatsBtn);
            }
            
            if (viewReportsBtn != null) {
                viewReportsBtn.getStyleClass().addAll("enhanced-button", "success");
                EnhancedUIComponents.addHoverAnimation(viewReportsBtn);
            }
            
            if (systemSettingsBtn != null) {
                systemSettingsBtn.getStyleClass().addAll("enhanced-button", "warning");
                EnhancedUIComponents.addHoverAnimation(systemSettingsBtn);
            }
            
            // Setup containers
            if (statsContainer != null) {
                statsContainer.getStyleClass().add("dashboard-container");
                statsContainer.setSpacing(20);
            }
            
            if (chartsContainer != null) {
                chartsContainer.getStyleClass().add("dashboard-container");
                chartsContainer.setSpacing(20);
            }
            
            if (systemInfoContainer != null) {
                systemInfoContainer.getStyleClass().add("dashboard-container");
                systemInfoContainer.setSpacing(15);
            }
            
        } catch (Exception e) {
            System.err.println("❌ Error setting up enhanced UI: " + e.getMessage());
        }
    }
    
    /**
     * Setup real-time updates
     */
    private void setupRealTimeUpdates() {
        // Update current time every second
        Timeline timeline = new Timeline(
            new KeyFrame(Duration.seconds(1), e -> updateCurrentTime())
        );
        timeline.setCycleCount(Timeline.INDEFINITE);
        timeline.play();
    }
    
    /**
     * Load dashboard data asynchronously
     */
    private void loadDashboardDataAsync() {
        if (loadingIndicator != null) {
            loadingIndicator.setVisible(true);
        }
        
        Task<Void> loadTask = new Task<Void>() {
            @Override
            protected Void call() throws Exception {
                // Simulate loading time for demo
                Thread.sleep(1000);
                
                Platform.runLater(() -> {
                    try {
                        loadStatistics();
                        loadCharts();
                        loadSystemInfo();
                        
                        if (loadingIndicator != null) {
                            loadingIndicator.setVisible(false);
                        }
                        
                    } catch (Exception e) {
                        System.err.println("❌ Error loading dashboard data: " + e.getMessage());
                    }
                });
                
                return null;
            }
        };
        
        Thread loadThread = new Thread(loadTask);
        loadThread.setDaemon(true);
        loadThread.start();
    }
    
    /**
     * Load and display statistics
     */
    private void loadStatistics() {
        if (statsContainer == null) return;
        
        try {
            // Get enrollment statistics
            EnhancedCourseService.EnrollmentStats stats = courseService.getEnrollmentStats();
            
            // Create statistics cards
            HBox statsRow = new HBox(20);
            statsRow.setAlignment(Pos.CENTER);
            
            VBox totalCoursesCard = EnhancedUIComponents.createStatCard(
                "Total Courses", 
                String.valueOf(stats.getTotalCourses()), 
                "📚"
            );
            
            VBox totalStudentsCard = EnhancedUIComponents.createStatCard(
                "Enrolled Students", 
                String.valueOf(stats.getTotalEnrolledStudents()), 
                "👥"
            );
            
            VBox avgEnrollmentCard = EnhancedUIComponents.createStatCard(
                "Avg Enrollment", 
                String.format("%.1f", stats.getAvgEnrollmentPerCourse()), 
                "📊"
            );
            
            VBox systemStatusCard = EnhancedUIComponents.createStatCard(
                "System Status", 
                "Online", 
                "✅"
            );
            
            statsRow.getChildren().addAll(
                totalCoursesCard, totalStudentsCard, 
                avgEnrollmentCard, systemStatusCard
            );
            
            statsContainer.getChildren().clear();
            statsContainer.getChildren().add(statsRow);
            
            System.out.println("✅ Statistics loaded successfully");
            
        } catch (Exception e) {
            System.err.println("❌ Error loading statistics: " + e.getMessage());
        }
    }
    
    /**
     * Load and display charts
     */
    private void loadCharts() {
        if (chartsContainer == null) return;
        
        try {
            // Create enrollment trend chart
            LineChart<String, Number> enrollmentChart = createEnrollmentTrendChart();
            
            // Create course distribution chart
            PieChart courseDistributionChart = createCourseDistributionChart();
            
            // Create charts container
            HBox chartsRow = new HBox(20);
            chartsRow.setAlignment(Pos.CENTER);
            
            VBox chartCard1 = EnhancedUIComponents.createCard("Enrollment Trends", enrollmentChart);
            VBox chartCard2 = EnhancedUIComponents.createCard("Course Distribution", courseDistributionChart);
            
            chartsRow.getChildren().addAll(chartCard1, chartCard2);
            
            chartsContainer.getChildren().clear();
            chartsContainer.getChildren().add(chartsRow);
            
            System.out.println("✅ Charts loaded successfully");
            
        } catch (Exception e) {
            System.err.println("❌ Error loading charts: " + e.getMessage());
        }
    }
    
    /**
     * Load system information
     */
    private void loadSystemInfo() {
        if (systemInfoContainer == null) return;
        
        try {
            VBox systemCard = EnhancedUIComponents.createCard("System Information", null);
            
            // Database connection info
            String dbStats = EnhancedDatabaseConnection.getConnectionStats();
            Label dbLabel = new Label("Database Status: Connected ✅");
            dbLabel.getStyleClass().add("form-label");
            
            // Service info
            String serviceStats = courseService.getServiceStats();
            Label serviceLabel = new Label("Enhanced Services: Active 🚀");
            serviceLabel.getStyleClass().add("form-label");
            
            // Performance info
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory() / 1024 / 1024;
            long freeMemory = runtime.freeMemory() / 1024 / 1024;
            long usedMemory = totalMemory - freeMemory;
            
            Label memoryLabel = new Label(String.format("Memory Usage: %d MB / %d MB", usedMemory, totalMemory));
            memoryLabel.getStyleClass().add("form-label");
            
            ProgressBar memoryBar = EnhancedUIComponents.createEnhancedProgressBar();
            memoryBar.setProgress((double) usedMemory / totalMemory);
            
            VBox infoContent = new VBox(10);
            infoContent.getChildren().addAll(dbLabel, serviceLabel, memoryLabel, memoryBar);
            
            systemCard.getChildren().add(infoContent);
            
            systemInfoContainer.getChildren().clear();
            systemInfoContainer.getChildren().add(systemCard);
            
            System.out.println("✅ System info loaded successfully");
            
        } catch (Exception e) {
            System.err.println("❌ Error loading system info: " + e.getMessage());
        }
    }
    
    /**
     * Create enrollment trend chart
     */
    private LineChart<String, Number> createEnrollmentTrendChart() {
        CategoryAxis xAxis = new CategoryAxis();
        NumberAxis yAxis = new NumberAxis();
        xAxis.setLabel("Month");
        yAxis.setLabel("Enrollments");
        
        LineChart<String, Number> chart = new LineChart<>(xAxis, yAxis);
        chart.setTitle("Monthly Enrollment Trends");
        chart.setPrefSize(400, 300);
        
        XYChart.Series<String, Number> series = new XYChart.Series<>();
        series.setName("Enrollments");
        
        // Sample data
        series.getData().add(new XYChart.Data<>("Jan", 45));
        series.getData().add(new XYChart.Data<>("Feb", 52));
        series.getData().add(new XYChart.Data<>("Mar", 48));
        series.getData().add(new XYChart.Data<>("Apr", 61));
        series.getData().add(new XYChart.Data<>("May", 58));
        series.getData().add(new XYChart.Data<>("Jun", 67));
        
        chart.getData().add(series);
        return chart;
    }
    
    /**
     * Create course distribution chart
     */
    private PieChart createCourseDistributionChart() {
        PieChart chart = new PieChart();
        chart.setTitle("Course Distribution by Department");
        chart.setPrefSize(400, 300);
        
        // Sample data
        chart.getData().addAll(
            new PieChart.Data("Computer Science", 35),
            new PieChart.Data("Mathematics", 25),
            new PieChart.Data("Physics", 20),
            new PieChart.Data("Chemistry", 15),
            new PieChart.Data("Others", 5)
        );
        
        return chart;
    }
    
    /**
     * Get time-based greeting
     */
    private String getTimeBasedGreeting() {
        int hour = LocalDateTime.now().getHour();
        if (hour < 12) return "Good Morning";
        else if (hour < 17) return "Good Afternoon";
        else return "Good Evening";
    }
    
    /**
     * Update current time display
     */
    private void updateCurrentTime() {
        if (currentTimeLabel != null) {
            String currentTime = LocalDateTime.now().format(
                DateTimeFormatter.ofPattern("EEEE, MMMM dd, yyyy - HH:mm:ss")
            );
            currentTimeLabel.setText(currentTime);
        }
    }
    
    /**
     * Event handlers
     */
    @FXML
    private void handleRefreshStats() {
        System.out.println("🔄 Refreshing dashboard statistics...");
        loadDashboardDataAsync();
        
        // Show notification
        EnhancedUIComponents.showNotificationToast(
            "Dashboard refreshed successfully!", 
            "success", 
            statsContainer.getParent()
        );
    }
    
    @FXML
    private void handleViewReports() {
        System.out.println("📊 Opening reports view...");
        // TODO: Implement reports view
        EnhancedUIComponents.showNotificationToast(
            "Reports feature coming soon!", 
            "info", 
            statsContainer.getParent()
        );
    }
    
    @FXML
    private void handleSystemSettings() {
        System.out.println("⚙️ Opening system settings...");
        // TODO: Implement system settings
        EnhancedUIComponents.showNotificationToast(
            "System settings feature coming soon!",
            "info",
            statsContainer.getParent()
        );
    }

    /**
     * Setup enhanced features UI
     */
    private void setupEnhancedFeatures() {
        // Setup theme toggle button
        if (themeToggleBtn != null) {
            updateThemeButton();
            EnhancedUIComponents.addHoverAnimation(themeToggleBtn);
        }

        // Setup performance button
        if (performanceBtn != null) {
            performanceBtn.getStyleClass().addAll("enhanced-button", "primary");
            EnhancedUIComponents.addHoverAnimation(performanceBtn);
        }

        // Setup cache button
        if (cacheBtn != null) {
            cacheBtn.getStyleClass().addAll("enhanced-button", "warning");
            EnhancedUIComponents.addHoverAnimation(cacheBtn);
        }

        // Setup notification button
        if (notificationBtn != null) {
            notificationBtn.getStyleClass().addAll("enhanced-button", "success");
            EnhancedUIComponents.addHoverAnimation(notificationBtn);
        }

        // Update system status
        updateSystemStatus();
    }

    /**
     * Update theme button text
     */
    private void updateThemeButton() {
        if (themeToggleBtn != null) {
            ThemeManager.Theme currentTheme = SceneManager.getCurrentTheme();
            themeToggleBtn.setText("Theme: " + currentTheme.getDisplayName());
        }
    }

    /**
     * Update system status
     */
    private void updateSystemStatus() {
        if (statusLabel != null) {
            PerformanceMonitor.MemoryInfo memInfo = performanceMonitor.getMemoryInfo();
            String status = String.format("Memory: %s | Uptime: %s | Cache: %d entries",
                memInfo.getHeapUsedMB(),
                performanceMonitor.getFormattedUptime(),
                cacheManager.size()
            );
            statusLabel.setText(status);
        }

        if (systemHealthBar != null) {
            PerformanceMonitor.MemoryInfo memInfo = performanceMonitor.getMemoryInfo();
            double health = 1.0 - memInfo.getHeapUsagePercentage();
            systemHealthBar.setProgress(Math.max(0.1, health));
        }
    }

    /**
     * Enhanced event handlers
     */
    @FXML
    private void handleToggleTheme() {
        PerformanceMonitor.Timer timer = performanceMonitor.startTimer("theme_toggle");

        SceneManager.toggleTheme();
        updateThemeButton();

        EnhancedUIComponents.showNotificationToast(
            "Theme toggled successfully!",
            "success",
            statsContainer.getParent()
        );

        timer.stop();
    }

    @FXML
    private void handlePerformanceView() {
        String performanceReport = performanceMonitor.getDetailedReport();

        Alert alert = EnhancedUIComponents.createEnhancedAlert(
            Alert.AlertType.INFORMATION,
            "Performance Monitor",
            performanceReport
        );
        alert.showAndWait();
    }

    @FXML
    private void handleCacheManagement() {
        String cacheInfo = cacheManager.getDetailedInfo();

        Alert alert = EnhancedUIComponents.createEnhancedAlert(
            Alert.AlertType.INFORMATION,
            "Cache Management",
            cacheInfo + "\n\nWould you like to clear the cache?"
        );

        alert.getButtonTypes().clear();
        alert.getButtonTypes().addAll(ButtonType.YES, ButtonType.NO);

        alert.showAndWait().ifPresent(response -> {
            if (response == ButtonType.YES) {
                cacheManager.clear();
                updateSystemStatus();

                EnhancedUIComponents.showNotificationToast(
                    "Cache cleared successfully!",
                    "success",
                    statsContainer.getParent()
                );
            }
        });
    }

    @FXML
    private void handleNotificationTest() {
        if (currentUser != null) {
            notificationService.sendNotification(
                currentUser.getUserId(),
                "Test Notification",
                "This is a test notification from the enhanced dashboard!",
                AdvancedNotificationService.NotificationType.INFO,
                AdvancedNotificationService.Priority.NORMAL
            ).thenAccept(success -> {
                Platform.runLater(() -> {
                    if (success) {
                        EnhancedUIComponents.showNotificationToast(
                            "Test notification sent successfully!",
                            "success",
                            statsContainer.getParent()
                        );
                    } else {
                        EnhancedUIComponents.showNotificationToast(
                            "Failed to send notification!",
                            "error",
                            statsContainer.getParent()
                        );
                    }
                });
            });
        }
    }

    @FXML
    private void handleForceGC() {
        performanceMonitor.forceGC();
        updateSystemStatus();

        EnhancedUIComponents.showNotificationToast(
            "Garbage collection completed!",
            "success",
            statsContainer.getParent()
        );
    }
}
