<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.example.doancuoikyjava.controller.AddScheduleDialogController">
   <children>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="10.0" style="-fx-background-color: #2196F3; -fx-padding: 15px 20px;">
         <children>
            <Label text="🕒" style="-fx-font-size: 20px; -fx-text-fill: white;" />
            <Label fx:id="titleLabel" text="THÊM LỊCH HỌC MỚI" style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: white;" />
         </children>
      </HBox>
      
      <!-- Form Content -->
      <ScrollPane fitToWidth="true" prefHeight="500.0">
         <content>
            <VBox spacing="20.0" style="-fx-padding: 20px;">
               <children>
                  <!-- Course and Teacher Selection -->
                  <HBox spacing="20.0">
                     <children>
                        <!-- Course Selection -->
                        <VBox spacing="10.0" HBox.hgrow="ALWAYS">
                           <children>
                              <Label text="Môn học *" style="-fx-font-weight: bold; -fx-font-size: 14px;" />
                              <ComboBox fx:id="courseComboBox" prefWidth="300.0" promptText="Chọn môn học" />
                              <Label fx:id="courseInfoLabel" text="" style="-fx-font-size: 12px; -fx-text-fill: #666;" />
                           </children>
                        </VBox>

                        <!-- Teacher Selection -->
                        <VBox spacing="10.0" HBox.hgrow="ALWAYS">
                           <children>
                              <Label text="Giảng viên *" style="-fx-font-weight: bold; -fx-font-size: 14px;" />
                              <ComboBox fx:id="teacherComboBox" prefWidth="250.0" promptText="Chọn giảng viên" />
                              <Label fx:id="teacherInfoLabel" text="" style="-fx-font-size: 12px; -fx-text-fill: #666;" />
                           </children>
                        </VBox>
                     </children>
                  </HBox>
                  
                  <!-- Day and Time Selection -->
                  <HBox spacing="20.0">
                     <children>
                        <!-- Day Selection -->
                        <VBox spacing="10.0" HBox.hgrow="ALWAYS">
                           <children>
                              <Label text="Thứ trong tuần *" style="-fx-font-weight: bold; -fx-font-size: 14px;" />
                              <ComboBox fx:id="dayComboBox" prefWidth="180.0" promptText="Chọn thứ" />
                           </children>
                        </VBox>
                        
                        <!-- Start Time -->
                        <VBox spacing="10.0" HBox.hgrow="ALWAYS">
                           <children>
                              <Label text="Giờ bắt đầu *" style="-fx-font-weight: bold; -fx-font-size: 14px;" />
                              <ComboBox fx:id="startTimeComboBox" prefWidth="120.0" promptText="Giờ bắt đầu" />
                           </children>
                        </VBox>
                        
                        <!-- End Time -->
                        <VBox spacing="10.0" HBox.hgrow="ALWAYS">
                           <children>
                              <Label text="Giờ kết thúc *" style="-fx-font-weight: bold; -fx-font-size: 14px;" />
                              <ComboBox fx:id="endTimeComboBox" prefWidth="120.0" promptText="Giờ kết thúc" />
                           </children>
                        </VBox>
                     </children>
                  </HBox>
                  
                  <!-- Classroom and Week Type -->
                  <HBox spacing="20.0">
                     <children>
                        <!-- Classroom Selection -->
                        <VBox spacing="10.0" HBox.hgrow="ALWAYS">
                           <children>
                              <Label text="Phòng học *" style="-fx-font-weight: bold; -fx-font-size: 14px;" />
                              <ComboBox fx:id="classroomComboBox" prefWidth="150.0" promptText="Chọn phòng" />
                           </children>
                        </VBox>
                        
                        <!-- Week Type -->
                        <VBox spacing="10.0" HBox.hgrow="ALWAYS">
                           <children>
                              <Label text="Loại tuần" style="-fx-font-weight: bold; -fx-font-size: 14px;" />
                              <ComboBox fx:id="weekTypeComboBox" prefWidth="150.0" promptText="Loại tuần" />
                           </children>
                        </VBox>
                        
                        <!-- Semester -->
                        <VBox spacing="10.0" HBox.hgrow="ALWAYS">
                           <children>
                              <Label text="Học kỳ" style="-fx-font-weight: bold; -fx-font-size: 14px;" />
                              <ComboBox fx:id="semesterComboBox" prefWidth="150.0" promptText="Học kỳ" />
                           </children>
                        </VBox>
                     </children>
                  </HBox>
                  
                  <!-- Academic Year -->
                  <VBox spacing="10.0">
                     <children>
                        <Label text="Năm học" style="-fx-font-weight: bold; -fx-font-size: 14px;" />
                        <ComboBox fx:id="academicYearComboBox" prefWidth="200.0" promptText="Chọn năm học" />
                     </children>
                  </VBox>
                  
                  <!-- Conflict Check Results -->
                  <VBox fx:id="conflictCheckBox" spacing="10.0" style="-fx-background-color: #FFF3E0; -fx-border-color: #FF9800; -fx-border-radius: 5px; -fx-background-radius: 5px; -fx-padding: 15px;" visible="false">
                     <children>
                        <Label text="⚠️ KIỂM TRA XUNG ĐỘT" style="-fx-font-weight: bold; -fx-font-size: 14px; -fx-text-fill: #F57C00;" />
                        <Label fx:id="conflictResultLabel" text="" style="-fx-font-size: 12px; -fx-text-fill: #E65100;" wrapText="true" />
                     </children>
                  </VBox>
                  
                  <!-- Preview -->
                  <VBox spacing="10.0" style="-fx-background-color: #E8F5E8; -fx-border-color: #4CAF50; -fx-border-radius: 5px; -fx-background-radius: 5px; -fx-padding: 15px;">
                     <children>
                        <Label text="📋 XEM TRƯỚC LỊCH HỌC" style="-fx-font-weight: bold; -fx-font-size: 14px; -fx-text-fill: #2E7D32;" />
                        <Label fx:id="previewLabel" text="Vui lòng điền đầy đủ thông tin để xem trước" style="-fx-font-size: 12px; -fx-text-fill: #388E3C;" wrapText="true" />
                        <Label fx:id="previewTeacherLabel" text="" style="-fx-font-size: 12px; -fx-text-fill: #2E7D32; -fx-font-weight: bold;" wrapText="true" />
                     </children>
                  </VBox>
                  
                  <!-- Notes -->
                  <VBox spacing="10.0">
                     <children>
                        <Label text="Ghi chú" style="-fx-font-weight: bold; -fx-font-size: 14px;" />
                        <TextArea fx:id="notesTextArea" prefHeight="80.0" promptText="Nhập ghi chú cho lịch học (tùy chọn)..." wrapText="true" />
                     </children>
                  </VBox>
               </children>
            </VBox>
         </content>
      </ScrollPane>
      
      <!-- Action Buttons -->
      <HBox alignment="CENTER_RIGHT" spacing="15.0" style="-fx-background-color: #F5F5F5; -fx-border-color: #E0E0E0; -fx-border-width: 1px 0 0 0; -fx-padding: 15px 20px;">
         <children>
            <Button fx:id="checkConflictBtn" onAction="#checkConflicts" text="🔍 Kiểm tra xung đột" style="-fx-background-color: #FF9800; -fx-text-fill: white; -fx-background-radius: 5px; -fx-padding: 10px 20px; -fx-font-weight: bold;" />
            <Button fx:id="cancelBtn" onAction="#cancel" text="❌ Hủy" style="-fx-background-color: #6c757d; -fx-text-fill: white; -fx-background-radius: 5px; -fx-padding: 10px 20px; -fx-font-weight: bold;" />
            <Button fx:id="saveBtn" onAction="#save" text="💾 Lưu lịch học" style="-fx-background-color: #4CAF50; -fx-text-fill: white; -fx-background-radius: 5px; -fx-padding: 10px 20px; -fx-font-weight: bold;" />
         </children>
      </HBox>
   </children>
</VBox>
