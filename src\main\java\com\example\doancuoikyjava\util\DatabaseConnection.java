package com.example.doancuoikyjava.util;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;

public class DatabaseConnection {
    private static final String SERVER = "LAPTOP-1HR3G05C";
    private static final String DATABASE = "StudentManagementDB";
    private static final String USERNAME = "sa";
    private static final String PASSWORD = "123456789";

    private static final String CONNECTION_URL = "jdbc:sqlserver://" + SERVER + ":1434;"
            + "databaseName=" + DATABASE + ";"
            + "user=" + USERNAME + ";"
            + "password=" + PASSWORD + ";"
            + "encrypt=false;trustServerCertificate=true;";
    
    private static Connection connection = null;
    
    /**
     * Lấy kết nối đến database
     */
    public static Connection getConnection() throws SQLException {
        if (connection == null || connection.isClosed()) {
            try {
                connection = DriverManager.getConnection(CONNECTION_URL);
                System.out.println("✅ Kết nối SQL Server thành công!");
            } catch (SQLException e) {
                System.err.println("❌ Lỗi kết nối SQL Server: " + e.getMessage());
                throw e;
            }
        }
        return connection;
    }
    
    /**
     * Đóng kết nối
     */
    public static void closeConnection() {
        if (connection != null) {
            try {
                connection.close();
                System.out.println("🔒 Đã đóng kết nối SQL Server");
            } catch (SQLException e) {
                System.err.println("❌ Lỗi khi đóng kết nối: " + e.getMessage());
            }
        }
    }
    
    /**
     * Kiểm tra kết nối
     */
    public static boolean testConnection() {
        try {
            Connection conn = getConnection();
            return conn != null && !conn.isClosed();
        } catch (SQLException e) {
            System.err.println("❌ Kiểm tra kết nối thất bại: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Tạo database và tables nếu chưa tồn tại
     */
    public static void initializeDatabase() {
        try {
            Connection conn = getConnection();
            Statement stmt = conn.createStatement();
            
            // Tạo bảng Users
            String createUsersTable = """
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
                CREATE TABLE Users (
                    user_id NVARCHAR(50) PRIMARY KEY,
                    username NVARCHAR(50) UNIQUE NOT NULL,
                    password NVARCHAR(255) NOT NULL,
                    full_name NVARCHAR(255) NOT NULL,
                    email NVARCHAR(255),
                    phone NVARCHAR(20),
                    date_of_birth DATE,
                    address NVARCHAR(500),
                    role NVARCHAR(20) NOT NULL,
                    created_date DATETIME DEFAULT GETDATE()
                )
                """;
            stmt.executeUpdate(createUsersTable);
            
            // Tạo bảng Students
            String createStudentsTable = """
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Students' AND xtype='U')
                CREATE TABLE Students (
                    student_id NVARCHAR(50) PRIMARY KEY,
                    user_id NVARCHAR(50) NOT NULL,
                    class_name NVARCHAR(50),
                    major NVARCHAR(100),
                    year_of_study INT,
                    gpa DECIMAL(3,2) DEFAULT 0.00,
                    FOREIGN KEY (user_id) REFERENCES Users(user_id) ON DELETE CASCADE
                )
                """;
            stmt.executeUpdate(createStudentsTable);
            
            // Tạo bảng Teachers
            String createTeachersTable = """
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Teachers' AND xtype='U')
                CREATE TABLE Teachers (
                    teacher_id NVARCHAR(50) PRIMARY KEY,
                    user_id NVARCHAR(50) NOT NULL,
                    department NVARCHAR(100),
                    position NVARCHAR(50),
                    salary DECIMAL(15,2),
                    qualification NVARCHAR(100),
                    experience_years INT DEFAULT 0,
                    FOREIGN KEY (user_id) REFERENCES Users(user_id) ON DELETE CASCADE
                )
                """;
            stmt.executeUpdate(createTeachersTable);
            
            // Tạo bảng Courses
            String createCoursesTable = """
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Courses' AND xtype='U')
                CREATE TABLE Courses (
                    course_id NVARCHAR(50) PRIMARY KEY,
                    course_name NVARCHAR(255) NOT NULL,
                    description NTEXT,
                    credits INT NOT NULL,
                    teacher_id NVARCHAR(50),
                    teacher_name NVARCHAR(255),
                    schedule NVARCHAR(255),
                    classroom NVARCHAR(50),
                    max_students INT DEFAULT 30,
                    created_date DATETIME DEFAULT GETDATE(),
                    FOREIGN KEY (teacher_id) REFERENCES Teachers(teacher_id)
                )
                """;
            stmt.executeUpdate(createCoursesTable);
            
            // Tạo bảng Course_Enrollments
            String createEnrollmentsTable = """
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Course_Enrollments' AND xtype='U')
                CREATE TABLE Course_Enrollments (
                    enrollment_id INT IDENTITY(1,1) PRIMARY KEY,
                    course_id NVARCHAR(50) NOT NULL,
                    student_id NVARCHAR(50) NOT NULL,
                    enrollment_date DATETIME DEFAULT GETDATE(),
                    FOREIGN KEY (course_id) REFERENCES Courses(course_id) ON DELETE CASCADE,
                    FOREIGN KEY (student_id) REFERENCES Students(student_id) ON DELETE CASCADE,
                    UNIQUE(course_id, student_id)
                )
                """;
            stmt.executeUpdate(createEnrollmentsTable);
            
            // Tạo bảng Grades
            String createGradesTable = """
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Grades' AND xtype='U')
                CREATE TABLE Grades (
                    grade_id NVARCHAR(50) PRIMARY KEY,
                    student_id NVARCHAR(50) NOT NULL,
                    course_id NVARCHAR(50) NOT NULL,
                    course_name NVARCHAR(255),
                    score DECIMAL(5,2) NOT NULL,
                    credits INT NOT NULL,
                    letter_grade NVARCHAR(2),
                    grade_point DECIMAL(3,2),
                    semester NVARCHAR(20),
                    teacher_id NVARCHAR(50),
                    date_recorded DATETIME DEFAULT GETDATE(),
                    FOREIGN KEY (student_id) REFERENCES Students(student_id) ON DELETE CASCADE,
                    FOREIGN KEY (course_id) REFERENCES Courses(course_id) ON DELETE CASCADE,
                    FOREIGN KEY (teacher_id) REFERENCES Teachers(teacher_id)
                )
                """;
            stmt.executeUpdate(createGradesTable);

            // Tạo bảng Teacher_Courses để lưu trữ môn dạy của giáo viên
            String createTeacherCoursesTable = """
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Teacher_Courses' AND xtype='U')
                CREATE TABLE Teacher_Courses (
                    id INT IDENTITY(1,1) PRIMARY KEY,
                    teacher_id NVARCHAR(50) NOT NULL,
                    course_name NVARCHAR(255) NOT NULL,
                    created_date DATETIME DEFAULT GETDATE(),
                    FOREIGN KEY (teacher_id) REFERENCES Teachers(teacher_id) ON DELETE CASCADE,
                    UNIQUE(teacher_id, course_name)
                )
                """;
            stmt.executeUpdate(createTeacherCoursesTable);

            // Tạo bảng Course_Schedule để lưu trữ lịch học chi tiết
            String createCourseScheduleTable = """
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Course_Schedule' AND xtype='U')
                CREATE TABLE Course_Schedule (
                    schedule_id INT IDENTITY(1,1) PRIMARY KEY,
                    course_id NVARCHAR(50) NOT NULL,
                    teacher_id NVARCHAR(50),
                    teacher_name NVARCHAR(255),
                    day_of_week INT NOT NULL, -- 2=Thứ 2, 3=Thứ 3, ..., 7=Thứ 7, 8=Chủ nhật
                    start_time TIME NOT NULL,
                    end_time TIME NOT NULL,
                    classroom NVARCHAR(50),
                    week_type NVARCHAR(20) DEFAULT 'ALL', -- ALL, ODD, EVEN
                    semester NVARCHAR(20),
                    academic_year NVARCHAR(20),
                    is_active BIT DEFAULT 1,
                    created_date DATETIME DEFAULT GETDATE(),
                    FOREIGN KEY (course_id) REFERENCES Courses(course_id) ON DELETE CASCADE,
                    FOREIGN KEY (teacher_id) REFERENCES Teachers(teacher_id) ON DELETE SET NULL
                )
                """;
            stmt.executeUpdate(createCourseScheduleTable);

            // Thêm cột teacher_id và teacher_name nếu chưa có
            addTeacherColumnsToScheduleTable(stmt);

            System.out.println("✅ Database và tables đã được tạo thành công!");
            
        } catch (SQLException e) {
            System.err.println("❌ Lỗi khi tạo database: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Thực thi câu lệnh SQL
     */
    public static boolean executeUpdate(String sql) {
        try {
            Connection conn = getConnection();
            Statement stmt = conn.createStatement();
            int result = stmt.executeUpdate(sql);
            return result > 0;
        } catch (SQLException e) {
            System.err.println("❌ Lỗi thực thi SQL: " + e.getMessage());
            return false;
        }
    }

    /**
     * Thêm cột teacher_id và teacher_name vào bảng Course_Schedule nếu chưa có
     */
    private static void addTeacherColumnsToScheduleTable(Statement stmt) {
        try {
            System.out.println("🔧 Checking and adding teacher columns to Course_Schedule table...");

            // Kiểm tra xem cột teacher_id đã tồn tại chưa
            String checkTeacherIdColumn = """
                IF NOT EXISTS (
                    SELECT * FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_NAME = 'Course_Schedule' AND COLUMN_NAME = 'teacher_id'
                )
                BEGIN
                    ALTER TABLE Course_Schedule ADD teacher_id NVARCHAR(50)
                    PRINT 'Added teacher_id column to Course_Schedule'
                END
                """;
            stmt.executeUpdate(checkTeacherIdColumn);

            // Kiểm tra xem cột teacher_name đã tồn tại chưa
            String checkTeacherNameColumn = """
                IF NOT EXISTS (
                    SELECT * FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_NAME = 'Course_Schedule' AND COLUMN_NAME = 'teacher_name'
                )
                BEGIN
                    ALTER TABLE Course_Schedule ADD teacher_name NVARCHAR(255)
                    PRINT 'Added teacher_name column to Course_Schedule'
                END
                """;
            stmt.executeUpdate(checkTeacherNameColumn);

            // Thêm foreign key constraint nếu chưa có (bỏ qua lỗi nếu đã tồn tại)
            try {
                String addForeignKeyConstraint = """
                    IF NOT EXISTS (
                        SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS
                        WHERE CONSTRAINT_NAME = 'FK_Course_Schedule_Teacher'
                    )
                    BEGIN
                        ALTER TABLE Course_Schedule
                        ADD CONSTRAINT FK_Course_Schedule_Teacher
                        FOREIGN KEY (teacher_id) REFERENCES Teachers(teacher_id) ON DELETE SET NULL
                        PRINT 'Added foreign key constraint for teacher_id'
                    END
                    """;
                stmt.executeUpdate(addForeignKeyConstraint);
            } catch (SQLException e) {
                System.out.println("⚠️ Foreign key constraint may already exist: " + e.getMessage());
            }

            System.out.println("✅ Teacher columns check/add completed for Course_Schedule table");

        } catch (SQLException e) {
            System.err.println("⚠️ Warning: Could not add teacher columns to Course_Schedule: " + e.getMessage());
            // Don't throw exception, just log warning as this is not critical for basic functionality
        }
    }
}
