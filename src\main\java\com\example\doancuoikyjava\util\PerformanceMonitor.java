package com.example.doancuoikyjava.util;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.lang.management.RuntimeMXBean;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Advanced Performance Monitor for tracking application performance metrics
 */
public class PerformanceMonitor {
    
    private static PerformanceMonitor instance;
    private final ScheduledExecutorService scheduler;
    private final ConcurrentHashMap<String, PerformanceMetric> metrics;
    private final AtomicLong totalOperations = new AtomicLong(0);
    private final AtomicLong totalExecutionTime = new AtomicLong(0);
    
    // System monitoring
    private final MemoryMXBean memoryBean;
    private final RuntimeMXBean runtimeBean;
    
    // Performance thresholds
    private static final long SLOW_OPERATION_THRESHOLD = 1000; // 1 second
    private static final double HIGH_MEMORY_THRESHOLD = 0.8; // 80%
    
    /**
     * Performance metric class
     */
    public static class PerformanceMetric {
        private final String name;
        private final AtomicLong executionCount = new AtomicLong(0);
        private final AtomicLong totalTime = new AtomicLong(0);
        private final AtomicLong minTime = new AtomicLong(Long.MAX_VALUE);
        private final AtomicLong maxTime = new AtomicLong(0);
        private volatile long lastExecutionTime;
        
        public PerformanceMetric(String name) {
            this.name = name;
        }
        
        public void recordExecution(long executionTime) {
            executionCount.incrementAndGet();
            totalTime.addAndGet(executionTime);
            lastExecutionTime = executionTime;
            
            // Update min/max
            minTime.updateAndGet(current -> Math.min(current, executionTime));
            maxTime.updateAndGet(current -> Math.max(current, executionTime));
        }
        
        public String getName() { return name; }
        public long getExecutionCount() { return executionCount.get(); }
        public long getTotalTime() { return totalTime.get(); }
        public long getMinTime() { return minTime.get() == Long.MAX_VALUE ? 0 : minTime.get(); }
        public long getMaxTime() { return maxTime.get(); }
        public long getLastExecutionTime() { return lastExecutionTime; }
        
        public double getAverageTime() {
            long count = executionCount.get();
            return count > 0 ? (double) totalTime.get() / count : 0.0;
        }
        
        @Override
        public String toString() {
            return String.format(
                "%s: Count=%d, Avg=%.2fms, Min=%dms, Max=%dms, Last=%dms",
                name, getExecutionCount(), getAverageTime(), getMinTime(), getMaxTime(), getLastExecutionTime()
            );
        }
    }
    
    /**
     * Timer class for measuring execution time
     */
    public static class Timer {
        private final String operationName;
        private final long startTime;
        
        public Timer(String operationName) {
            this.operationName = operationName;
            this.startTime = System.currentTimeMillis();
        }
        
        public void stop() {
            long executionTime = System.currentTimeMillis() - startTime;
            PerformanceMonitor.getInstance().recordOperation(operationName, executionTime);
        }
    }
    
    private PerformanceMonitor() {
        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "PerformanceMonitor");
            t.setDaemon(true);
            return t;
        });
        this.metrics = new ConcurrentHashMap<>();
        this.memoryBean = ManagementFactory.getMemoryMXBean();
        this.runtimeBean = ManagementFactory.getRuntimeMXBean();
        
        // Start periodic monitoring
        startPeriodicMonitoring();
        
        System.out.println("📊 Performance Monitor initialized");
    }
    
    public static synchronized PerformanceMonitor getInstance() {
        if (instance == null) {
            instance = new PerformanceMonitor();
        }
        return instance;
    }
    
    /**
     * Start timing an operation
     */
    public Timer startTimer(String operationName) {
        return new Timer(operationName);
    }
    
    /**
     * Record operation execution time
     */
    public void recordOperation(String operationName, long executionTime) {
        PerformanceMetric metric = metrics.computeIfAbsent(operationName, PerformanceMetric::new);
        metric.recordExecution(executionTime);
        
        totalOperations.incrementAndGet();
        totalExecutionTime.addAndGet(executionTime);
        
        // Log slow operations
        if (executionTime > SLOW_OPERATION_THRESHOLD) {
            System.out.println("⚠️ Slow operation detected: " + operationName + " took " + executionTime + "ms");
        }
    }
    
    /**
     * Get performance metric for operation
     */
    public PerformanceMetric getMetric(String operationName) {
        return metrics.get(operationName);
    }
    
    /**
     * Get all performance metrics
     */
    public ConcurrentHashMap<String, PerformanceMetric> getAllMetrics() {
        return new ConcurrentHashMap<>(metrics);
    }
    
    /**
     * Get system memory information
     */
    public MemoryInfo getMemoryInfo() {
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        MemoryUsage nonHeapUsage = memoryBean.getNonHeapMemoryUsage();
        
        return new MemoryInfo(
            heapUsage.getUsed(),
            heapUsage.getMax(),
            nonHeapUsage.getUsed(),
            nonHeapUsage.getMax()
        );
    }
    
    /**
     * Memory information class
     */
    public static class MemoryInfo {
        private final long heapUsed;
        private final long heapMax;
        private final long nonHeapUsed;
        private final long nonHeapMax;
        
        public MemoryInfo(long heapUsed, long heapMax, long nonHeapUsed, long nonHeapMax) {
            this.heapUsed = heapUsed;
            this.heapMax = heapMax;
            this.nonHeapUsed = nonHeapUsed;
            this.nonHeapMax = nonHeapMax;
        }
        
        public long getHeapUsed() { return heapUsed; }
        public long getHeapMax() { return heapMax; }
        public long getNonHeapUsed() { return nonHeapUsed; }
        public long getNonHeapMax() { return nonHeapMax; }
        
        public double getHeapUsagePercentage() {
            return heapMax > 0 ? (double) heapUsed / heapMax : 0.0;
        }
        
        public String getHeapUsedMB() {
            return String.format("%.2f MB", heapUsed / 1024.0 / 1024.0);
        }
        
        public String getHeapMaxMB() {
            return String.format("%.2f MB", heapMax / 1024.0 / 1024.0);
        }
        
        @Override
        public String toString() {
            return String.format(
                "Memory - Heap: %s/%s (%.1f%%), Non-Heap: %.2f MB",
                getHeapUsedMB(), getHeapMaxMB(), getHeapUsagePercentage() * 100,
                nonHeapUsed / 1024.0 / 1024.0
            );
        }
    }
    
    /**
     * Get application uptime
     */
    public long getUptimeMillis() {
        return runtimeBean.getUptime();
    }
    
    /**
     * Get formatted uptime
     */
    public String getFormattedUptime() {
        long uptime = getUptimeMillis();
        long seconds = uptime / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;
        
        if (days > 0) {
            return String.format("%dd %dh %dm", days, hours % 24, minutes % 60);
        } else if (hours > 0) {
            return String.format("%dh %dm %ds", hours, minutes % 60, seconds % 60);
        } else if (minutes > 0) {
            return String.format("%dm %ds", minutes, seconds % 60);
        } else {
            return String.format("%ds", seconds);
        }
    }
    
    /**
     * Get performance summary
     */
    public String getPerformanceSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("📊 Performance Summary:\n");
        summary.append("   • Uptime: ").append(getFormattedUptime()).append("\n");
        summary.append("   • Total Operations: ").append(totalOperations.get()).append("\n");
        
        if (totalOperations.get() > 0) {
            double avgTime = (double) totalExecutionTime.get() / totalOperations.get();
            summary.append("   • Average Operation Time: ").append(String.format("%.2fms", avgTime)).append("\n");
        }
        
        summary.append("   • ").append(getMemoryInfo().toString()).append("\n");
        summary.append("   • Tracked Operations: ").append(metrics.size()).append("\n");
        
        return summary.toString();
    }
    
    /**
     * Get detailed performance report
     */
    public String getDetailedReport() {
        StringBuilder report = new StringBuilder();
        report.append(getPerformanceSummary()).append("\n");
        
        if (!metrics.isEmpty()) {
            report.append("🔍 Operation Details:\n");
            metrics.values().stream()
                .sorted((a, b) -> Long.compare(b.getTotalTime(), a.getTotalTime()))
                .forEach(metric -> report.append("   • ").append(metric.toString()).append("\n"));
        }
        
        return report.toString();
    }
    
    /**
     * Start periodic monitoring
     */
    private void startPeriodicMonitoring() {
        scheduler.scheduleAtFixedRate(() -> {
            try {
                MemoryInfo memInfo = getMemoryInfo();
                
                // Check for high memory usage
                if (memInfo.getHeapUsagePercentage() > HIGH_MEMORY_THRESHOLD) {
                    System.out.println("⚠️ High memory usage detected: " + 
                        String.format("%.1f%%", memInfo.getHeapUsagePercentage() * 100));
                }
                
                // Log periodic summary (every 5 minutes)
                if (getUptimeMillis() % 300000 < 10000) { // Approximately every 5 minutes
                    System.out.println(getPerformanceSummary());
                }
                
            } catch (Exception e) {
                System.err.println("❌ Error in performance monitoring: " + e.getMessage());
            }
        }, 10, 10, TimeUnit.SECONDS);
    }
    
    /**
     * Clear all metrics
     */
    public void clearMetrics() {
        metrics.clear();
        totalOperations.set(0);
        totalExecutionTime.set(0);
        System.out.println("🗑️ Performance metrics cleared");
    }
    
    /**
     * Force garbage collection and log memory info
     */
    public void forceGC() {
        MemoryInfo beforeGC = getMemoryInfo();
        System.gc();
        
        // Wait a bit for GC to complete
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        MemoryInfo afterGC = getMemoryInfo();
        
        long freedMemory = beforeGC.getHeapUsed() - afterGC.getHeapUsed();
        System.out.println("🗑️ Garbage collection completed. Freed: " + 
            String.format("%.2f MB", freedMemory / 1024.0 / 1024.0));
    }
    
    /**
     * Shutdown performance monitor
     */
    public void shutdown() {
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        System.out.println("🔒 Performance Monitor shutdown completed");
        System.out.println(getDetailedReport());
    }
    
    // Predefined operation names
    public static final class Operations {
        public static final String DATABASE_QUERY = "database_query";
        public static final String USER_LOGIN = "user_login";
        public static final String SCENE_SWITCH = "scene_switch";
        public static final String DATA_LOAD = "data_load";
        public static final String CACHE_OPERATION = "cache_operation";
        public static final String NOTIFICATION_SEND = "notification_send";
        public static final String REPORT_GENERATION = "report_generation";
        public static final String FILE_OPERATION = "file_operation";
    }
}
