package com.example.doancuoikyjava.util;

import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.stage.FileChooser;
import javafx.stage.Stage;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;

public class AvatarUtils {
    
    private static final String AVATAR_DIRECTORY = "avatars";
    private static final String DEFAULT_AVATAR = "/images/default-avatar.png";
    
    static {
        // Tạo thư mục avatars nếu chưa tồn tại
        createAvatarDirectory();
    }
    
    /**
     * Tạo thư mục avatars
     */
    private static void createAvatarDirectory() {
        try {
            Path avatarPath = Paths.get(AVATAR_DIRECTORY);
            if (!Files.exists(avatarPath)) {
                Files.createDirectories(avatarPath);
            }
        } catch (IOException e) {
            System.err.println("Không thể tạo thư mục avatars: " + e.getMessage());
        }
    }
    
    /**
     * Mở dialog chọn file ảnh
     */
    public static File chooseAvatarFile(Stage parentStage) {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Chọn ảnh đại diện");
        
        // Thiết lập filter cho file ảnh
        FileChooser.ExtensionFilter imageFilter = new FileChooser.ExtensionFilter(
            "Image Files", "*.png", "*.jpg", "*.jpeg", "*.gif", "*.bmp"
        );
        fileChooser.getExtensionFilters().add(imageFilter);
        
        // Thiết lập thư mục mặc định
        File initialDirectory = new File(System.getProperty("user.home"));
        if (initialDirectory.exists()) {
            fileChooser.setInitialDirectory(initialDirectory);
        }
        
        return fileChooser.showOpenDialog(parentStage);
    }
    
    /**
     * Lưu avatar vào thư mục avatars
     */
    public static String saveAvatar(File sourceFile, String userId) {
        if (sourceFile == null || !sourceFile.exists()) {
            return null;
        }
        
        try {
            // Lấy extension của file
            String fileName = sourceFile.getName();
            String extension = "";
            int lastDotIndex = fileName.lastIndexOf('.');
            if (lastDotIndex > 0) {
                extension = fileName.substring(lastDotIndex);
            }
            
            // Tạo tên file mới
            String newFileName = userId + "_avatar" + extension;
            Path targetPath = Paths.get(AVATAR_DIRECTORY, newFileName);
            
            // Copy file
            Files.copy(sourceFile.toPath(), targetPath, StandardCopyOption.REPLACE_EXISTING);
            
            return targetPath.toString();
            
        } catch (IOException e) {
            System.err.println("Không thể lưu avatar: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * Tải ảnh avatar
     */
    public static Image loadAvatar(String avatarPath) {
        if (avatarPath == null || avatarPath.trim().isEmpty()) {
            return loadDefaultAvatar();
        }
        
        try {
            File avatarFile = new File(avatarPath);
            if (avatarFile.exists()) {
                return new Image(avatarFile.toURI().toString());
            } else {
                return loadDefaultAvatar();
            }
        } catch (Exception e) {
            System.err.println("Không thể tải avatar: " + e.getMessage());
            return loadDefaultAvatar();
        }
    }
    
    /**
     * Tải ảnh avatar mặc định
     */
    public static Image loadDefaultAvatar() {
        try {
            // Tạo ảnh đơn giản với màu xám
            return createDefaultImage();
        } catch (Exception e) {
            // Fallback: tạo ảnh 1x1 pixel
            return createSimpleImage();
        }
    }

    /**
     * Tạo ảnh mặc định đơn giản
     */
    private static Image createDefaultImage() {
        try {
            // Tạo ảnh 100x100 với background màu xám nhạt
            return new Image("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==");
        } catch (Exception e) {
            return createSimpleImage();
        }
    }

    /**
     * Tạo ảnh 1x1 pixel đơn giản
     */
    private static Image createSimpleImage() {
        // Ảnh 1x1 pixel trong suốt
        return new Image("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAAC0lEQVQIHWNgAAIAAAUAAY27m/MAAAAASUVORK5CYII=");
    }
    
    /**
     * Thiết lập ImageView cho avatar
     */
    public static void setupAvatarImageView(ImageView imageView, String avatarPath) {
        setupAvatarImageView(imageView, avatarPath, 100, 100);
    }
    
    /**
     * Thiết lập ImageView cho avatar với kích thước tùy chỉnh
     */
    public static void setupAvatarImageView(ImageView imageView, String avatarPath, double width, double height) {
        try {
            Image avatar = loadAvatar(avatarPath);
            imageView.setImage(avatar);

            // Thiết lập kích thước
            imageView.setFitWidth(width);
            imageView.setFitHeight(height);
            imageView.setPreserveRatio(true);
            imageView.setSmooth(true);

            // Thiết lập style đơn giản
            imageView.setStyle(
                "-fx-border-color: #dee2e6; " +
                "-fx-border-width: 2; " +
                "-fx-background-radius: 50%; " +
                "-fx-border-radius: 50%;"
            );

            // Clip để tạo hình tròn (đơn giản hóa)
            try {
                javafx.scene.shape.Circle clip = new javafx.scene.shape.Circle();
                clip.setCenterX(width / 2);
                clip.setCenterY(height / 2);
                clip.setRadius(Math.min(width, height) / 2);
                imageView.setClip(clip);
            } catch (Exception e) {
                // Nếu clip bị lỗi, bỏ qua
                System.out.println("Không thể tạo clip cho avatar: " + e.getMessage());
            }
        } catch (Exception e) {
            System.out.println("Lỗi khi setup avatar: " + e.getMessage());
            // Thiết lập cơ bản nếu có lỗi
            imageView.setFitWidth(width);
            imageView.setFitHeight(height);
        }
    }
    
    /**
     * Xóa avatar cũ
     */
    public static boolean deleteAvatar(String avatarPath) {
        if (avatarPath == null || avatarPath.trim().isEmpty()) {
            return true;
        }
        
        try {
            File avatarFile = new File(avatarPath);
            if (avatarFile.exists()) {
                return avatarFile.delete();
            }
            return true;
        } catch (Exception e) {
            System.err.println("Không thể xóa avatar: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Kiểm tra file có phải là ảnh không
     */
    public static boolean isImageFile(File file) {
        if (file == null || !file.exists()) {
            return false;
        }
        
        String fileName = file.getName().toLowerCase();
        return fileName.endsWith(".png") || 
               fileName.endsWith(".jpg") || 
               fileName.endsWith(".jpeg") || 
               fileName.endsWith(".gif") || 
               fileName.endsWith(".bmp");
    }
    
    /**
     * Lấy kích thước file theo MB
     */
    public static double getFileSizeMB(File file) {
        if (file == null || !file.exists()) {
            return 0;
        }
        return file.length() / (1024.0 * 1024.0);
    }
    
    /**
     * Kiểm tra kích thước file (giới hạn 5MB)
     */
    public static boolean isValidFileSize(File file) {
        return getFileSizeMB(file) <= 5.0;
    }
}
