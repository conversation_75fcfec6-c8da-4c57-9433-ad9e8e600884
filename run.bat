@echo off
title Student Management System - Main Launcher
color 0E

:main
cls
echo.
echo ==========================================
echo   🎓 STUDENT MANAGEMENT SYSTEM
echo ==========================================
echo.
echo Chọn vai trò để truy cập hệ thống:
echo.
echo 1. 🔧 ADMIN (Quản trị viên)
echo    └─ Quản lý toàn bộ hệ thống
echo.
echo 2. 👨‍🏫 TEACHER (Giảng viên)  
echo    └─ Quản lý giảng dạy và lịch học
echo.
echo 3. 🎓 STUDENT (Sinh viên)
echo    └─ Học tập và đăng ký môn học
echo.
echo 4. 🔑 LOGIN (Đăng nhập tùy chỉnh)
echo    └─ Đăng nhập với tài khoản riêng
echo.
echo 5. ❌ EXIT (<PERSON><PERSON><PERSON><PERSON>)
echo.
echo ==========================================
echo.
set /p choice="Nhập lựa chọn (1-5): "

if "%choice%"=="1" (
    echo.
    echo 🔧 Starting Admin Dashboard...
    call run-admin.bat
    goto main
) else if "%choice%"=="2" (
    echo.
    echo 👨‍🏫 Starting Teacher Dashboard...
    call run-teacher.bat
    goto main
) else if "%choice%"=="3" (
    echo.
    echo 🎓 Starting Student Dashboard...
    call run-student.bat
    goto main
) else if "%choice%"=="4" (
    echo.
    echo 🔑 Starting Login System...
    call run-login.bat
    goto main
) else if "%choice%"=="5" (
    echo.
    echo 👋 Cảm ơn bạn đã sử dụng!
    echo 🔴 Đang thoát...
    timeout /t 2 >nul
    exit
) else (
    echo.
    echo ❌ Lựa chọn không hợp lệ! Vui lòng chọn từ 1-5.
    echo.
    pause
    goto main
)
