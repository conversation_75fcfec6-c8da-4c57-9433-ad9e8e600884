package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.*;
import com.example.doancuoikyjava.service.*;
import com.example.doancuoikyjava.service.EnhancedCourseService;
import com.example.doancuoikyjava.util.SceneManager;
import com.example.doancuoikyjava.util.EnhancedUIComponents;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.stage.Modality;
import javafx.stage.Stage;
import javafx.scene.layout.HBox;

import java.io.IOException;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;
import java.util.stream.Collectors;

public class TeacherScheduleController implements Initializable {
    
    @FXML private Label welcomeLabel;
    @FXML private Button backButton;
    @FXML private Button refreshBtn;
    @FXML private Button exportBtn;
    @FXML private Button printBtn;
    @FXML private Button saveNotesBtn;
    @FXML private Button clearNotesBtn;
    
    @FXML private Label totalCoursesLabel;
    @FXML private Label totalClassesLabel;
    @FXML private Label totalStudentsLabel;
    @FXML private Label currentTimeLabel;
    
    @FXML private TableView<ScheduleInfo> scheduleTableView;
    @FXML private TableColumn<ScheduleInfo, String> dayColumn;
    @FXML private TableColumn<ScheduleInfo, String> timeColumn;
    @FXML private TableColumn<ScheduleInfo, String> courseColumn;
    @FXML private TableColumn<ScheduleInfo, String> classroomColumn;
    @FXML private TableColumn<ScheduleInfo, Integer> studentsCountColumn;
    @FXML private TableColumn<ScheduleInfo, Integer> creditsColumn;
    @FXML private TableColumn<ScheduleInfo, String> statusColumn;
    @FXML private TableColumn<ScheduleInfo, Void> actionsColumn;
    
    @FXML private ListView<String> todayScheduleListView;
    @FXML private ListView<String> nextWeekScheduleListView;
    @FXML private TextArea notesTextArea;
    
    private CourseService courseService;
    private EnhancedCourseService enhancedCourseService;
    private ScheduleService scheduleService;
    private Teacher currentTeacher;
    private ObservableList<ScheduleInfo> scheduleData;
    private boolean useEnhancedFeatures = true;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        System.out.println("🔧 TeacherScheduleController: Initializing...");

        try {
            // Initialize services
            courseService = new CourseService();
            scheduleService = new ScheduleService();
            if (useEnhancedFeatures) {
                enhancedCourseService = new EnhancedCourseService();
                System.out.println("🚀 Using Enhanced Course Service");
            }

            scheduleData = FXCollections.observableArrayList();

            setupCurrentTeacher();
            setupWelcomeMessage();
            setupTableColumns();
            setupEnhancedUI();
            loadData();
            updateCurrentTime();

            System.out.println("✅ TeacherScheduleController: Initialization completed successfully");
        } catch (Exception e) {
            System.err.println("❌ TeacherScheduleController: Error during initialization: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void setupCurrentTeacher() {
        System.out.println("👤 TeacherScheduleController: Setting up current teacher...");
        User currentUser = SceneManager.getCurrentUser();

        if (currentUser == null) {
            System.err.println("❌ TeacherScheduleController: Current user is null!");
            return;
        }

        System.out.println("👤 Current user: " + currentUser.getFullName() + " (Role: " + currentUser.getRole() + ")");

        if (currentUser instanceof Teacher) {
            currentTeacher = (Teacher) currentUser;
            System.out.println("✅ TeacherScheduleController: Current teacher set: " + currentTeacher.getFullName() + " (ID: " + currentTeacher.getTeacherId() + ")");
        } else {
            System.err.println("❌ TeacherScheduleController: Current user is not a Teacher!");
        }
    }
    
    private void setupWelcomeMessage() {
        if (currentTeacher != null) {
            welcomeLabel.setText("Xin chào, " + currentTeacher.getFullName());
        }
    }
    
    private void setupTableColumns() {
        dayColumn.setCellValueFactory(new PropertyValueFactory<>("day"));
        timeColumn.setCellValueFactory(new PropertyValueFactory<>("time"));
        courseColumn.setCellValueFactory(new PropertyValueFactory<>("courseName"));
        classroomColumn.setCellValueFactory(new PropertyValueFactory<>("classroom"));
        studentsCountColumn.setCellValueFactory(new PropertyValueFactory<>("studentsCount"));
        creditsColumn.setCellValueFactory(new PropertyValueFactory<>("credits"));
        statusColumn.setCellValueFactory(new PropertyValueFactory<>("status"));
        
        // Actions column with buttons
        actionsColumn.setCellFactory(param -> new TableCell<ScheduleInfo, Void>() {
            private final Button viewBtn = new Button("Xem");
            private final Button editBtn = new Button("Sửa");
            private final HBox buttons = new HBox(5, viewBtn, editBtn);
            
            {
                viewBtn.setStyle("-fx-background-color: #2196F3; -fx-text-fill: white; -fx-font-size: 10px;");
                editBtn.setStyle("-fx-background-color: #FF9800; -fx-text-fill: white; -fx-font-size: 10px;");
                
                viewBtn.setOnAction(e -> {
                    ScheduleInfo info = getTableView().getItems().get(getIndex());
                    showScheduleDetails(info);
                });
                
                editBtn.setOnAction(e -> {
                    ScheduleInfo info = getTableView().getItems().get(getIndex());
                    editSchedule(info);
                });
            }
            
            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(buttons);
                }
            }
        });
        
        scheduleTableView.setItems(scheduleData);
    }

    /**
     * Setup enhanced UI components and styling
     */
    private void setupEnhancedUI() {
        try {
            System.out.println("🎨 Setting up enhanced UI...");

            // Apply enhanced styling to buttons
            if (refreshBtn != null) {
                refreshBtn.getStyleClass().addAll("enhanced-button", "primary");
                EnhancedUIComponents.addHoverAnimation(refreshBtn);
            }

            if (exportBtn != null) {
                exportBtn.getStyleClass().addAll("enhanced-button", "success");
                EnhancedUIComponents.addHoverAnimation(exportBtn);
            }

            if (printBtn != null) {
                printBtn.getStyleClass().addAll("enhanced-button", "warning");
                EnhancedUIComponents.addHoverAnimation(printBtn);
            }

            if (saveNotesBtn != null) {
                saveNotesBtn.getStyleClass().addAll("enhanced-button", "primary");
                EnhancedUIComponents.addHoverAnimation(saveNotesBtn);
            }

            if (clearNotesBtn != null) {
                clearNotesBtn.getStyleClass().addAll("enhanced-button", "secondary");
                EnhancedUIComponents.addHoverAnimation(clearNotesBtn);
            }

            // Apply enhanced styling to table
            if (scheduleTableView != null) {
                scheduleTableView.getStyleClass().add("enhanced-table");
            }

            // Apply enhanced styling to text area
            if (notesTextArea != null) {
                notesTextArea.getStyleClass().add("enhanced-textfield");
            }

            System.out.println("✅ Enhanced UI setup completed");

        } catch (Exception e) {
            System.err.println("❌ Error setting up enhanced UI: " + e.getMessage());
        }
    }
    
    private void loadData() {
        if (currentTeacher == null) {
            System.out.println("❌ TeacherScheduleController: currentTeacher is null");
            return;
        }

        System.out.println("📅 TeacherScheduleController: Loading data for teacher: " + currentTeacher.getFullName() + " (ID: " + currentTeacher.getTeacherId() + ")");

        // Load courses taught by this teacher using enhanced service if available
        List<Course> teacherCourses;
        if (useEnhancedFeatures && enhancedCourseService != null) {
            teacherCourses = enhancedCourseService.getCoursesByTeacher(currentTeacher.getTeacherId());
            System.out.println("🚀 Enhanced: Found " + teacherCourses.size() + " courses for teacher");
        } else {
            teacherCourses = courseService.getCoursesByTeacher(currentTeacher.getTeacherId());
            System.out.println("📚 Standard: Found " + teacherCourses.size() + " courses for teacher");
        }

        // Load detailed schedules for teacher
        List<CourseSchedule> detailedSchedules = scheduleService.getSchedulesByTeacher(currentTeacher.getTeacherId());
        System.out.println("📅 Found " + detailedSchedules.size() + " detailed schedules for teacher");

        // Update statistics
        totalCoursesLabel.setText(String.valueOf(teacherCourses.size()));

        int totalStudents = teacherCourses.stream()
                .mapToInt(course -> course.getEnrolledStudents().size())
                .sum();
        totalStudentsLabel.setText(String.valueOf(totalStudents));

        // Calculate total classes per week (assuming each course has 3 classes per week)
        int totalClasses = teacherCourses.size() * 3;
        totalClassesLabel.setText(String.valueOf(totalClasses));
        
        // Load schedule data
        scheduleData.clear();

        // First try to use detailed schedules
        if (!detailedSchedules.isEmpty()) {
            System.out.println("📅 Using detailed schedules from CourseSchedule table");
            for (CourseSchedule schedule : detailedSchedules) {
                if (schedule.isActive()) {
                    ScheduleInfo info = new ScheduleInfo();
                    info.setDay(schedule.getDayOfWeekText());
                    info.setTime(schedule.getTimeRange());
                    info.setCourseName(schedule.getCourseName());
                    info.setClassroom(schedule.getClassroom());
                    info.setCredits(0); // Will be set from course if available
                    info.setStatus("Đang diễn ra");

                    // Get additional info from course if available
                    Course course = teacherCourses.stream()
                            .filter(c -> c.getCourseId().equals(schedule.getCourseId()))
                            .findFirst()
                            .orElse(null);

                    if (course != null) {
                        info.setStudentsCount(course.getEnrolledStudents().size());
                        info.setCredits(course.getCredits());
                    } else {
                        info.setStudentsCount(0);
                    }

                    scheduleData.add(info);
                    System.out.println("✅ Added detailed schedule: " + info.getDay() + " - " + info.getTime() + " - " + info.getCourseName());
                }
            }
        } else {
            // Fallback to parsing from course schedule strings
            System.out.println("📋 No detailed schedules found, using course schedule strings");
            for (Course course : teacherCourses) {
                System.out.println("📋 Processing course: " + course.getCourseName() + " - Schedule: " + course.getSchedule());

                // Parse schedule string and create schedule entries
                if (course.getSchedule() != null && !course.getSchedule().trim().isEmpty()) {
                    String[] scheduleParts = course.getSchedule().split(" - ");
                    System.out.println("📋 Schedule parts: " + java.util.Arrays.toString(scheduleParts));

                    if (scheduleParts.length >= 2) {
                        String daysString = scheduleParts[0];
                        String time = scheduleParts[1];

                        // Split multiple days (e.g., "Thứ 2, 4, 6" -> ["Thứ 2", "4", "6"])
                        String[] individualDays = daysString.split(",\\s*");

                        for (String day : individualDays) {
                            day = day.trim();

                            // Normalize day format
                            if (day.matches("\\d+")) {
                                // If it's just a number, add "Thứ " prefix
                                day = "Thứ " + day;
                            }

                            ScheduleInfo info = new ScheduleInfo();
                            info.setDay(day);
                            info.setTime(time);
                            info.setCourseName(course.getCourseName());
                            info.setClassroom(course.getClassroom() != null ? course.getClassroom() : "TBA");
                            info.setStudentsCount(course.getEnrolledStudents().size());
                            info.setCredits(course.getCredits());
                            info.setStatus("Đang diễn ra");

                            scheduleData.add(info);
                            System.out.println("✅ Added schedule info: " + day + " - " + time + " - " + course.getCourseName());
                        }
                    } else {
                        System.out.println("⚠️ Invalid schedule format for course: " + course.getCourseName());
                        // Add course without specific schedule
                        ScheduleInfo info = new ScheduleInfo();
                        info.setDay("TBA");
                        info.setTime("TBA");
                        info.setCourseName(course.getCourseName());
                        info.setClassroom(course.getClassroom() != null ? course.getClassroom() : "TBA");
                        info.setStudentsCount(course.getEnrolledStudents().size());
                        info.setCredits(course.getCredits());
                        info.setStatus("Chưa xếp lịch");
                        scheduleData.add(info);
                    }
                } else {
                    System.out.println("⚠️ Empty schedule for course: " + course.getCourseName());
                    // Add course without schedule
                    ScheduleInfo info = new ScheduleInfo();
                    info.setDay("TBA");
                    info.setTime("TBA");
                    info.setCourseName(course.getCourseName());
                    info.setClassroom(course.getClassroom() != null ? course.getClassroom() : "TBA");
                    info.setStudentsCount(course.getEnrolledStudents().size());
                    info.setCredits(course.getCredits());
                    info.setStatus("Chưa xếp lịch");
                    scheduleData.add(info);
                }
            }
        }

        // If still no data, create sample data for testing
        if (scheduleData.isEmpty()) {
            System.out.println("⚠️ No schedule data found, creating sample data for testing");
            createSampleScheduleData();
        }

        System.out.println("📊 Total schedule entries: " + scheduleData.size());
        
        loadTodaySchedule();
        loadNextWeekSchedule();
    }

    /**
     * Create sample schedule data for testing when no real data is available
     */
    private void createSampleScheduleData() {
        System.out.println("🔧 Creating sample schedule data for teacher: " + currentTeacher.getFullName());

        // Sample schedule entries
        String[][] sampleSchedules = {
            {"Thứ 2", "07:30 - 09:30", "Lập trình Java", "Lab A101", "25", "3"},
            {"Thứ 2", "13:30 - 15:30", "Cơ sở dữ liệu", "A201", "30", "3"},
            {"Thứ 4", "09:45 - 11:45", "Lập trình Java", "Lab A101", "25", "3"},
            {"Thứ 4", "13:30 - 15:30", "Thiết kế web", "Lab B102", "28", "2"},
            {"Thứ 6", "07:30 - 09:30", "Cơ sở dữ liệu", "A201", "30", "3"}
        };

        for (String[] schedule : sampleSchedules) {
            ScheduleInfo info = new ScheduleInfo();
            info.setDay(schedule[0]);
            info.setTime(schedule[1]);
            info.setCourseName(schedule[2]);
            info.setClassroom(schedule[3]);
            info.setStudentsCount(Integer.parseInt(schedule[4]));
            info.setCredits(Integer.parseInt(schedule[5]));
            info.setStatus("Đang diễn ra");

            scheduleData.add(info);
            System.out.println("✅ Added sample schedule: " + info.getDay() + " - " + info.getTime() + " - " + info.getCourseName());
        }
    }
    
    private void loadTodaySchedule() {
        ObservableList<String> todaySchedule = FXCollections.observableArrayList();

        if (currentTeacher != null) {
            // Get today's day of week (2=Monday, 3=Tuesday, etc.)
            int todayDayOfWeek = LocalDateTime.now().getDayOfWeek().getValue() + 1; // Convert to our format
            if (todayDayOfWeek == 8) todayDayOfWeek = 8; // Sunday

            // Load detailed schedules for today
            List<CourseSchedule> detailedSchedules = scheduleService.getSchedulesByTeacher(currentTeacher.getTeacherId());

            for (CourseSchedule schedule : detailedSchedules) {
                if (schedule.getDayOfWeek() == todayDayOfWeek && schedule.isActive()) {
                    String scheduleText = schedule.getTimeRange() + ": " +
                                        schedule.getCourseName() + " - " +
                                        (schedule.getClassroom() != null ? schedule.getClassroom() : "Chưa xếp phòng");
                    todaySchedule.add(scheduleText);
                }
            }

            // If no detailed schedules, use fallback from schedule data
            if (todaySchedule.isEmpty()) {
                String todayText = getTodayText();
                for (ScheduleInfo info : scheduleData) {
                    if (info.getDay().equals(todayText)) {
                        String scheduleText = info.getTime() + ": " + info.getCourseName() + " - " + info.getClassroom();
                        todaySchedule.add(scheduleText);
                    }
                }
            }
        }

        if (todaySchedule.isEmpty()) {
            todaySchedule.add("Không có lịch dạy hôm nay");
        }

        todayScheduleListView.setItems(todaySchedule);
    }
    
    private void loadNextWeekSchedule() {
        ObservableList<String> nextWeekSchedule = FXCollections.observableArrayList();

        if (currentTeacher != null) {
            // Load detailed schedules for the week
            List<CourseSchedule> detailedSchedules = scheduleService.getSchedulesByTeacher(currentTeacher.getTeacherId());

            // Group by day of week
            Map<Integer, List<CourseSchedule>> weeklySchedule = detailedSchedules.stream()
                .filter(CourseSchedule::isActive)
                .collect(Collectors.groupingBy(CourseSchedule::getDayOfWeek));

            // Add schedules for each day
            for (int day = 2; day <= 8; day++) { // Monday to Sunday
                List<CourseSchedule> daySchedules = weeklySchedule.get(day);
                if (daySchedules != null && !daySchedules.isEmpty()) {
                    for (CourseSchedule schedule : daySchedules) {
                        String scheduleText = schedule.getDayOfWeekText() + ": " +
                                            schedule.getCourseName() + " (" +
                                            schedule.getTimeRange() + ")";
                        nextWeekSchedule.add(scheduleText);
                    }
                }
            }

            // If no detailed schedules, use fallback
            if (nextWeekSchedule.isEmpty()) {
                for (ScheduleInfo info : scheduleData) {
                    String scheduleText = info.getDay() + ": " + info.getCourseName() + " (" + info.getTime() + ")";
                    nextWeekSchedule.add(scheduleText);
                }
            }
        }

        if (nextWeekSchedule.isEmpty()) {
            nextWeekSchedule.add("Không có lịch dạy trong tuần");
        }

        nextWeekScheduleListView.setItems(nextWeekSchedule);
    }

    private String getTodayText() {
        int dayOfWeek = LocalDateTime.now().getDayOfWeek().getValue();
        return switch (dayOfWeek) {
            case 1 -> "Thứ 2";
            case 2 -> "Thứ 3";
            case 3 -> "Thứ 4";
            case 4 -> "Thứ 5";
            case 5 -> "Thứ 6";
            case 6 -> "Thứ 7";
            case 7 -> "Chủ nhật";
            default -> "Không xác định";
        };
    }
    
    private void updateCurrentTime() {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");
        currentTimeLabel.setText("Thời gian hiện tại: " + now.format(formatter));
    }
    
    private void showScheduleDetails(ScheduleInfo info) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Chi tiết lịch dạy");
        alert.setHeaderText("Thông tin chi tiết: " + info.getCourseName());
        
        StringBuilder content = new StringBuilder();
        content.append("Môn học: ").append(info.getCourseName()).append("\n");
        content.append("Thời gian: ").append(info.getDay()).append(" - ").append(info.getTime()).append("\n");
        content.append("Phòng học: ").append(info.getClassroom()).append("\n");
        content.append("Số sinh viên: ").append(info.getStudentsCount()).append("\n");
        content.append("Số tín chỉ: ").append(info.getCredits()).append("\n");
        content.append("Trạng thái: ").append(info.getStatus()).append("\n");
        
        alert.setContentText(content.toString());
        alert.showAndWait();
    }
    
    private void editSchedule(ScheduleInfo info) {
        try {
            // Load the edit dialog
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/com/example/doancuoikyjava/edit-teacher-schedule-dialog.fxml"));
            Parent root = loader.load();

            EditTeacherScheduleDialogController controller = loader.getController();

            // Try to find corresponding CourseSchedule for more detailed editing
            CourseSchedule courseSchedule = null;
            if (currentTeacher != null) {
                List<CourseSchedule> detailedSchedules = scheduleService.getSchedulesByTeacher(currentTeacher.getTeacherId());
                courseSchedule = detailedSchedules.stream()
                        .filter(cs -> cs.getCourseName().equals(info.getCourseName()) &&
                                     cs.getDayOfWeekText().equals(info.getDay()) &&
                                     cs.getTimeRange().equals(info.getTime()))
                        .findFirst()
                        .orElse(null);
            }

            if (courseSchedule != null) {
                // Use detailed CourseSchedule for editing
                controller.setCourseSchedule(courseSchedule);
                System.out.println("✅ Using CourseSchedule for editing: " + courseSchedule.getCourseName());
            } else {
                // Fallback to ScheduleInfo
                controller.setScheduleInfo(info);
                System.out.println("⚠️ Using ScheduleInfo fallback for editing: " + info.getCourseName());
            }

            // Create and show dialog
            Stage dialogStage = new Stage();
            dialogStage.setTitle("Chỉnh sửa lịch dạy - " + info.getCourseName());
            dialogStage.initModality(Modality.WINDOW_MODAL);
            dialogStage.initOwner(scheduleTableView.getScene().getWindow());
            dialogStage.setScene(new Scene(root));
            dialogStage.setResizable(false);

            dialogStage.showAndWait();

            // Refresh data if schedule was saved
            if (controller.isSaved()) {
                loadData();
                showAlert("Thành công", "Lịch dạy đã được cập nhật thành công!");
            }

        } catch (Exception e) {
            System.err.println("❌ Error opening edit dialog: " + e.getMessage());
            e.printStackTrace();
            showAlert("Lỗi", "Không thể mở dialog chỉnh sửa: " + e.getMessage());
        }
    }
    
    @FXML
    private void goBack() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/teacher-dashboard.fxml", 
                                   "Giáo viên - " + SceneManager.getCurrentUser().getFullName());
        } catch (IOException e) {
            showAlert("Lỗi", "Không thể quay lại trang chính: " + e.getMessage());
        }
    }
    
    @FXML
    private void refreshData() {
        loadData();
        updateCurrentTime();
        showAlert("Thành công", "Dữ liệu đã được làm mới!");
    }
    
    @FXML
    private void saveNotes() {
        String notes = notesTextArea.getText();
        if (notes.trim().isEmpty()) {
            showAlert("Thông báo", "Vui lòng nhập ghi chú trước khi lưu.");
            return;
        }
        
        // In a real application, save notes to database or file
        showAlert("Thành công", "Ghi chú đã được lưu!");
    }
    
    @FXML
    private void clearNotes() {
        notesTextArea.clear();
        showAlert("Thành công", "Ghi chú đã được xóa!");
    }
    
    @FXML
    private void exportSchedule() {
        StringBuilder report = new StringBuilder();
        report.append("LỊCH GIẢNG DẠY\n");
        report.append("==============\n\n");
        
        if (currentTeacher != null) {
            report.append("Giáo viên: ").append(currentTeacher.getFullName()).append("\n");
            report.append("Khoa: ").append(currentTeacher.getDepartment()).append("\n");
            report.append("Chức vụ: ").append(currentTeacher.getPosition()).append("\n\n");
        }
        
        report.append("LỊCH DẠY TRONG TUẦN:\n");
        report.append("Thứ\tGiờ\t\tMôn học\t\tPhòng\tSV\n");
        report.append("------------------------------------------------\n");
        
        for (ScheduleInfo info : scheduleData) {
            report.append(info.getDay()).append("\t");
            report.append(info.getTime()).append("\t");
            report.append(info.getCourseName()).append("\t");
            report.append(info.getClassroom()).append("\t");
            report.append(info.getStudentsCount()).append("\n");
        }
        
        // Show report in a dialog
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Lịch giảng dạy");
        alert.setHeaderText("Lịch giảng dạy của " + (currentTeacher != null ? currentTeacher.getFullName() : "giáo viên"));
        
        TextArea textArea = new TextArea(report.toString());
        textArea.setEditable(false);
        textArea.setWrapText(true);
        textArea.setPrefSize(600, 400);
        
        alert.getDialogPane().setContent(textArea);
        alert.showAndWait();
    }
    
    @FXML
    private void printSchedule() {
        showAlert("Thông báo", "Tính năng in lịch dạy đang được phát triển!");
    }
    
    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
}
