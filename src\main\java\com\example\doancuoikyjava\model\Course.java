package com.example.doancuoikyjava.model;

import java.util.ArrayList;
import java.util.List;

public class Course {
    private String courseId;
    private String courseName;
    private String description;
    private int credits;
    private String teacherId;
    private String teacherName;
    private List<String> enrolledStudents;
    private String schedule;
    private String classroom;
    private int maxStudents;

    public Course() {
        this.enrolledStudents = new ArrayList<>();
    }

    public Course(String courseId, String courseName, String description, int credits,
                  String teacherId, String schedule, String classroom, int maxStudents) {
        this.courseId = courseId;
        this.courseName = courseName;
        this.description = description;
        this.credits = credits;
        this.teacherId = teacherId;
        this.schedule = schedule;
        this.classroom = classroom;
        this.maxStudents = maxStudents;
        this.enrolledStudents = new ArrayList<>();
    }

    // Getters and Setters
    public String getCourseId() { return courseId; }
    public void setCourseId(String courseId) { this.courseId = courseId; }

    public String getCourseName() { return courseName; }
    public void setCourseName(String courseName) { this.courseName = courseName; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public int getCredits() { return credits; }
    public void setCredits(int credits) { this.credits = credits; }

    public String getTeacherId() { return teacherId; }
    public void setTeacherId(String teacherId) { this.teacherId = teacherId; }

    public String getTeacherName() { return teacherName; }
    public void setTeacherName(String teacherName) { this.teacherName = teacherName; }

    public List<String> getEnrolledStudents() { return enrolledStudents; }
    public void setEnrolledStudents(List<String> enrolledStudents) { this.enrolledStudents = enrolledStudents; }

    public String getSchedule() { return schedule; }
    public void setSchedule(String schedule) { this.schedule = schedule; }

    public String getClassroom() { return classroom; }
    public void setClassroom(String classroom) { this.classroom = classroom; }

    public int getMaxStudents() { return maxStudents; }
    public void setMaxStudents(int maxStudents) { this.maxStudents = maxStudents; }

    // Utility methods
    public boolean addStudent(String studentId) {
        if (enrolledStudents.size() < maxStudents && !enrolledStudents.contains(studentId)) {
            enrolledStudents.add(studentId);
            return true;
        }
        return false;
    }

    public boolean removeStudent(String studentId) {
        return enrolledStudents.remove(studentId);
    }

    public int getAvailableSlots() {
        return maxStudents - enrolledStudents.size();
    }

    public boolean isFull() {
        return enrolledStudents.size() >= maxStudents;
    }

    @Override
    public String toString() {
        return "Course{" +
                "courseId='" + courseId + '\'' +
                ", courseName='" + courseName + '\'' +
                ", credits=" + credits +
                ", teacherName='" + teacherName + '\'' +
                ", enrolledStudents=" + enrolledStudents.size() + "/" + maxStudents +
                '}';
    }
}
