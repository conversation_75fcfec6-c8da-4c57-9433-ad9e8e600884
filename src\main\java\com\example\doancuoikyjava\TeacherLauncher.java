// IMPORTANT: If you see "JavaFX runtime components are missing", you must add VM options:
// --module-path "path\to\javafx-sdk-XX\lib" --add-modules javafx.controls,javafx.fxml
// Replace "path\to\javafx-sdk-XX\lib" with the path to your JavaFX SDK lib folder.
// In IntelliJ: Run > Edit Configurations > VM options
// In command line: java --module-path ... --add-modules ...
package com.example.doancuoikyjava;

import com.example.doancuoikyjava.model.Teacher;
import com.example.doancuoikyjava.model.User;
import com.example.doancuoikyjava.service.UserService;
import com.example.doancuoikyjava.util.SceneManager;
import javafx.application.Application;
import javafx.stage.Stage;

/**
 * Teacher Launcher - Direct access to Teacher Dashboard
 */
public class TeacherLauncher extends Application {

    private UserService userService;

    @Override
    public void start(Stage primaryStage) {
        try {
            this.userService = new UserService();
            
            // Initialize default teacher if not exists
            initializeDefaultTeacher();
            
            // Get teacher user
            User teacher = userService.getUserById("GV001");
            if (teacher == null) {
                System.err.println("❌ Could not find or create teacher user!");
                return;
            }

            // Set up SceneManager
            SceneManager.setPrimaryStage(primaryStage);
            SceneManager.setCurrentUser(teacher);
            
            // Launch teacher dashboard directly
            SceneManager.switchScene("/com/example/doancuoikyjava/teacher-dashboard.fxml", 
                                   "👨‍🏫 TEACHER DASHBOARD - Hệ thống quản lý giảng dạy");
            
            System.out.println("👨‍🏫 Teacher Dashboard launched successfully!");
            System.out.println("👤 Logged in as: " + teacher.getFullName());
            
        } catch (Exception e) {
            System.err.println("❌ Error launching Teacher Dashboard: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void initializeDefaultTeacher() {
        try {
            if (userService.getUserById("GV001") == null) {
                Teacher teacher = new Teacher();
                teacher.setUserId("GV001");
                teacher.setUsername("teacher");
                teacher.setPassword("teacher123");
                teacher.setFullName("Nguyễn Văn Giáo");
                teacher.setEmail("<EMAIL>");
                teacher.setPhone("0987654321");
                teacher.setRole(User.UserRole.TEACHER);
                teacher.setTeacherId("GV001");
                teacher.setDepartment("Khoa Khoa học máy tính");
                teacher.setPosition("Giảng viên");
                teacher.setQualification("Thạc sĩ Khoa học máy tính");
                teacher.setExperienceYears(5);
                
                userService.addUser(teacher);
                System.out.println("✅ Created default teacher user: teacher/teacher123");
            }
        } catch (Exception e) {
            System.err.println("❌ Error creating default teacher: " + e.getMessage());
        }
    }

    public static void main(String[] args) {
        // NOTE: If you use Java 11+ and JavaFX SDK, add VM options:
        // --module-path "path\to\javafx-sdk-XX\lib" --add-modules javafx.controls,javafx.fxml
        // Replace "path\to\javafx-sdk-XX\lib" with your JavaFX SDK lib folder.
        // Example for IntelliJ IDEA: Run/Debug Configurations > VM options
        System.out.println("👨‍🏫 Starting Teacher Dashboard");
        System.out.println("════════════════════════════════════════");
        System.out.println("👤 Auto-login as Teacher");
        System.out.println("🎯 Direct access to teaching functions");
        System.out.println("════════════════════════════════════════");
        
        launch(args);
    }
}