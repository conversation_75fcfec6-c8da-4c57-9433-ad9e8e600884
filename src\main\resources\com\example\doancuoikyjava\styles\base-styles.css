/* Base Styles - Common styles for all themes */

/* Root variables will be overridden by theme-specific CSS */
.root {
    -fx-font-family: "Segoe UI", "Roboto", "Arial", sans-serif;
    -fx-font-size: 14px;
}

/* Enhanced <PERSON><PERSON> Styles */
.enhanced-button {
    -fx-background-radius: 8px;
    -fx-padding: 12px 24px;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 4, 0, 0, 2);
    -fx-border-width: 0;
}

.enhanced-button:hover {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 6, 0, 0, 3);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

.enhanced-button:pressed {
    -fx-scale-x: 0.9;
    -fx-scale-y: 0.9;
}

.enhanced-button.primary {
    -fx-background-color: #2196F3;
    -fx-text-fill: white;
}

.enhanced-button.secondary {
    -fx-background-color: #FFC107;
    -fx-text-fill: #212121;
}

.enhanced-button.success {
    -fx-background-color: #4CAF50;
    -fx-text-fill: white;
}

.enhanced-button.warning {
    -fx-background-color: #FF9800;
    -fx-text-fill: white;
}

.enhanced-button.error {
    -fx-background-color: #F44336;
    -fx-text-fill: white;
}



/* Enhanced Card Styles */
.enhanced-card {
    -fx-background-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 8, 0, 0, 4);
    -fx-padding: 20px;
}

.enhanced-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 12, 0, 0, 6);
}

/* Colorful Card Variants */
.enhanced-card.rainbow {
    -fx-background-color: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -fx-text-fill: white;
}

.enhanced-card.ocean {
    -fx-background-color: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    -fx-text-fill: white;
}

.enhanced-card.sunset {
    -fx-background-color: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    -fx-text-fill: white;
}

.enhanced-card.forest {
    -fx-background-color: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    -fx-text-fill: white;
}

/* Enhanced Text Field Styles */
.enhanced-textfield {
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-border-width: 2px;
    -fx-padding: 12px;
    -fx-font-size: 14px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 2, 0, 0, 1);
}

.enhanced-textfield:focused {
    -fx-border-color: #2196F3;
    -fx-effect: dropshadow(gaussian, rgba(33,150,243,0.3), 4, 0, 0, 2);
}

/* Enhanced ComboBox Styles */
.enhanced-combobox {
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-border-width: 2px;
    -fx-padding: 8px;
    -fx-font-size: 14px;
}

.enhanced-combobox:focused {
    -fx-border-color: #2196F3;
}

/* Enhanced Table Styles */
.enhanced-table {
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-border-width: 1px;
}

.enhanced-table .column-header {
    -fx-background-color: #F5F5F5;
    -fx-font-weight: bold;
    -fx-padding: 12px;
}

.enhanced-table .table-row-cell {
    -fx-padding: 8px;
}

.enhanced-table .table-row-cell:hover {
    -fx-background-color: rgba(33,150,243,0.1);
}

.enhanced-table .table-row-cell:selected {
    -fx-background-color: rgba(33,150,243,0.2);
}

/* Statistics Card Styles - Rainbow */
.stat-card {
    -fx-background-radius: 20px;
    -fx-effect: dropshadow(gaussian, rgba(102,126,234,0.4), 12, 0, 0, 6);
    -fx-padding: 25px;
    -fx-background-color: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -fx-text-fill: white;
}

.stat-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(102,126,234,0.7), 20, 0, 0, 12);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
    -fx-background-color: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

/* Different colored stat cards */
.stat-card.blue {
    -fx-background-color: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card.green {
    -fx-background-color: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-card.orange {
    -fx-background-color: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stat-card.purple {
    -fx-background-color: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    -fx-text-fill: #2c3e50;
}

/* Notification Toast Styles */
.notification-toast {
    -fx-background-radius: 25px;
    -fx-padding: 12px 20px;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 6, 0, 0, 3);
}

.notification-toast.success {
    -fx-background-color: #4CAF50;
    -fx-text-fill: white;
}

.notification-toast.warning {
    -fx-background-color: #FF9800;
    -fx-text-fill: white;
}

.notification-toast.error {
    -fx-background-color: #F44336;
    -fx-text-fill: white;
}

.notification-toast.info {
    -fx-background-color: #2196F3;
    -fx-text-fill: white;
}

/* Enhanced Progress Bar */
.enhanced-progress {
    -fx-background-radius: 10px;
    -fx-background-insets: 0;
    -fx-accent: #2196F3;
}

.enhanced-progress .bar {
    -fx-background-radius: 10px;
    -fx-background-insets: 0;
}

/* Enhanced Dialog Styles */
.enhanced-dialog {
    -fx-background-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 15, 0, 0, 8);
}

.enhanced-dialog .header-panel {
    -fx-background-radius: 12px 12px 0 0;
}

.enhanced-dialog .button-bar {
    -fx-padding: 15px;
}

/* Sidebar Styles */
.sidebar {
    -fx-padding: 20px;
    -fx-spacing: 10px;
}

.sidebar-button {
    -fx-background-radius: 8px;
    -fx-padding: 12px 16px;
    -fx-font-size: 14px;
    -fx-cursor: hand;
    -fx-alignment: center-left;
    -fx-min-width: 200px;
}

.sidebar-button:hover {
    -fx-background-color: rgba(33,150,243,0.1);
}

.sidebar-button.active {
    -fx-background-color: #2196F3;
    -fx-text-fill: white;
}

/* Content Container Styles */
.content-container {
    -fx-padding: 20px;
    -fx-spacing: 20px;
}

.dashboard-container {
    -fx-spacing: 20px;
    -fx-padding: 20px;
}

/* Form Styles */
.form-container {
    -fx-spacing: 15px;
    -fx-padding: 20px;
}

.form-label {
    -fx-font-weight: bold;
    -fx-font-size: 14px;
}

.form-field {
    -fx-spacing: 5px;
}

/* Header Styles */
.header-container {
    -fx-padding: 15px 20px;
    -fx-spacing: 15px;
    -fx-alignment: center-left;
}

.header-title {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
}

.header-subtitle {
    -fx-font-size: 16px;
}

/* Animation Classes - Enhanced */
.fade-in {
    -fx-opacity: 0;
}

.slide-in {
    -fx-translate-y: 20px;
}

/* Pulse Animation */
.pulse {
    -fx-effect: dropshadow(gaussian, rgba(102,126,234,0.6), 15, 0, 0, 0);
}

/* Glow Effect */
.glow {
    -fx-effect: dropshadow(gaussian, rgba(102,126,234,0.8), 20, 0, 0, 0);
}

/* Rainbow Border Animation */
.rainbow-border {
    -fx-border-width: 3px;
    -fx-border-color: linear-gradient(45deg, #ff0000, #ff7f00, #ffff00, #00ff00, #0000ff, #4b0082, #9400d3);
    -fx-border-radius: 15px;
}

/* Floating Animation */
.floating {
    -fx-translate-y: -5px;
    -fx-effect: dropshadow(gaussian, rgba(102,126,234,0.4), 10, 0, 0, 8);
}

/* Gradient Text Effect */
.gradient-text {
    -fx-fill: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
    -fx-font-weight: bold;
    -fx-font-size: 18px;
}

/* Neon Glow */
.neon-glow {
    -fx-effect: dropshadow(gaussian, #00f2fe, 15, 0, 0, 0);
    -fx-text-fill: #00f2fe;
}

/* Shimmer Effect */
.shimmer {
    -fx-background-color: linear-gradient(90deg,
        transparent 0%,
        rgba(255,255,255,0.4) 50%,
        transparent 100%);
}

/* Responsive Grid */
.responsive-grid {
    -fx-hgap: 20px;
    -fx-vgap: 20px;
    -fx-padding: 20px;
}

/* Loading Indicator */
.loading-container {
    -fx-alignment: center;
    -fx-spacing: 20px;
    -fx-padding: 40px;
}

.loading-text {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
}

/* Utility Classes */
.text-center {
    -fx-alignment: center;
}

.text-left {
    -fx-alignment: center-left;
}

.text-right {
    -fx-alignment: center-right;
}

.full-width {
    -fx-max-width: infinity;
}

.margin-top {
    -fx-padding: 10px 0 0 0;
}

.margin-bottom {
    -fx-padding: 0 0 10px 0;
}

.no-padding {
    -fx-padding: 0;
}

/* Scrollbar Styles */
.scroll-pane {
    -fx-background-color: transparent;
}

.scroll-pane .viewport {
    -fx-background-color: transparent;
}

.scroll-bar {
    -fx-background-color: transparent;
}

.scroll-bar .track {
    -fx-background-color: rgba(0,0,0,0.1);
    -fx-background-radius: 5px;
}

.scroll-bar .thumb {
    -fx-background-color: rgba(0,0,0,0.3);
    -fx-background-radius: 5px;
}

.scroll-bar .thumb:hover {
    -fx-background-color: rgba(0,0,0,0.5);
}
