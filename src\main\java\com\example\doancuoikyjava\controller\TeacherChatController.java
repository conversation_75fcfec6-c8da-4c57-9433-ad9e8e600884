package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.ChatMessage;
import com.example.doancuoikyjava.model.Student;
import com.example.doancuoikyjava.model.User;
import com.example.doancuoikyjava.service.ChatService;
import com.example.doancuoikyjava.util.SceneManager;
import com.example.doancuoikyjava.util.SessionManager;
import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.scene.paint.Color;
import javafx.scene.text.Font;
import javafx.scene.text.FontWeight;

import java.net.URL;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Controller for teacher chat functionality
 */
public class TeacherChatController implements Initializable {
    
    @FXML private Button backButton;
    @FXML private ListView<Student> studentListView;
    @FXML private VBox chatArea;
    @FXML private ScrollPane chatScrollPane;
    @FXML private TextField messageField;
    @FXML private Button sendButton;
    @FXML private Label chatHeaderLabel;
    @FXML private Label onlineStatusLabel;
    
    private ChatService chatService;
    private User currentUser;
    private Student selectedStudent;
    private List<ChatMessage> currentChatHistory;
    private ScheduledExecutorService refreshService;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        currentUser = SessionManager.getCurrentUser();
        if (currentUser == null) {
            System.err.println("❌ No current user in session!");
            return;
        }
        
        chatService = new ChatService();
        currentChatHistory = new ArrayList<>();
        
        setupUI();
        loadAvailableStudents();
        startRefreshService();
        
        System.out.println("💬 Teacher Chat Controller initialized for: " + currentUser.getFullName());
    }
    
    private void setupUI() {
        // Setup student list
        studentListView.setCellFactory(listView -> new StudentListCell());
        studentListView.getSelectionModel().selectedItemProperty().addListener(
            (obs, oldSelection, newSelection) -> {
                if (newSelection != null) {
                    selectStudent(newSelection);
                }
            }
        );
        
        // Setup message field
        messageField.setOnAction(e -> sendMessage());
        sendButton.setOnAction(e -> sendMessage());
        
        // Setup chat area
        chatArea.setSpacing(10);
        chatArea.setPadding(new Insets(10));
        chatScrollPane.setFitToWidth(true);
        chatScrollPane.vvalueProperty().bind(chatArea.heightProperty());
        
        // Initial state
        chatHeaderLabel.setText("Chọn sinh viên để bắt đầu chat");
        messageField.setDisable(true);
        sendButton.setDisable(true);
        onlineStatusLabel.setText("Offline");
        onlineStatusLabel.setTextFill(Color.GRAY);
    }
    
    private void loadAvailableStudents() {
        try {
            List<Student> students = chatService.getAvailableStudentsForTeacher(currentUser.getUserId());
            
            Platform.runLater(() -> {
                studentListView.getItems().clear();
                studentListView.getItems().addAll(students);
                
                if (students.isEmpty()) {
                    // Show message if no students available
                    Label noStudentsLabel = new Label("Không có sinh viên nào để chat.\nChưa có sinh viên trong các môn bạn dạy.");
                    noStudentsLabel.setStyle("-fx-text-fill: gray; -fx-font-style: italic;");
                    studentListView.setPlaceholder(noStudentsLabel);
                }
            });
            
            System.out.println("👨‍🎓 Loaded " + students.size() + " available students");
            
        } catch (Exception e) {
            System.err.println("❌ Error loading students: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void selectStudent(Student student) {
        selectedStudent = student;
        
        // Update UI
        chatHeaderLabel.setText("Chat với " + student.getFullName());
        messageField.setDisable(false);
        sendButton.setDisable(false);
        
        // Check online status
        updateOnlineStatus(student.getUserId());
        
        // Load chat history
        loadChatHistory();
        
        System.out.println("👨‍🎓 Selected student: " + student.getFullName());
    }
    
    private void loadChatHistory() {
        if (selectedStudent == null) return;
        
        try {
            currentChatHistory = chatService.getChatHistory(currentUser.getUserId(), selectedStudent.getUserId());
            
            Platform.runLater(() -> {
                chatArea.getChildren().clear();
                
                for (ChatMessage message : currentChatHistory) {
                    addMessageToChat(message);
                }
                
                // Scroll to bottom
                Platform.runLater(() -> chatScrollPane.setVvalue(1.0));
            });
            
            System.out.println("📜 Loaded " + currentChatHistory.size() + " messages");
            
        } catch (Exception e) {
            System.err.println("❌ Error loading chat history: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void addMessageToChat(ChatMessage message) {
        boolean isSentByMe = message.isSentByUser(currentUser.getUserId());
        
        // Create message bubble
        VBox messageBox = new VBox(5);
        messageBox.setPadding(new Insets(10));
        messageBox.setMaxWidth(300);
        
        // Message content
        Label contentLabel = new Label(message.getContent());
        contentLabel.setWrapText(true);
        contentLabel.setFont(Font.font("System", 14));
        
        // Timestamp
        Label timeLabel = new Label(message.getTimeOnly());
        timeLabel.setFont(Font.font("System", 10));
        timeLabel.setTextFill(Color.GRAY);
        
        messageBox.getChildren().addAll(contentLabel, timeLabel);
        
        // Style based on sender
        if (isSentByMe) {
            messageBox.setStyle("-fx-background-color: #28a745; -fx-background-radius: 15; -fx-padding: 10;");
            contentLabel.setTextFill(Color.WHITE);
            messageBox.setAlignment(Pos.CENTER_RIGHT);
            
            HBox container = new HBox();
            container.setAlignment(Pos.CENTER_RIGHT);
            container.getChildren().add(messageBox);
            chatArea.getChildren().add(container);
        } else {
            messageBox.setStyle("-fx-background-color: #f1f1f1; -fx-background-radius: 15; -fx-padding: 10;");
            contentLabel.setTextFill(Color.BLACK);
            messageBox.setAlignment(Pos.CENTER_LEFT);
            
            HBox container = new HBox();
            container.setAlignment(Pos.CENTER_LEFT);
            container.getChildren().add(messageBox);
            chatArea.getChildren().add(container);
        }
    }
    
    private void sendMessage() {
        if (selectedStudent == null || messageField.getText().trim().isEmpty()) {
            return;
        }
        
        String content = messageField.getText().trim();
        messageField.clear();
        
        try {
            // Create message
            ChatMessage message = new ChatMessage(
                currentUser.getUserId(),
                currentUser.getFullName(),
                selectedStudent.getUserId(),
                selectedStudent.getFullName(),
                content
            );
            
            // Save message to database
            boolean success = chatService.saveMessage(message);
            
            if (success) {
                // Add to local chat immediately
                currentChatHistory.add(message);
                Platform.runLater(() -> {
                    addMessageToChat(message);
                    Platform.runLater(() -> chatScrollPane.setVvalue(1.0));
                });
                
                System.out.println("📤 Message sent: " + content);
            } else {
                showAlert("Lỗi", "Không thể gửi tin nhắn. Vui lòng thử lại.");
            }
            
        } catch (Exception e) {
            System.err.println("❌ Error sending message: " + e.getMessage());
            e.printStackTrace();
            showAlert("Lỗi", "Không thể gửi tin nhắn: " + e.getMessage());
        }
    }
    
    private void startRefreshService() {
        refreshService = Executors.newSingleThreadScheduledExecutor();
        
        // Refresh chat every 3 seconds to check for new messages
        refreshService.scheduleAtFixedRate(() -> {
            if (selectedStudent != null) {
                refreshChatHistory();
            }
        }, 3, 3, TimeUnit.SECONDS);
        
        System.out.println("🔄 Teacher chat refresh service started");
    }
    
    private void refreshChatHistory() {
        if (selectedStudent == null) return;
        
        try {
            List<ChatMessage> latestHistory = chatService.getChatHistory(currentUser.getUserId(), selectedStudent.getUserId());
            
            // Check if there are new messages
            if (latestHistory.size() > currentChatHistory.size()) {
                Platform.runLater(() -> {
                    // Clear and reload all messages
                    chatArea.getChildren().clear();
                    currentChatHistory = latestHistory;
                    
                    for (ChatMessage message : currentChatHistory) {
                        addMessageToChat(message);
                    }
                    
                    // Scroll to bottom
                    Platform.runLater(() -> chatScrollPane.setVvalue(1.0));
                });
                
                System.out.println("🔄 Teacher chat refreshed - " + latestHistory.size() + " messages");
            }
            
        } catch (Exception e) {
            System.err.println("❌ Error refreshing teacher chat: " + e.getMessage());
        }
    }
    
    private void updateOnlineStatus(String userId) {
        // Simple implementation - show as available for chat
        Platform.runLater(() -> {
            onlineStatusLabel.setText("Có thể chat");
            onlineStatusLabel.setTextFill(Color.GREEN);
        });
    }
    
    private void showAlert(String title, String message) {
        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        });
    }
    
    // Custom cell for student list
    private static class StudentListCell extends ListCell<Student> {
        @Override
        protected void updateItem(Student student, boolean empty) {
            super.updateItem(student, empty);
            
            if (empty || student == null) {
                setText(null);
                setGraphic(null);
            } else {
                VBox content = new VBox(2);
                
                Label nameLabel = new Label(student.getFullName());
                nameLabel.setFont(Font.font("System", FontWeight.BOLD, 14));
                
                Label classLabel = new Label(student.getClassName() != null ? student.getClassName() : "Chưa có lớp");
                classLabel.setFont(Font.font("System", 12));
                classLabel.setTextFill(Color.GRAY);
                
                content.getChildren().addAll(nameLabel, classLabel);
                setGraphic(content);
            }
        }
    }
    
    @FXML
    private void goBackToHome() {
        try {
            // Stop refresh service before leaving
            cleanup();

            // Navigate back to teacher dashboard
            SceneManager.switchScene("/com/example/doancuoikyjava/teacher-dashboard.fxml",
                                   "Teacher Dashboard - " + currentUser.getFullName());

            System.out.println("🏠 Navigating back to Teacher Dashboard");

        } catch (Exception e) {
            System.err.println("❌ Error navigating back to home: " + e.getMessage());
            e.printStackTrace();
            showAlert("Lỗi", "Không thể quay lại trang chủ: " + e.getMessage());
        }
    }

    public void cleanup() {
        if (refreshService != null && !refreshService.isShutdown()) {
            refreshService.shutdown();
            System.out.println("🔄 Teacher chat refresh service stopped");
        }
    }
}
