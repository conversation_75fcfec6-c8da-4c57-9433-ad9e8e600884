package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.ChatMessage;
import com.example.doancuoikyjava.model.Teacher;
import com.example.doancuoikyjava.model.User;
import com.example.doancuoikyjava.service.ChatService;
import com.example.doancuoikyjava.util.SceneManager;
import com.example.doancuoikyjava.util.SessionManager;
import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.scene.paint.Color;
import javafx.scene.text.Font;
import javafx.scene.text.FontWeight;

import java.net.URL;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Controller for student chat functionality (Simple version without WebSocket)
 */
public class StudentChatController implements Initializable {
    
    @FXML private Button backButton;
    @FXML private ListView<Teacher> teacherListView;
    @FXML private VBox chatArea;
    @FXML private ScrollPane chatScrollPane;
    @FXML private TextField messageField;
    @FXML private Button sendButton;
    @FXML private Label chatHeaderLabel;
    @FXML private Label onlineStatusLabel;
    
    private ChatService chatService;
    private User currentUser;
    private Teacher selectedTeacher;
    private List<ChatMessage> currentChatHistory;
    private ScheduledExecutorService refreshService;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        currentUser = SessionManager.getCurrentUser();
        if (currentUser == null) {
            System.err.println("❌ No current user in session!");
            return;
        }

        chatService = new ChatService();
        currentChatHistory = new ArrayList<>();

        setupUI();
        loadAvailableTeachers();
        startRefreshService();

        System.out.println("💬 Student Chat Controller initialized for: " + currentUser.getFullName());
    }
    
    private void setupUI() {
        // Setup teacher list
        teacherListView.setCellFactory(listView -> new TeacherListCell());
        teacherListView.getSelectionModel().selectedItemProperty().addListener(
            (obs, oldSelection, newSelection) -> {
                if (newSelection != null) {
                    selectTeacher(newSelection);
                }
            }
        );
        
        // Setup message field
        messageField.setOnAction(e -> sendMessage());
        sendButton.setOnAction(e -> sendMessage());
        
        // Setup chat area
        chatArea.setSpacing(10);
        chatArea.setPadding(new Insets(10));
        chatScrollPane.setFitToWidth(true);
        chatScrollPane.vvalueProperty().bind(chatArea.heightProperty());
        
        // Initial state
        chatHeaderLabel.setText("Chọn giáo viên để bắt đầu chat");
        messageField.setDisable(true);
        sendButton.setDisable(true);
        onlineStatusLabel.setText("Offline");
        onlineStatusLabel.setTextFill(Color.GRAY);
    }
    
    private void loadAvailableTeachers() {
        try {
            List<Teacher> teachers = chatService.getAvailableTeachersForStudent(currentUser.getUserId());
            
            Platform.runLater(() -> {
                teacherListView.getItems().clear();
                teacherListView.getItems().addAll(teachers);
                
                if (teachers.isEmpty()) {
                    // Show message if no teachers available
                    Label noTeachersLabel = new Label("Không có giáo viên nào để chat.\nHãy đăng ký môn học trước.");
                    noTeachersLabel.setStyle("-fx-text-fill: gray; -fx-font-style: italic;");
                    teacherListView.setPlaceholder(noTeachersLabel);
                }
            });
            
            System.out.println("📚 Loaded " + teachers.size() + " available teachers");
            
        } catch (Exception e) {
            System.err.println("❌ Error loading teachers: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void selectTeacher(Teacher teacher) {
        selectedTeacher = teacher;
        
        // Update UI
        chatHeaderLabel.setText("Chat với " + teacher.getFullName());
        messageField.setDisable(false);
        sendButton.setDisable(false);
        
        // Check online status
        updateOnlineStatus(teacher.getUserId());
        
        // Load chat history
        loadChatHistory();
        
        System.out.println("👨‍🏫 Selected teacher: " + teacher.getFullName());
    }
    
    private void loadChatHistory() {
        if (selectedTeacher == null) return;
        
        try {
            currentChatHistory = chatService.getChatHistory(currentUser.getUserId(), selectedTeacher.getUserId());
            
            Platform.runLater(() -> {
                chatArea.getChildren().clear();
                
                for (ChatMessage message : currentChatHistory) {
                    addMessageToChat(message);
                }
                
                // Scroll to bottom
                Platform.runLater(() -> chatScrollPane.setVvalue(1.0));
            });
            
            System.out.println("📜 Loaded " + currentChatHistory.size() + " messages");
            
        } catch (Exception e) {
            System.err.println("❌ Error loading chat history: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void addMessageToChat(ChatMessage message) {
        boolean isSentByMe = message.isSentByUser(currentUser.getUserId());
        
        // Create message bubble
        VBox messageBox = new VBox(5);
        messageBox.setPadding(new Insets(10));
        messageBox.setMaxWidth(300);
        
        // Message content
        Label contentLabel = new Label(message.getContent());
        contentLabel.setWrapText(true);
        contentLabel.setFont(Font.font("System", 14));
        
        // Timestamp
        Label timeLabel = new Label(message.getTimeOnly());
        timeLabel.setFont(Font.font("System", 10));
        timeLabel.setTextFill(Color.GRAY);
        
        messageBox.getChildren().addAll(contentLabel, timeLabel);
        
        // Style based on sender
        if (isSentByMe) {
            messageBox.setStyle("-fx-background-color: #007bff; -fx-background-radius: 15; -fx-padding: 10;");
            contentLabel.setTextFill(Color.WHITE);
            messageBox.setAlignment(Pos.CENTER_RIGHT);
            
            HBox container = new HBox();
            container.setAlignment(Pos.CENTER_RIGHT);
            container.getChildren().add(messageBox);
            chatArea.getChildren().add(container);
        } else {
            messageBox.setStyle("-fx-background-color: #f1f1f1; -fx-background-radius: 15; -fx-padding: 10;");
            contentLabel.setTextFill(Color.BLACK);
            messageBox.setAlignment(Pos.CENTER_LEFT);
            
            HBox container = new HBox();
            container.setAlignment(Pos.CENTER_LEFT);
            container.getChildren().add(messageBox);
            chatArea.getChildren().add(container);
        }
    }
    
    private void sendMessage() {
        if (selectedTeacher == null || messageField.getText().trim().isEmpty()) {
            return;
        }

        String content = messageField.getText().trim();
        messageField.clear();

        try {
            // Create message
            ChatMessage message = new ChatMessage(
                currentUser.getUserId(),
                currentUser.getFullName(),
                selectedTeacher.getUserId(),
                selectedTeacher.getFullName(),
                content
            );

            // Save message to database
            boolean success = chatService.saveMessage(message);

            if (success) {
                // Add to local chat immediately
                currentChatHistory.add(message);
                Platform.runLater(() -> {
                    addMessageToChat(message);
                    Platform.runLater(() -> chatScrollPane.setVvalue(1.0));
                });

                System.out.println("📤 Message sent: " + content);
            } else {
                showAlert("Lỗi", "Không thể gửi tin nhắn. Vui lòng thử lại.");
            }

        } catch (Exception e) {
            System.err.println("❌ Error sending message: " + e.getMessage());
            e.printStackTrace();
            showAlert("Lỗi", "Không thể gửi tin nhắn: " + e.getMessage());
        }
    }
    
    private void startRefreshService() {
        refreshService = Executors.newSingleThreadScheduledExecutor();

        // Refresh chat every 3 seconds to check for new messages
        refreshService.scheduleAtFixedRate(() -> {
            if (selectedTeacher != null) {
                refreshChatHistory();
            }
        }, 3, 3, TimeUnit.SECONDS);

        System.out.println("🔄 Chat refresh service started");
    }

    private void refreshChatHistory() {
        if (selectedTeacher == null) return;

        try {
            List<ChatMessage> latestHistory = chatService.getChatHistory(currentUser.getUserId(), selectedTeacher.getUserId());

            // Check if there are new messages
            if (latestHistory.size() > currentChatHistory.size()) {
                Platform.runLater(() -> {
                    // Clear and reload all messages
                    chatArea.getChildren().clear();
                    currentChatHistory = latestHistory;

                    for (ChatMessage message : currentChatHistory) {
                        addMessageToChat(message);
                    }

                    // Scroll to bottom
                    Platform.runLater(() -> chatScrollPane.setVvalue(1.0));
                });

                System.out.println("🔄 Chat refreshed - " + latestHistory.size() + " messages");
            }

        } catch (Exception e) {
            System.err.println("❌ Error refreshing chat: " + e.getMessage());
        }
    }
    

    
    private void updateOnlineStatus(String userId) {
        // Simple implementation - show as available for chat
        Platform.runLater(() -> {
            onlineStatusLabel.setText("Có thể chat");
            onlineStatusLabel.setTextFill(Color.GREEN);
        });
    }
    
    private void showAlert(String title, String message) {
        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        });
    }
    
    // Custom cell for teacher list
    private static class TeacherListCell extends ListCell<Teacher> {
        @Override
        protected void updateItem(Teacher teacher, boolean empty) {
            super.updateItem(teacher, empty);
            
            if (empty || teacher == null) {
                setText(null);
                setGraphic(null);
            } else {
                VBox content = new VBox(2);
                
                Label nameLabel = new Label(teacher.getFullName());
                nameLabel.setFont(Font.font("System", FontWeight.BOLD, 14));
                
                Label deptLabel = new Label(teacher.getDepartment());
                deptLabel.setFont(Font.font("System", 12));
                deptLabel.setTextFill(Color.GRAY);
                
                content.getChildren().addAll(nameLabel, deptLabel);
                setGraphic(content);
            }
        }
    }
    
    @FXML
    private void goBackToHome() {
        try {
            // Stop refresh service before leaving
            cleanup();

            // Navigate back to student dashboard
            SceneManager.switchScene("/com/example/doancuoikyjava/student-dashboard.fxml",
                                   "Student Dashboard - " + currentUser.getFullName());

            System.out.println("🏠 Navigating back to Student Dashboard");

        } catch (Exception e) {
            System.err.println("❌ Error navigating back to home: " + e.getMessage());
            e.printStackTrace();
            showAlert("Lỗi", "Không thể quay lại trang chủ: " + e.getMessage());
        }
    }

    public void cleanup() {
        if (refreshService != null && !refreshService.isShutdown()) {
            refreshService.shutdown();
            System.out.println("🔄 Chat refresh service stopped");
        }
    }
}
