package com.example.doancuoikyjava.util;

import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.stage.Stage;
import javafx.stage.Screen;
import javafx.geometry.Rectangle2D;
import com.example.doancuoikyjava.model.User;

import java.io.IOException;

/**
 * Enhanced Scene Manager with theme support and improved navigation
 */
public class SceneManager {
    private static Stage primaryStage;
    private static User currentUser;

    // Window size constants
    private static final double DEFAULT_WIDTH = 1400;
    private static final double DEFAULT_HEIGHT = 900;
    private static final double MIN_WIDTH = 1200;
    private static final double MIN_HEIGHT = 800;

    public static void setPrimaryStage(Stage stage) {
        primaryStage = stage;
        configureStage();

        // Apply theme to primary stage
        if (stage != null && stage.getScene() != null) {
            ThemeManager.registerScene(stage.getScene());
        }
    }

    /**
     * Cấu hình stage với kích thước và thuộc t<PERSON>h đồng nhất
     */
    private static void configureStage() {
        if (primaryStage != null) {
            // Set minimum size
            primaryStage.setMinWidth(MIN_WIDTH);
            primaryStage.setMinHeight(MIN_HEIGHT);

            // Get screen bounds
            Rectangle2D screenBounds = Screen.getPrimary().getVisualBounds();

            // Set window size (90% of screen size or default, whichever is smaller)
            double windowWidth = Math.min(DEFAULT_WIDTH, screenBounds.getWidth() * 0.9);
            double windowHeight = Math.min(DEFAULT_HEIGHT, screenBounds.getHeight() * 0.9);

            primaryStage.setWidth(windowWidth);
            primaryStage.setHeight(windowHeight);

            // Center the window
            primaryStage.setX((screenBounds.getWidth() - windowWidth) / 2);
            primaryStage.setY((screenBounds.getHeight() - windowHeight) / 2);

            // Allow resizing
            primaryStage.setResizable(true);

            // Set maximized for better experience
            primaryStage.setMaximized(true);

            System.out.println("🖥️ Window configured: " + windowWidth + "x" + windowHeight + " (Maximized)");
        }
    }

    public static Stage getPrimaryStage() {
        return primaryStage;
    }

    public static void setCurrentUser(User user) {
        currentUser = user;
        System.out.println("👤 Current user set: " + (user != null ? user.getFullName() : "null"));
    }

    public static User getCurrentUser() {
        return currentUser;
    }

    /**
     * Switch theme
     */
    public static void switchTheme(ThemeManager.Theme theme) {
        ThemeManager.setTheme(theme);
        System.out.println("🎨 Theme switched to: " + theme.getDisplayName());
    }

    /**
     * Toggle between light and dark theme
     */
    public static void toggleTheme() {
        ThemeManager.toggleTheme();
    }

    /**
     * Get current theme
     */
    public static ThemeManager.Theme getCurrentTheme() {
        return ThemeManager.getCurrentTheme();
    }

    public static void switchScene(String fxmlPath, String title) throws IOException {
        FXMLLoader loader = new FXMLLoader(SceneManager.class.getResource(fxmlPath));
        Parent root = loader.load();
        Scene scene = new Scene(root);

        // Register scene with theme manager
        ThemeManager.registerScene(scene);

        primaryStage.setScene(scene);
        primaryStage.setTitle(title);

        // Ensure consistent window properties
        ensureConsistentWindowProperties();

        primaryStage.show();

        System.out.println("🔄 Scene switched to: " + title);
    }

    public static void switchSceneWithController(String fxmlPath, String title, Object controller) throws IOException {
        FXMLLoader loader = new FXMLLoader(SceneManager.class.getResource(fxmlPath));
        loader.setController(controller);
        Parent root = loader.load();
        Scene scene = new Scene(root);

        // Register scene with theme manager
        ThemeManager.registerScene(scene);

        primaryStage.setScene(scene);
        primaryStage.setTitle(title);

        // Ensure consistent window properties
        ensureConsistentWindowProperties();

        primaryStage.show();

        System.out.println("🔄 Scene switched with controller to: " + title);
    }

    /**
     * Đảm bảo thuộc tính cửa sổ đồng nhất khi chuyển scene
     */
    private static void ensureConsistentWindowProperties() {
        if (primaryStage != null) {
            // Maintain minimum size
            if (primaryStage.getWidth() < MIN_WIDTH) {
                primaryStage.setWidth(MIN_WIDTH);
            }
            if (primaryStage.getHeight() < MIN_HEIGHT) {
                primaryStage.setHeight(MIN_HEIGHT);
            }

            // Keep maximized state if it was maximized
            if (!primaryStage.isMaximized()) {
                // If not maximized, ensure it's at least the default size
                if (primaryStage.getWidth() < DEFAULT_WIDTH || primaryStage.getHeight() < DEFAULT_HEIGHT) {
                    primaryStage.setWidth(DEFAULT_WIDTH);
                    primaryStage.setHeight(DEFAULT_HEIGHT);

                    // Center the window
                    Rectangle2D screenBounds = Screen.getPrimary().getVisualBounds();
                    primaryStage.setX((screenBounds.getWidth() - DEFAULT_WIDTH) / 2);
                    primaryStage.setY((screenBounds.getHeight() - DEFAULT_HEIGHT) / 2);
                }
            }

            // Ensure resizable
            primaryStage.setResizable(true);
        }
    }

    /**
     * Force maximize window
     */
    public static void maximizeWindow() {
        if (primaryStage != null) {
            primaryStage.setMaximized(true);
        }
    }

    /**
     * Get current window size info
     */
    public static String getWindowInfo() {
        if (primaryStage != null) {
            return String.format("Window: %.0fx%.0f (Maximized: %s)",
                primaryStage.getWidth(),
                primaryStage.getHeight(),
                primaryStage.isMaximized());
        }
        return "Window: Not initialized";
    }

    public static void logout() {
        currentUser = null;
        try {
            switchScene("/com/example/doancuoikyjava/login.fxml", "Đăng nhập - Hệ thống quản lý");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
