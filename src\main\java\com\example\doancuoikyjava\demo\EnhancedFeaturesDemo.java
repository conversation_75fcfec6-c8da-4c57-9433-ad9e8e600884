package com.example.doancuoikyjava.demo;

import com.example.doancuoikyjava.service.AdvancedNotificationService;
import com.example.doancuoikyjava.service.AdvancedReportingService;
import com.example.doancuoikyjava.util.*;

import java.util.Arrays;
import java.util.concurrent.CompletableFuture;

/**
 * Demo class showcasing all enhanced features
 */
public class EnhancedFeaturesDemo {
    
    public static void main(String[] args) {
        System.out.println("🚀 Starting Enhanced Features Demo...\n");
        
        EnhancedFeaturesDemo demo = new EnhancedFeaturesDemo();
        demo.runDemo();
    }
    
    public void runDemo() {
        try {
            // 1. Performance Monitoring Demo
            demoPerformanceMonitoring();
            
            // 2. Cache Management Demo
            demoCacheManagement();
            
            // 3. Theme System Demo
            demoThemeSystem();
            
            // 4. Notification System Demo
            demoNotificationSystem();
            
            // 5. Reporting System Demo
            demoReportingSystem();
            
            // 6. Database Connection Pool Demo
            demoDatabaseConnectionPool();
            
            System.out.println("\n✅ Enhanced Features Demo completed successfully!");
            
        } catch (Exception e) {
            System.err.println("❌ Demo failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Demo Performance Monitoring
     */
    private void demoPerformanceMonitoring() {
        System.out.println("📊 === Performance Monitoring Demo ===");
        
        PerformanceMonitor monitor = PerformanceMonitor.getInstance();
        
        // Demo operation timing
        PerformanceMonitor.Timer timer1 = monitor.startTimer("demo_operation_1");
        simulateWork(100);
        timer1.stop();
        
        PerformanceMonitor.Timer timer2 = monitor.startTimer("demo_operation_2");
        simulateWork(200);
        timer2.stop();
        
        PerformanceMonitor.Timer timer3 = monitor.startTimer("demo_operation_1");
        simulateWork(150);
        timer3.stop();
        
        // Show performance summary
        System.out.println(monitor.getPerformanceSummary());
        
        // Show memory info
        PerformanceMonitor.MemoryInfo memInfo = monitor.getMemoryInfo();
        System.out.println("Memory Info: " + memInfo);
        
        System.out.println("✅ Performance monitoring demo completed\n");
    }
    
    /**
     * Demo Cache Management
     */
    private void demoCacheManagement() {
        System.out.println("🗄️ === Cache Management Demo ===");
        
        CacheManager cache = CacheManager.getInstance();
        
        // Demo basic caching
        cache.put("user:1", "John Doe");
        cache.put("user:2", "Jane Smith");
        cache.put("course:CS101", "Introduction to Computer Science");
        cache.put("course:MATH201", "Calculus II");
        
        // Demo cache retrieval
        String user1 = cache.get("user:1");
        String course1 = cache.get("course:CS101");
        System.out.println("Retrieved from cache - User: " + user1 + ", Course: " + course1);
        
        // Demo cache or compute
        String user3 = cache.getOrCompute("user:3", () -> {
            System.out.println("Computing user:3 (cache miss)");
            return "Bob Johnson";
        });
        
        String user3Again = cache.getOrCompute("user:3", () -> {
            System.out.println("This should not print (cache hit)");
            return "Bob Johnson";
        });
        
        // Show cache statistics
        System.out.println(cache.getStatistics());
        System.out.println(cache.getDetailedInfo());
        
        // Demo prefix clearing
        cache.clearByPrefix("user:");
        System.out.println("After clearing user: prefix - " + cache.getStatistics());
        
        System.out.println("✅ Cache management demo completed\n");
    }
    
    /**
     * Demo Theme System
     */
    private void demoThemeSystem() {
        System.out.println("🎨 === Theme System Demo ===");
        
        // Show available themes
        ThemeManager.Theme[] themes = ThemeManager.getAvailableThemes();
        System.out.println("Available themes:");
        for (ThemeManager.Theme theme : themes) {
            System.out.println("  • " + theme.getDisplayName() + " (" + theme.getCssPath() + ")");
        }
        
        // Demo theme switching
        System.out.println("\nCurrent theme: " + ThemeManager.getCurrentTheme().getDisplayName());
        
        ThemeManager.setTheme(ThemeManager.Theme.DARK);
        System.out.println("Switched to: " + ThemeManager.getCurrentTheme().getDisplayName());
        
        ThemeManager.setTheme(ThemeManager.Theme.BLUE);
        System.out.println("Switched to: " + ThemeManager.getCurrentTheme().getDisplayName());
        
        // Demo theme colors
        System.out.println("\nCurrent theme colors:");
        System.out.println("  • Primary: " + ThemeManager.getPrimaryColor());
        System.out.println("  • Secondary: " + ThemeManager.getSecondaryColor());
        System.out.println("  • Background: " + ThemeManager.getBackgroundColor());
        System.out.println("  • Text: " + ThemeManager.getTextColor());
        
        // Demo style creation
        String style = ThemeManager.createStyle(
            "-fx-background-color: {{BACKGROUND}}; -fx-text-fill: {{TEXT}}; -fx-border-color: {{PRIMARY}};"
        );
        System.out.println("Generated style: " + style);
        
        System.out.println("✅ Theme system demo completed\n");
    }
    
    /**
     * Demo Notification System
     */
    private void demoNotificationSystem() {
        System.out.println("📢 === Notification System Demo ===");
        
        AdvancedNotificationService notificationService = AdvancedNotificationService.getInstance();
        
        // Demo single notification
        CompletableFuture<Boolean> singleNotification = notificationService.sendNotification(
            "user123",
            "Welcome!",
            "Welcome to the enhanced student management system!",
            AdvancedNotificationService.NotificationType.INFO,
            AdvancedNotificationService.Priority.NORMAL
        );
        
        singleNotification.thenAccept(success -> {
            System.out.println("Single notification sent: " + success);
        });
        
        // Demo bulk notification
        CompletableFuture<Integer> bulkNotification = notificationService.sendBulkNotification(
            Arrays.asList("user123", "user456", "user789"),
            "System Maintenance",
            "The system will undergo maintenance tonight from 2 AM to 4 AM.",
            AdvancedNotificationService.NotificationType.WARNING,
            AdvancedNotificationService.Priority.HIGH
        );
        
        bulkNotification.thenAccept(count -> {
            System.out.println("Bulk notification sent to " + count + " users");
        });
        
        // Demo system notification
        CompletableFuture<Integer> systemNotification = notificationService.createSystemNotification(
            "New Features Available",
            "Check out the new enhanced features in the latest update!",
            AdvancedNotificationService.Priority.NORMAL
        );
        
        systemNotification.thenAccept(count -> {
            System.out.println("System notification sent to " + count + " users");
        });
        
        // Wait for notifications to complete
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        System.out.println("✅ Notification system demo completed\n");
    }
    
    /**
     * Demo Reporting System
     */
    private void demoReportingSystem() {
        System.out.println("📈 === Reporting System Demo ===");
        
        AdvancedReportingService reportingService = AdvancedReportingService.getInstance();
        
        // Show available report types
        AdvancedReportingService.ReportType[] reportTypes = reportingService.getAvailableReportTypes();
        System.out.println("Available report types:");
        for (AdvancedReportingService.ReportType type : reportTypes) {
            System.out.println("  • " + type.getDisplayName());
        }
        
        // Demo student performance report
        CompletableFuture<AdvancedReportingService.ReportData> performanceReport = 
            reportingService.generateStudentPerformanceReport();
        
        performanceReport.thenAccept(reportData -> {
            System.out.println("\nStudent Performance Report Generated:");
            System.out.println("  • Title: " + reportData.getTitle());
            System.out.println("  • Chart Type: " + reportData.getChartType());
            System.out.println("  • Data Points: " + reportData.getData().size());
            System.out.println("  • Generated At: " + reportData.getGeneratedAt());
            
            // Demo CSV export
            String csvPath = "student_performance_report.csv";
            reportingService.exportToCSV(reportData, csvPath).thenAccept(success -> {
                if (success) {
                    System.out.println("  • Exported to CSV: " + csvPath);
                }
            });
        });
        
        // Demo course enrollment report
        CompletableFuture<AdvancedReportingService.ReportData> enrollmentReport = 
            reportingService.generateCourseEnrollmentReport();
        
        enrollmentReport.thenAccept(reportData -> {
            System.out.println("\nCourse Enrollment Report Generated:");
            System.out.println("  • Title: " + reportData.getTitle());
            System.out.println("  • Chart Type: " + reportData.getChartType());
            System.out.println("  • Data Points: " + reportData.getData().size());
        });
        
        // Demo teacher workload report
        CompletableFuture<AdvancedReportingService.ReportData> workloadReport = 
            reportingService.generateTeacherWorkloadReport();
        
        workloadReport.thenAccept(reportData -> {
            System.out.println("\nTeacher Workload Report Generated:");
            System.out.println("  • Title: " + reportData.getTitle());
            System.out.println("  • Chart Type: " + reportData.getChartType());
            System.out.println("  • Data Points: " + reportData.getData().size());
        });
        
        // Wait for reports to complete
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        System.out.println("✅ Reporting system demo completed\n");
    }
    
    /**
     * Demo Database Connection Pool
     */
    private void demoDatabaseConnectionPool() {
        System.out.println("🏊 === Database Connection Pool Demo ===");
        
        try {
            // Show pool statistics
            System.out.println("Pool Statistics: " + DatabaseConnectionPool.getPoolStatistics());
            System.out.println("Pool Health: " + (DatabaseConnectionPool.isPoolHealthy() ? "Healthy ✅" : "Unhealthy ❌"));
            System.out.println("Initialization Time: " + DatabaseConnectionPool.getInitializationTime() + "ms");
            System.out.println("Active Connections: " + DatabaseConnectionPool.getActiveConnectionsCount());
            System.out.println("Total Connections Created: " + DatabaseConnectionPool.getTotalConnectionsCreated());
            
            // Demo connection usage
            System.out.println("\nTesting connection pool...");
            for (int i = 0; i < 5; i++) {
                try (var conn = DatabaseConnectionPool.getConnection()) {
                    System.out.println("Connection " + (i + 1) + " obtained successfully");
                    // Simulate some work
                    Thread.sleep(100);
                }
            }
            
            System.out.println("Final Pool Statistics: " + DatabaseConnectionPool.getPoolStatistics());
            
        } catch (Exception e) {
            System.err.println("❌ Database connection pool demo failed: " + e.getMessage());
        }
        
        System.out.println("✅ Database connection pool demo completed\n");
    }
    
    /**
     * Simulate work by sleeping
     */
    private void simulateWork(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
