package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.Student;
import com.example.doancuoikyjava.model.User;
import com.example.doancuoikyjava.service.UserService;
import com.example.doancuoikyjava.util.SceneManager;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.chart.PieChart;
import javafx.scene.control.*;
import javafx.scene.layout.VBox;

import java.io.IOException;
import java.net.URL;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;
import java.util.stream.Collectors;

public class ChartController implements Initializable {
    
    @FXML private Label welcomeLabel;
    @FXML private ComboBox<String> chartTypeComboBox;
    @FXML private Button generateChartButton;
    @FXML private Button refreshButton;
    @FXML private Button backButton;
    @FXML private VBox chartContainer;
    @FXML private Label chartInfoLabel;

    private UserService userService;
    private PieChart currentChart;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        userService = new UserService();
        setupUI();
        generateChart(); // Tạo biểu đồ mặc định
    }
    
    private void setupUI() {
        // Thiết lập welcome label
        User currentUser = SceneManager.getCurrentUser();
        if (currentUser != null) {
            welcomeLabel.setText("📊 Thống kê Biểu đồ - " + currentUser.getFullName());
        }
        
        // Thiết lập combo box
        chartTypeComboBox.getItems().addAll(
            "📊 Xếp loại Học lực",
            "📚 Sinh viên theo Lớp", 
            "🎓 Sinh viên theo Ngành",
            "📈 Phân bố GPA Chi tiết"
        );
        
        chartTypeComboBox.setValue("📊 Xếp loại Học lực");
        
        // Thiết lập info label
        chartInfoLabel.setText("💡 Chọn loại biểu đồ và nhấn 'Tạo biểu đồ' để xem thống kê trực quan");
    }
    
    @FXML
    private void generateChart() {
        try {
            String selectedType = chartTypeComboBox.getValue();
            PieChart chart = null;
            String info = "";

            switch (selectedType) {
                case "📊 Xếp loại Học lực":
                    chart = createStudentGradeDistributionChart();
                    info = "📊 Biểu đồ thống kê xếp loại học lực của sinh viên theo GPA";
                    break;

                case "📚 Sinh viên theo Lớp":
                    chart = createStudentsByClassChart();
                    info = "📚 Biểu đồ phân bố số lượng sinh viên theo từng lớp học";
                    break;

                case "🎓 Sinh viên theo Ngành":
                    chart = createStudentsByMajorChart();
                    info = "🎓 Biểu đồ phân bố số lượng sinh viên theo ngành đào tạo";
                    break;

                case "📈 Phân bố GPA Chi tiết":
                    chart = createGPARangesChart();
                    info = "📈 Biểu đồ phân bố chi tiết GPA theo các khoảng điểm";
                    break;

                default:
                    chart = createStudentGradeDistributionChart();
                    info = "📊 Biểu đồ thống kê xếp loại học lực của sinh viên theo GPA";
                    break;
            }

            if (chart != null) {
                // Xóa biểu đồ cũ nếu có
                if (currentChart != null) {
                    chartContainer.getChildren().remove(currentChart);
                }

                // Thêm biểu đồ mới
                currentChart = chart;
                chartContainer.getChildren().add(currentChart);

                // Cập nhật info label
                chartInfoLabel.setText(info);

                showAlert("Thành công", "Biểu đồ đã được tạo thành công!");
            }

        } catch (Exception e) {
            showAlert("Lỗi", "Không thể tạo biểu đồ: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    @FXML
    private void refreshData() {
        try {
            // Tạo lại biểu đồ với dữ liệu mới
            generateChart();
            showAlert("Thành công", "Dữ liệu đã được làm mới!");
        } catch (Exception e) {
            showAlert("Lỗi", "Không thể làm mới dữ liệu: " + e.getMessage());
        }
    }
    
    @FXML
    private void exportChart() {
        try {
            // Tạo dialog hiển thị thông tin xuất biểu đồ
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle("📊 Xuất biểu đồ");
            alert.setHeaderText("Thông tin xuất biểu đồ");
            
            String content = "🎯 Loại biểu đồ: " + chartTypeComboBox.getValue() + "\n\n" +
                           "📅 Thời gian tạo: " + java.time.LocalDateTime.now().format(
                               java.time.format.DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss")) + "\n\n" +
                           "💡 Hướng dẫn:\n" +
                           "• Click chuột phải vào biểu đồ để xem menu xuất\n" +
                           "• Chọn 'Save as...' để lưu biểu đồ dưới dạng PNG/JPEG\n" +
                           "• Sử dụng chuột để zoom in/out biểu đồ\n" +
                           "• Kéo thả để di chuyển biểu đồ khi đã zoom";
            
            alert.setContentText(content);
            alert.showAndWait();
            
        } catch (Exception e) {
            showAlert("Lỗi", "Không thể xuất biểu đồ: " + e.getMessage());
        }
    }
    
    @FXML
    private void showChartDetails() {
        try {
            String selectedType = chartTypeComboBox.getValue();
            
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle("📋 Chi tiết biểu đồ");
            alert.setHeaderText("Thông tin chi tiết - " + selectedType);
            
            StringBuilder details = new StringBuilder();
            details.append("📊 THÔNG TIN BIỂU ĐỒ:\n");
            details.append("═══════════════════════\n\n");
            
            switch (selectedType) {
                case "📊 Xếp loại Học lực":
                    details.append("🎯 Mục đích: Thống kê xếp loại học lực sinh viên\n");
                    details.append("📏 Tiêu chí: Dựa trên GPA (Grade Point Average)\n");
                    details.append("🏆 Phân loại:\n");
                    details.append("   • Giỏi: GPA ≥ 3.6\n");
                    details.append("   • Khá: 3.2 ≤ GPA < 3.6\n");
                    details.append("   • Trung bình: 2.5 ≤ GPA < 3.2\n");
                    details.append("   • Yếu: 2.0 ≤ GPA < 2.5\n");
                    details.append("   • Kém: GPA < 2.0\n");
                    break;
                    
                case "📚 Sinh viên theo Lớp":
                    details.append("🎯 Mục đích: Phân bố sinh viên theo lớp học\n");
                    details.append("📊 Hiển thị: Số lượng và tỷ lệ sinh viên mỗi lớp\n");
                    details.append("💡 Ý nghĩa: Giúp quản lý sĩ số và cân bằng lớp học\n");
                    break;
                    
                case "🎓 Sinh viên theo Ngành":
                    details.append("🎯 Mục đích: Phân bố sinh viên theo ngành đào tạo\n");
                    details.append("📊 Hiển thị: Số lượng sinh viên từng ngành\n");
                    details.append("💡 Ý nghĩa: Hỗ trợ quy hoạch tuyển sinh và phân bổ tài nguyên\n");
                    break;
                    
                case "📈 Phân bố GPA Chi tiết":
                    details.append("🎯 Mục đích: Phân tích chi tiết phân bố điểm GPA\n");
                    details.append("📏 Khoảng điểm:\n");
                    details.append("   • Xuất sắc: 3.5-4.0\n");
                    details.append("   • Giỏi: 3.0-3.5\n");
                    details.append("   • Khá: 2.5-3.0\n");
                    details.append("   • Trung bình: 2.0-2.5\n");
                    details.append("   • Yếu: < 2.0\n");
                    break;
            }
            
            details.append("\n🔧 TÍNH NĂNG:\n");
            details.append("• Zoom in/out bằng chuột\n");
            details.append("• Click chuột phải để xuất biểu đồ\n");
            details.append("• Tooltip hiển thị thông tin chi tiết\n");
            details.append("• Legend tương tác\n");
            
            TextArea textArea = new TextArea(details.toString());
            textArea.setEditable(false);
            textArea.setWrapText(true);
            textArea.setPrefSize(600, 400);
            
            alert.getDialogPane().setContent(textArea);
            alert.showAndWait();
            
        } catch (Exception e) {
            showAlert("Lỗi", "Không thể hiển thị chi tiết: " + e.getMessage());
        }
    }
    
    @FXML
    private void goBack() {
        try {
            User currentUser = SceneManager.getCurrentUser();
            if (currentUser != null) {
                switch (currentUser.getRole()) {
                    case ADMIN:
                        SceneManager.switchScene("/com/example/doancuoikyjava/admin-dashboard.fxml", 
                                               "Quản trị viên - " + currentUser.getFullName());
                        break;
                    case TEACHER:
                        SceneManager.switchScene("/com/example/doancuoikyjava/teacher-dashboard.fxml", 
                                               "Giáo viên - " + currentUser.getFullName());
                        break;
                    case STUDENT:
                        SceneManager.switchScene("/com/example/doancuoikyjava/student-dashboard.fxml", 
                                               "Sinh viên - " + currentUser.getFullName());
                        break;
                }
            }
        } catch (IOException e) {
            showAlert("Lỗi", "Không thể quay lại trang chính: " + e.getMessage());
        }
    }
    
    // Methods to create JavaFX PieCharts
    private PieChart createStudentGradeDistributionChart() {
        List<User> users = userService.getUsersByRole(User.UserRole.STUDENT);
        List<Student> students = users.stream()
                .filter(u -> u instanceof Student)
                .map(u -> (Student) u)
                .collect(Collectors.toList());

        // Đếm số lượng theo xếp loại
        int excellent = 0, good = 0, average = 0, weak = 0, poor = 0;

        for (Student student : students) {
            double gpa = student.getGpa();
            if (gpa >= 3.6) excellent++;
            else if (gpa >= 3.2) good++;
            else if (gpa >= 2.5) average++;
            else if (gpa >= 2.0) weak++;
            else poor++;
        }

        ObservableList<PieChart.Data> pieChartData = FXCollections.observableArrayList();
        if (excellent > 0) pieChartData.add(new PieChart.Data("Giỏi (≥3.6)", excellent));
        if (good > 0) pieChartData.add(new PieChart.Data("Khá (3.2-3.6)", good));
        if (average > 0) pieChartData.add(new PieChart.Data("Trung bình (2.5-3.2)", average));
        if (weak > 0) pieChartData.add(new PieChart.Data("Yếu (2.0-2.5)", weak));
        if (poor > 0) pieChartData.add(new PieChart.Data("Kém (<2.0)", poor));

        PieChart chart = new PieChart(pieChartData);
        chart.setTitle("📊 Thống kê Xếp loại Học lực Sinh viên");
        customizePieChart(chart);

        return chart;
    }

    private PieChart createStudentsByClassChart() {
        List<User> users = userService.getUsersByRole(User.UserRole.STUDENT);
        List<Student> students = users.stream()
                .filter(u -> u instanceof Student)
                .map(u -> (Student) u)
                .collect(Collectors.toList());

        Map<String, Long> classCounts = students.stream()
                .filter(s -> s.getClassName() != null && !s.getClassName().trim().isEmpty())
                .collect(Collectors.groupingBy(Student::getClassName, Collectors.counting()));

        ObservableList<PieChart.Data> pieChartData = FXCollections.observableArrayList();
        classCounts.forEach((className, count) -> {
            pieChartData.add(new PieChart.Data(className + " (" + count + " SV)", count));
        });

        PieChart chart = new PieChart(pieChartData);
        chart.setTitle("📚 Thống kê Sinh viên theo Lớp");
        customizePieChart(chart);

        return chart;
    }

    private PieChart createStudentsByMajorChart() {
        List<User> users = userService.getUsersByRole(User.UserRole.STUDENT);
        List<Student> students = users.stream()
                .filter(u -> u instanceof Student)
                .map(u -> (Student) u)
                .collect(Collectors.toList());

        Map<String, Long> majorCounts = students.stream()
                .filter(s -> s.getMajor() != null && !s.getMajor().trim().isEmpty())
                .collect(Collectors.groupingBy(Student::getMajor, Collectors.counting()));

        ObservableList<PieChart.Data> pieChartData = FXCollections.observableArrayList();
        majorCounts.forEach((major, count) -> {
            pieChartData.add(new PieChart.Data(major + " (" + count + " SV)", count));
        });

        PieChart chart = new PieChart(pieChartData);
        chart.setTitle("🎓 Thống kê Sinh viên theo Ngành");
        customizePieChart(chart);

        return chart;
    }

    private PieChart createGPARangesChart() {
        List<User> users = userService.getUsersByRole(User.UserRole.STUDENT);
        List<Student> students = users.stream()
                .filter(u -> u instanceof Student)
                .map(u -> (Student) u)
                .collect(Collectors.toList());

        int range_35_40 = 0, range_30_35 = 0, range_25_30 = 0, range_20_25 = 0, range_below_20 = 0;

        for (Student student : students) {
            double gpa = student.getGpa();
            if (gpa >= 3.5) range_35_40++;
            else if (gpa >= 3.0) range_30_35++;
            else if (gpa >= 2.5) range_25_30++;
            else if (gpa >= 2.0) range_20_25++;
            else range_below_20++;
        }

        ObservableList<PieChart.Data> pieChartData = FXCollections.observableArrayList();
        if (range_35_40 > 0) pieChartData.add(new PieChart.Data("Xuất sắc (3.5-4.0)", range_35_40));
        if (range_30_35 > 0) pieChartData.add(new PieChart.Data("Giỏi (3.0-3.5)", range_30_35));
        if (range_25_30 > 0) pieChartData.add(new PieChart.Data("Khá (2.5-3.0)", range_25_30));
        if (range_20_25 > 0) pieChartData.add(new PieChart.Data("Trung bình (2.0-2.5)", range_20_25));
        if (range_below_20 > 0) pieChartData.add(new PieChart.Data("Yếu (<2.0)", range_below_20));

        PieChart chart = new PieChart(pieChartData);
        chart.setTitle("📈 Phân bố GPA Chi tiết");
        customizePieChart(chart);

        return chart;
    }

    private void customizePieChart(PieChart chart) {
        chart.setLegendVisible(true);
        chart.setLabelsVisible(true);
        chart.setStartAngle(90);
        chart.setClockwise(true);
        chart.setPrefSize(700, 500);

        // Styling
        chart.setStyle("-fx-background-color: white; -fx-border-color: #dee2e6; -fx-border-radius: 10; -fx-background-radius: 10;");
    }

    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
