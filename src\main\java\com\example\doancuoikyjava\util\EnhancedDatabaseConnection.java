package com.example.doancuoikyjava.util;

import java.sql.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Enhanced Database Connection with improved performance and monitoring
 */
public class EnhancedDatabaseConnection {
    
    // Database configuration
    private static final String SERVER = "LAPTOP-1HR3G05C";
    private static final String DATABASE = "StudentManagementDB";
    private static final String USERNAME = "sa";
    private static final String PASSWORD = "123456789";
    private static final int PORT = 1434;
    
    // Connection pool configuration
    private static final int MAX_CONNECTIONS = 10;
    private static final int MIN_CONNECTIONS = 2;
    private static final long CONNECTION_TIMEOUT = 30000; // 30 seconds
    
    // Connection URL with optimizations
    private static final String CONNECTION_URL = String.format(
        "jdbc:sqlserver://**:%d;databaseName=**;user=**;password=**;" +
        "encrypt=false;trustServerCertificate=true;" +
        "loginTimeout=30;socketTimeout=30000;" +
        "selectMethod=cursor;sendStringParametersAsUnicode=false",
        SERVER, PORT, DATABASE, USERNAME, PASSWORD
    );
    
    // Connection pool and monitoring
    private static final ConcurrentHashMap<String, Connection> connectionPool = new ConcurrentHashMap<>();
    private static final AtomicInteger activeConnections = new AtomicInteger(0);
    private static final AtomicInteger totalConnectionsCreated = new AtomicInteger(0);
    private static volatile boolean isInitialized = false;
    
    // Statistics
    private static volatile long lastConnectionTime = 0;
    private static volatile int successfulConnections = 0;
    private static volatile int failedConnections = 0;
    
    /**
     * Get optimized database connection
     */
    public static Connection getConnection() throws SQLException {
        try {
            Connection conn = createNewConnection();
            if (conn != null && !conn.isClosed()) {
                activeConnections.incrementAndGet();
                lastConnectionTime = System.currentTimeMillis();
                successfulConnections++;
                
                // Log connection info periodically
                if (successfulConnections % 10 == 0) {
                    logConnectionStats();
                }
                
                return conn;
            }
        } catch (SQLException e) {
            failedConnections++;
            System.err.println("❌ Enhanced DB Connection failed: " + e.getMessage());
            throw e;
        }
        
        throw new SQLException("Unable to establish database connection");
    }
    
    /**
     * Create new optimized connection
     */
    private static Connection createNewConnection() throws SQLException {
        try {
            // Load SQL Server driver if not already loaded
            Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
            
            Connection conn = DriverManager.getConnection(CONNECTION_URL);
            
            // Optimize connection settings
            conn.setAutoCommit(true);
            conn.setTransactionIsolation(Connection.TRANSACTION_READ_COMMITTED);
            
            totalConnectionsCreated.incrementAndGet();
            
            if (!isInitialized) {
                System.out.println("🚀 Enhanced Database Connection established successfully!");
                System.out.println("📊 Server: " + SERVER + ":" + PORT);
                System.out.println("🗄️ Database: " + DATABASE);
                isInitialized = true;
            }
            
            return conn;
            
        } catch (ClassNotFoundException e) {
            throw new SQLException("SQL Server JDBC Driver not found", e);
        }
    }
    
    /**
     * Test connection with detailed diagnostics
     */
    public static boolean testConnection() {
        long startTime = System.currentTimeMillis();
        
        try (Connection conn = getConnection()) {
            if (conn != null && !conn.isClosed()) {
                // Test with a simple query
                try (Statement stmt = conn.createStatement();
                     ResultSet rs = stmt.executeQuery("SELECT 1 as test")) {
                    
                    if (rs.next()) {
                        long duration = System.currentTimeMillis() - startTime;
                        System.out.println("✅ Enhanced DB Connection test successful (" + duration + "ms)");
                        return true;
                    }
                }
            }
        } catch (SQLException e) {
            long duration = System.currentTimeMillis() - startTime;
            System.err.println("❌ Enhanced DB Connection test failed (" + duration + "ms): " + e.getMessage());
            return false;
        }
        
        return false;
    }
    
    /**
     * Execute query with performance monitoring
     */
    public static ResultSet executeQuery(String sql, Object... params) throws SQLException {
        long startTime = System.currentTimeMillis();
        
        try {
            Connection conn = getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            
            // Set parameters
            for (int i = 0; i < params.length; i++) {
                stmt.setObject(i + 1, params[i]);
            }
            
            ResultSet rs = stmt.executeQuery();
            
            long duration = System.currentTimeMillis() - startTime;
            if (duration > 1000) { // Log slow queries
                System.out.println("⚠️ Slow query detected (" + duration + "ms): " + sql.substring(0, Math.min(50, sql.length())) + "...");
            }
            
            return rs;
            
        } catch (SQLException e) {
            long duration = System.currentTimeMillis() - startTime;
            System.err.println("❌ Query failed (" + duration + "ms): " + e.getMessage());
            throw e;
        }
    }
    
    /**
     * Execute update with performance monitoring
     */
    public static int executeUpdate(String sql, Object... params) throws SQLException {
        long startTime = System.currentTimeMillis();
        
        try (Connection conn = getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            // Set parameters
            for (int i = 0; i < params.length; i++) {
                stmt.setObject(i + 1, params[i]);
            }
            
            int result = stmt.executeUpdate();
            
            long duration = System.currentTimeMillis() - startTime;
            if (duration > 500) { // Log slow updates
                System.out.println("⚠️ Slow update detected (" + duration + "ms): " + sql.substring(0, Math.min(50, sql.length())) + "...");
            }
            
            return result;
            
        } catch (SQLException e) {
            long duration = System.currentTimeMillis() - startTime;
            System.err.println("❌ Update failed (" + duration + "ms): " + e.getMessage());
            throw e;
        }
    }
    
    /**
     * Log connection statistics
     */
    private static void logConnectionStats() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        System.out.println(String.format(
            "📊 [**] DB Stats - Active: %d, Total Created: %d, Success: %d, Failed: %d",
            timestamp, activeConnections.get(), totalConnectionsCreated.get(), 
            successfulConnections, failedConnections
        ));
    }
    
    /**
     * Get connection statistics
     */
    public static String getConnectionStats() {
        return String.format(
            "Database Connection Statistics:\n" +
            "- Active Connections: %d\n" +
            "- Total Connections Created: %d\n" +
            "- Successful Connections: %d\n" +
            "- Failed Connections: %d\n" +
            "- Last Connection: **\n" +
            "- Server: **:%d\n" +
            "- Database: **",
            activeConnections.get(), totalConnectionsCreated.get(),
            successfulConnections, failedConnections,
            lastConnectionTime > 0 ? new java.util.Date(lastConnectionTime).toString() : "Never",
            SERVER, PORT, DATABASE
        );
    }
    
    /**
     * Close connection and update statistics
     */
    public static void closeConnection(Connection conn) {
        if (conn != null) {
            try {
                if (!conn.isClosed()) {
                    conn.close();
                    activeConnections.decrementAndGet();
                }
            } catch (SQLException e) {
                System.err.println("❌ Error closing connection: " + e.getMessage());
            }
        }
    }
    
    /**
     * Cleanup all connections
     */
    public static void cleanup() {
        connectionPool.clear();
        System.out.println("🧹 Enhanced Database Connection cleanup completed");
        logConnectionStats();
    }
}
