/* Main CSS Styles for Student Management System */

/* Root color variables */
.root {
    -fx-primary-color: #2196F3;
    -fx-primary-dark: #1976D2;
    -fx-primary-light: #BBDEFB;
    -fx-accent-color: #FF5722;
    -fx-success-color: #4CAF50;
    -fx-warning-color: #FF9800;
    -fx-error-color: #F44336;
    -fx-background-color: #FAFAFA;
    -fx-text-color: #212121;
    -fx-secondary-text: #757575;
}

/* Background styling */
.main-container {
    -fx-background-color: linear-gradient(from 0% 0% to 100% 100%, #667eea 0%, #764ba2 100%);
    -fx-padding: 20;
}

.content-container {
    -fx-background-color: white;
    -fx-background-radius: 15;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 10, 0, 0, 5);
    -fx-padding: 30;
}

/* Login form styling */
.login-container {
    -fx-background-color: white;
    -fx-background-radius: 20;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 20, 0, 0, 10);
    -fx-padding: 40;
    -fx-spacing: 20;
    -fx-alignment: center;
    -fx-max-width: 400;
}

.login-title {
    -fx-font-size: 28px;
    -fx-font-weight: bold;
    -fx-text-fill: #2196F3;
    -fx-alignment: center;
}

.login-subtitle {
    -fx-font-size: 14px;
    -fx-text-fill: #757575;
    -fx-alignment: center;
}

/* Button styling */
.btn {
    -fx-background-radius: 25;
    -fx-padding: 12 24;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 5, 0, 0, 2);
}

.btn-primary {
    -fx-background-color: linear-gradient(from 0% 0% to 0% 100%, #2196F3 0%, #1976D2 100%);
    -fx-text-fill: white;
}

.btn-primary:hover {
    -fx-background-color: linear-gradient(from 0% 0% to 0% 100%, #1976D2 0%, #1565C0 100%);
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 8, 0, 0, 3);
}

.btn-success {
    -fx-background-color: #4CAF50;
    -fx-text-fill: white;
}

.btn-success:hover {
    -fx-background-color: #388E3C;
}

.btn-warning {
    -fx-background-color: #FF9800;
    -fx-text-fill: white;
}

.btn-warning:hover {
    -fx-background-color: #F57C00;
}

.btn-danger {
    -fx-background-color: #F44336;
    -fx-text-fill: white;
}

.btn-danger:hover {
    -fx-background-color: #D32F2F;
}

.btn-secondary {
    -fx-background-color: #9E9E9E;
    -fx-text-fill: white;
}

.btn-secondary:hover {
    -fx-background-color: #757575;
}

/* Input field styling */
.text-field {
    -fx-background-radius: 10;
    -fx-border-radius: 10;
    -fx-border-color: #E0E0E0;
    -fx-border-width: 2;
    -fx-padding: 12;
    -fx-font-size: 14px;
    -fx-background-color: white;
}

.text-field:focused {
    -fx-border-color: #2196F3;
    -fx-effect: dropshadow(gaussian, rgba(33,150,243,0.3), 5, 0, 0, 0);
}

.password-field {
    -fx-background-radius: 10;
    -fx-border-radius: 10;
    -fx-border-color: #E0E0E0;
    -fx-border-width: 2;
    -fx-padding: 12;
    -fx-font-size: 14px;
    -fx-background-color: white;
}

.password-field:focused {
    -fx-border-color: #2196F3;
    -fx-effect: dropshadow(gaussian, rgba(33,150,243,0.3), 5, 0, 0, 0);
}

/* ComboBox styling */
.combo-box {
    -fx-background-radius: 10;
    -fx-border-radius: 10;
    -fx-border-color: #E0E0E0;
    -fx-border-width: 2;
    -fx-padding: 8;
    -fx-font-size: 14px;
    -fx-background-color: white;
}

.combo-box:focused {
    -fx-border-color: #2196F3;
}

/* Label styling */
.label {
    -fx-font-size: 14px;
    -fx-text-fill: #212121;
}

.label-title {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
    -fx-text-fill: #2196F3;
}

.label-subtitle {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #424242;
}

.label-info {
    -fx-font-size: 12px;
    -fx-text-fill: #757575;
}

/* Table styling */
.table-view {
    -fx-background-color: white;
    -fx-border-color: #E0E0E0;
    -fx-border-width: 1;
    -fx-border-radius: 10;
    -fx-background-radius: 10;
}

.table-view .column-header {
    -fx-background-color: #2196F3;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-padding: 10;
}

.table-view .table-cell {
    -fx-padding: 8;
    -fx-border-color: #F5F5F5;
}

.table-row-cell:selected {
    -fx-background-color: #E3F2FD;
    -fx-text-fill: #1976D2;
}

.table-row-cell:hover {
    -fx-background-color: #F5F5F5;
}

/* Card styling */
.card {
    -fx-background-color: white;
    -fx-background-radius: 15;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 5);
    -fx-padding: 20;
    -fx-spacing: 10;
}

.card-header {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #2196F3;
    -fx-padding: 0 0 10 0;
    -fx-border-color: #E0E0E0;
    -fx-border-width: 0 0 1 0;
}

/* Navigation styling */
.nav-bar {
    -fx-background-color: #2196F3;
    -fx-padding: 15;
    -fx-spacing: 20;
    -fx-alignment: center-left;
}

.nav-button {
    -fx-background-color: transparent;
    -fx-text-fill: white;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-padding: 10 20;
    -fx-background-radius: 20;
    -fx-cursor: hand;
}

.nav-button:hover {
    -fx-background-color: rgba(255,255,255,0.2);
}

.nav-button:pressed {
    -fx-background-color: rgba(255,255,255,0.3);
}

/* Dashboard styling */
.dashboard-container {
    -fx-spacing: 20;
    -fx-padding: 20;
}

.stats-card {
    -fx-background-color: white;
    -fx-background-radius: 15;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 5);
    -fx-padding: 25;
    -fx-alignment: center;
    -fx-spacing: 10;
    -fx-min-width: 200;
}

.stats-number {
    -fx-font-size: 36px;
    -fx-font-weight: bold;
    -fx-text-fill: #2196F3;
}

.stats-label {
    -fx-font-size: 14px;
    -fx-text-fill: #757575;
    -fx-font-weight: bold;
}

/* Alert and dialog styling */
.alert {
    -fx-background-color: white;
    -fx-background-radius: 15;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 15, 0, 0, 5);
}

.alert .header-panel {
    -fx-background-color: #2196F3;
    -fx-background-radius: 15 15 0 0;
}

.alert .header-panel .label {
    -fx-text-fill: white;
    -fx-font-weight: bold;
}

/* Sidebar styling */
.sidebar {
    -fx-background-color: #263238;
    -fx-padding: 20;
    -fx-spacing: 10;
    -fx-min-width: 250;
}

.sidebar-button {
    -fx-background-color: transparent;
    -fx-text-fill: #B0BEC5;
    -fx-font-size: 14px;
    -fx-padding: 15 20;
    -fx-background-radius: 10;
    -fx-cursor: hand;
    -fx-alignment: center-left;
    -fx-min-width: 200;
}

.sidebar-button:hover {
    -fx-background-color: #37474F;
    -fx-text-fill: white;
}

.sidebar-button.active {
    -fx-background-color: #2196F3;
    -fx-text-fill: white;
}

/* Form styling */
.form-container {
    -fx-spacing: 15;
    -fx-padding: 20;
}

.form-group {
    -fx-spacing: 5;
}

.form-row {
    -fx-spacing: 15;
    -fx-alignment: center-left;
}

/* Responsive design helpers */
.full-width {
    -fx-max-width: infinity;
}

.center-content {
    -fx-alignment: center;
}

.space-between {
    -fx-alignment: center;
    -fx-spacing: 20;
}

/* ===== MODERN LOGIN STYLES ===== */

/* Login Container Enhancements */
.login-container {
    -fx-background-color: rgba(255, 255, 255, 0.95);
    -fx-background-radius: 20;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 30, 0, 0, 10);
    -fx-max-width: 900;
    -fx-min-width: 800;
    -fx-max-height: 600;
}

/* Login Form Field Enhancements */
.text-field:focused, .password-field:focused, .combo-box:focused {
    -fx-border-color: #667eea !important;
    -fx-border-width: 2 !important;
    -fx-effect: dropshadow(gaussian, rgba(102, 126, 234, 0.3), 8, 0, 0, 0);
}

.text-field:hover, .password-field:hover, .combo-box:hover {
    -fx-border-color: #764ba2 !important;
    -fx-border-width: 1.5 !important;
    -fx-effect: dropshadow(gaussian, rgba(118, 75, 162, 0.2), 5, 0, 0, 0);
}

/* Login Button Enhancements */
.button:hover {
    -fx-background-color: linear-gradient(to right, #5a6fd8, #6a42a0) !important;
    -fx-effect: dropshadow(gaussian, rgba(102, 126, 234, 0.4), 10, 0, 0, 3);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
    -fx-cursor: hand;
}

.button:pressed {
    -fx-background-color: linear-gradient(to right, #4e63d2, #5e3894) !important;
    -fx-scale-x: 0.98;
    -fx-scale-y: 0.98;
}

/* Demo Account Cards */
.demo-card {
    -fx-background-radius: 8;
    -fx-padding: 12;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 3, 0, 0, 1);
    -fx-cursor: hand;
}

.demo-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 8, 0, 0, 3);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

/* Background Animation Elements */
.background-circle {
    -fx-effect: dropshadow(gaussian, rgba(255,255,255,0.1), 20, 0, 0, 0);
}

/* Welcome Section Styling */
.welcome-section {
    -fx-background-color: linear-gradient(to bottom, #667eea, #764ba2);
    -fx-background-radius: 20 0 0 20;
    -fx-padding: 50;
}

.welcome-title {
    -fx-text-fill: white;
    -fx-font-size: 28px;
    -fx-font-weight: bold;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 2, 0, 0, 1);
}

.welcome-subtitle {
    -fx-text-fill: rgba(255,255,255,0.9);
    -fx-font-size: 16px;
}

.feature-item {
    -fx-text-fill: rgba(255,255,255,0.9);
    -fx-font-size: 14px;
}

.feature-check {
    -fx-text-fill: #4CAF50;
    -fx-font-size: 16px;
    -fx-font-weight: bold;
}

/* Login Form Section */
.login-form-section {
    -fx-padding: 50;
    -fx-background-color: white;
    -fx-background-radius: 0 20 20 0;
}

.login-header {
    -fx-font-size: 32px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

.login-description {
    -fx-font-size: 14px;
    -fx-text-fill: #7f8c8d;
}

/* Field Labels */
.field-label {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #34495e;
}

/* Error Label Styling */
.error-label {
    -fx-text-fill: #e74c3c;
    -fx-font-size: 12px;
    -fx-background-color: rgba(231, 76, 60, 0.1);
    -fx-background-radius: 5;
    -fx-padding: 8;
    -fx-border-color: rgba(231, 76, 60, 0.3);
    -fx-border-radius: 5;
    -fx-border-width: 1;
}

/* Demo Account Styling */
.demo-title {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

.demo-admin {
    -fx-background-color: rgba(52, 152, 219, 0.1);
    -fx-border-color: rgba(52, 152, 219, 0.3);
    -fx-border-width: 1;
}

.demo-teacher {
    -fx-background-color: rgba(46, 204, 113, 0.1);
    -fx-border-color: rgba(46, 204, 113, 0.3);
    -fx-border-width: 1;
}

.demo-student {
    -fx-background-color: rgba(155, 89, 182, 0.1);
    -fx-border-color: rgba(155, 89, 182, 0.3);
    -fx-border-width: 1;
}

.demo-role {
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

.demo-credentials {
    -fx-font-family: "Consolas", "Monaco", "Courier New", monospace;
    -fx-font-size: 13px;
}

.demo-admin .demo-credentials {
    -fx-text-fill: #3498db;
}

.demo-teacher .demo-credentials {
    -fx-text-fill: #27ae60;
}

.demo-student .demo-credentials {
    -fx-text-fill: #9b59b6;
}
