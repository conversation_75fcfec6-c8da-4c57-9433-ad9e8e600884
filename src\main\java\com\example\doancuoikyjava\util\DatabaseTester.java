package com.example.doancuoikyjava.util;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;

public class DatabaseTester {
    
    public static void main(String[] args) {
        testConnection();
    }
    
    public static void testConnection() {
        String server = "LAPTOP-1HR3G05C";
        String database = "StudentManagementDB";
        String username = "sa";
        String password = "123456789";
        
        String connectionUrl = "jdbc:sqlserver://" + server + ":1434;"
                             + "databaseName=" + database + ";"
                             + "user=" + username + ";"
                             + "password=" + password + ";"
                             + "encrypt=false;trustServerCertificate=true;";
        
        System.out.println("🔗 Đang kiểm tra kết nối SQL Server...");
        System.out.println("📍 Server: " + server + ":1434");
        System.out.println("🗃️ Database: " + database);
        System.out.println("👤 User: " + username);
        System.out.println("🔗 Connection URL: " + connectionUrl);
        System.out.println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        
        try {
            // Kiểm tra driver
            Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
            System.out.println("✅ SQL Server JDBC Driver loaded successfully");
            
            // Thử kết nối
            System.out.println("🔄 Đang kết nối...");
            Connection conn = DriverManager.getConnection(connectionUrl);
            System.out.println("✅ Kết nối SQL Server thành công!");
            
            // Kiểm tra database
            Statement stmt = conn.createStatement();
            
            // Kiểm tra xem database có tồn tại không
            try {
                ResultSet rs = stmt.executeQuery("SELECT DB_NAME() as CurrentDatabase");
                if (rs.next()) {
                    String currentDb = rs.getString("CurrentDatabase");
                    System.out.println("📊 Database hiện tại: " + currentDb);
                }
            } catch (Exception e) {
                System.out.println("⚠️ Database chưa tồn tại, cần tạo mới");
            }
            
            // Kiểm tra tables
            try {
                ResultSet tables = stmt.executeQuery(
                    "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'"
                );
                
                System.out.println("📋 Danh sách tables:");
                boolean hasTables = false;
                while (tables.next()) {
                    System.out.println("   - " + tables.getString("TABLE_NAME"));
                    hasTables = true;
                }
                
                if (!hasTables) {
                    System.out.println("   (Chưa có tables nào - sẽ được tạo tự động)");
                }
                
            } catch (Exception e) {
                System.out.println("⚠️ Chưa có tables nào - sẽ được tạo tự động");
            }
            
            // Đóng kết nối
            conn.close();
            System.out.println("🔒 Đã đóng kết nối");
            
            System.out.println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            System.out.println("🎉 KẾT NỐI THÀNH CÔNG! Bạn có thể chạy ứng dụng ngay.");
            
        } catch (Exception e) {
            System.out.println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            System.out.println("❌ LỖI KẾT NỐI: " + e.getMessage());
            System.out.println();
            System.out.println("🔧 CÁCH KHẮC PHỤC:");
            System.out.println("1. Kiểm tra SQL Server đang chạy:");
            System.out.println("   - Mở Services.msc");
            System.out.println("   - Tìm 'SQL Server (MSSQLSERVER)' hoặc 'SQL Server (SQLEXPRESS)'");
            System.out.println("   - Đảm bảo service đang Running");
            System.out.println();
            System.out.println("2. Kiểm tra SQL Server Configuration:");
            System.out.println("   - Mở SQL Server Configuration Manager");
            System.out.println("   - SQL Server Network Configuration > Protocols");
            System.out.println("   - Enable TCP/IP");
            System.out.println("   - Kiểm tra port 1434 đang active");
            System.out.println();
            System.out.println("3. Kiểm tra tài khoản sa:");
            System.out.println("   - Mở SQL Server Management Studio");
            System.out.println("   - Connect với Windows Authentication");
            System.out.println("   - Security > Logins > sa");
            System.out.println("   - Đảm bảo account enabled và password đúng");
            System.out.println();
            System.out.println("4. Kiểm tra Firewall:");
            System.out.println("   - Cho phép port 1434 qua Windows Firewall");
            System.out.println("   - Hoặc tạm thời tắt firewall để test");
            System.out.println();
            System.out.println("5. Tạo database:");
            System.out.println("   - Chạy script create_database.sql trong SSMS");
            System.out.println("   - Hoặc để ứng dụng tự tạo");
            System.out.println();
            System.out.println("📁 Nếu không sửa được, ứng dụng sẽ dùng file storage.");
        }
    }
}
