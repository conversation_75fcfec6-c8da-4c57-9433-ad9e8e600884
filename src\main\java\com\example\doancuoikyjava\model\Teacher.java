package com.example.doancuoikyjava.model;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

public class Teacher extends User {
    private String teacherId;
    private String department;
    private String position;
    private double salary;
    private List<String> teachingCourses;
    private String qualification;
    private int experienceYears;

    public Teacher() {
        super();
        this.role = UserRole.TEACHER;
        this.teachingCourses = new ArrayList<>();
    }

    public Teacher(String userId, String username, String password, String fullName,
                   String email, String phone, LocalDate dateOfBirth, String address,
                   String teacherId, String department, String position, double salary,
                   String qualification, int experienceYears) {
        super(userId, username, password, fullName, email, phone, dateOfBirth, address, UserRole.TEACHER);
        this.teacherId = teacherId;
        this.department = department;
        this.position = position;
        this.salary = salary;
        this.qualification = qualification;
        this.experienceYears = experienceYears;
        this.teachingCourses = new ArrayList<>();
    }

    // Getters and Setters
    public String getTeacherId() { return teacherId; }
    public void setTeacherId(String teacherId) { this.teacherId = teacherId; }

    public String getDepartment() { return department; }
    public void setDepartment(String department) { this.department = department; }

    public String getPosition() { return position; }
    public void setPosition(String position) { this.position = position; }

    public double getSalary() { return salary; }
    public void setSalary(double salary) { this.salary = salary; }

    public List<String> getTeachingCourses() { return teachingCourses; }
    public void setTeachingCourses(List<String> teachingCourses) { this.teachingCourses = teachingCourses; }

    public String getQualification() { return qualification; }
    public void setQualification(String qualification) { this.qualification = qualification; }

    public int getExperienceYears() { return experienceYears; }
    public void setExperienceYears(int experienceYears) { this.experienceYears = experienceYears; }

    // Utility methods
    public void assignCourse(String courseId) {
        if (!teachingCourses.contains(courseId)) {
            teachingCourses.add(courseId);
        }
    }

    public void removeCourse(String courseId) {
        teachingCourses.remove(courseId);
    }

    @Override
    public String toString() {
        return "Teacher{" +
                "teacherId='" + teacherId + '\'' +
                ", fullName='" + fullName + '\'' +
                ", department='" + department + '\'' +
                ", position='" + position + '\'' +
                ", qualification='" + qualification + '\'' +
                ", experienceYears=" + experienceYears +
                '}';
    }
}
