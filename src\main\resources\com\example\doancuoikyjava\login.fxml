<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.shape.*?>

<StackPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.example.doancuoikyjava.controller.LoginController">
   <children>
      <!-- Background Gradient -->
      <Rectangle fill="linear-gradient(to bottom right, #667eea, #764ba2)" width="2000" height="2000" />

      <!-- Animated Background Shapes -->
      <Circle fill="rgba(255,255,255,0.1)" radius="150" translateX="-300" translateY="-200" />
      <Circle fill="rgba(255,255,255,0.05)" radius="100" translateX="400" translateY="-300" />
      <Circle fill="rgba(255,255,255,0.08)" radius="80" translateX="-400" translateY="250" />
      <Circle fill="rgba(255,255,255,0.06)" radius="120" translateX="350" translateY="200" />

      <!-- Main Login Container -->
      <HBox alignment="CENTER" maxHeight="600" maxWidth="900" style="-fx-background-color: rgba(255,255,255,0.95); -fx-background-radius: 20; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 30, 0, 0, 10);">
         <children>
            <!-- Left Side - Welcome Section -->
            <VBox alignment="CENTER" prefWidth="400" spacing="30" style="-fx-background-color: linear-gradient(to bottom, #667eea, #764ba2); -fx-background-radius: 20 0 0 20; -fx-padding: 50;">
               <children>
                  <!-- Logo/Icon -->
                  <VBox alignment="CENTER" spacing="20">
                     <children>
                        <Circle fill="rgba(255,255,255,0.2)" radius="50" stroke="white" strokeWidth="3" />
                        <Label text="🎓" style="-fx-font-size: 48px;" />
                     </children>
                  </VBox>

                  <!-- Welcome Text -->
                  <VBox alignment="CENTER" spacing="15">
                     <children>
                        <Label text="CHÀO MỪNG ĐÉN VỚI" style="-fx-text-fill: white; -fx-font-size: 18px; -fx-font-weight: bold; -fx-letter-spacing: 2px;" />
                        <Label text="HỆ THỐNG QUẢN LÝ" style="-fx-text-fill: white; -fx-font-size: 28px; -fx-font-weight: bold;" />
                        <Label text="SINH VIÊN &amp; GIÁO VIÊN" style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 16px; -fx-font-weight: normal;" />
                     </children>
                  </VBox>

                  <!-- Features List -->
                  <VBox spacing="12" style="-fx-padding: 20 0 0 0;">
                     <children>
                        <HBox spacing="10" alignment="CENTER_LEFT">
                           <children>
                              <Label text="✓" style="-fx-text-fill: #4CAF50; -fx-font-size: 16px; -fx-font-weight: bold;" />
                              <Label text="Quản lý thông tin sinh viên" style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px;" />
                           </children>
                        </HBox>
                        <HBox spacing="10" alignment="CENTER_LEFT">
                           <children>
                              <Label text="✓" style="-fx-text-fill: #4CAF50; -fx-font-size: 16px; -fx-font-weight: bold;" />
                              <Label text="Theo dõi điểm số và GPA" style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px;" />
                           </children>
                        </HBox>
                        <HBox spacing="10" alignment="CENTER_LEFT">
                           <children>
                              <Label text="✓" style="-fx-text-fill: #4CAF50; -fx-font-size: 16px; -fx-font-weight: bold;" />
                              <Label text="Báo cáo và thống kê chi tiết" style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px;" />
                           </children>
                        </HBox>
                        <HBox spacing="10" alignment="CENTER_LEFT">
                           <children>
                              <Label text="✓" style="-fx-text-fill: #4CAF50; -fx-font-size: 16px; -fx-font-weight: bold;" />
                              <Label text="Giao diện thân thiện, dễ sử dụng" style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 14px;" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
               </children>
            </VBox>

            <!-- Right Side - Login Form -->
            <VBox alignment="CENTER" prefWidth="500" spacing="25" style="-fx-padding: 50;">
               <children>
                  <!-- Login Header -->
                  <VBox alignment="CENTER" spacing="10">
                     <children>
                        <Label text="ĐĂNG NHẬP" style="-fx-font-size: 32px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" />
                        <Label text="Vui lòng nhập thông tin để tiếp tục" style="-fx-font-size: 14px; -fx-text-fill: #7f8c8d;" />
                     </children>
                  </VBox>

                  <!-- Login Form -->
                  <VBox spacing="20" style="-fx-max-width: 350;">
                     <children>
                        <!-- Username Field -->
                        <VBox spacing="8">
                           <children>
                              <Label text="👤 Tên đăng nhập" style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #34495e;" />
                              <TextField fx:id="usernameField" promptText="Nhập tên đăng nhập"
                                         style="-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-radius: 8; -fx-background-radius: 8; -fx-padding: 12; -fx-font-size: 14px; -fx-pref-height: 45;" />
                           </children>
                        </VBox>

                        <!-- Password Field -->
                        <VBox spacing="8">
                           <children>
                              <Label text="🔒 Mật khẩu" style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #34495e;" />
                              <PasswordField fx:id="passwordField" promptText="Nhập mật khẩu"
                                             style="-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-radius: 8; -fx-background-radius: 8; -fx-padding: 12; -fx-font-size: 14px; -fx-pref-height: 45;" />
                           </children>
                        </VBox>

                        <!-- Role ComboBox -->
                        <VBox spacing="8">
                           <children>
                              <Label text="🎭 Vai trò" style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #34495e;" />
                              <ComboBox fx:id="roleComboBox" promptText="Chọn vai trò" maxWidth="Infinity"
                                        style="-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-radius: 8; -fx-background-radius: 8; -fx-padding: 12; -fx-font-size: 14px; -fx-pref-height: 45;" />
                           </children>
                        </VBox>
                     </children>
                  </VBox>

                  <!-- Login Button -->
                  <VBox spacing="15" style="-fx-max-width: 350;">
                     <children>
                        <Button fx:id="loginButton" onAction="#handleLogin" text="🚀 ĐĂNG NHẬP" maxWidth="Infinity"
                                style="-fx-background-color: linear-gradient(to right, #667eea, #764ba2); -fx-text-fill: white; -fx-font-size: 16px; -fx-font-weight: bold; -fx-background-radius: 8; -fx-padding: 15; -fx-cursor: hand;" />

                        <Label fx:id="errorLabel" visible="false" wrapText="true" maxWidth="350"
                               style="-fx-text-fill: #e74c3c; -fx-font-size: 12px; -fx-background-color: rgba(231, 76, 60, 0.1); -fx-background-radius: 5; -fx-padding: 8;" />
                     </children>
                  </VBox>

                  <!-- Divider -->
                  <Separator style="-fx-background-color: #ecf0f1; -fx-pref-width: 350;" />

                  <!-- Demo Accounts -->
                  <VBox alignment="CENTER" spacing="15" style="-fx-max-width: 350;">
                     <children>
                        <Label text="🔑 Tài khoản demo" style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" />
                        <VBox spacing="8">
                           <children>
                              <Button onAction="#quickLoginAdmin" maxWidth="Infinity" style="-fx-background-color: rgba(52, 152, 219, 0.1); -fx-background-radius: 8; -fx-padding: 12; -fx-border-color: rgba(52, 152, 219, 0.3); -fx-border-radius: 8; -fx-border-width: 1; -fx-cursor: hand;">
                                 <graphic>
                                    <HBox spacing="10" alignment="CENTER_LEFT">
                                       <children>
                                          <Label text="👨‍💼" style="-fx-font-size: 16px;" />
                                          <Label text="Admin:" style="-fx-font-weight: bold; -fx-text-fill: #2c3e50;" />
                                          <Label text="admin / admin123" style="-fx-text-fill: #3498db; -fx-font-family: monospace;" />
                                       </children>
                                    </HBox>
                                 </graphic>
                              </Button>
                              <Button onAction="#quickLoginTeacher" maxWidth="Infinity" style="-fx-background-color: rgba(46, 204, 113, 0.1); -fx-background-radius: 8; -fx-padding: 12; -fx-border-color: rgba(46, 204, 113, 0.3); -fx-border-radius: 8; -fx-border-width: 1; -fx-cursor: hand;">
                                 <graphic>
                                    <HBox spacing="10" alignment="CENTER_LEFT">
                                       <children>
                                          <Label text="👨‍🏫" style="-fx-font-size: 16px;" />
                                          <Label text="Giáo viên:" style="-fx-font-weight: bold; -fx-text-fill: #2c3e50;" />
                                          <Label text="teacher / teacher123" style="-fx-text-fill: #27ae60; -fx-font-family: monospace;" />
                                       </children>
                                    </HBox>
                                 </graphic>
                              </Button>
                              <Button onAction="#quickLoginStudent" maxWidth="Infinity" style="-fx-background-color: rgba(155, 89, 182, 0.1); -fx-background-radius: 8; -fx-padding: 12; -fx-border-color: rgba(155, 89, 182, 0.3); -fx-border-radius: 8; -fx-border-width: 1; -fx-cursor: hand;">
                                 <graphic>
                                    <HBox spacing="10" alignment="CENTER_LEFT">
                                       <children>
                                          <Label text="👨‍🎓" style="-fx-font-size: 16px;" />
                                          <Label text="Sinh viên:" style="-fx-font-weight: bold; -fx-text-fill: #2c3e50;" />
                                          <Label text="student / student123" style="-fx-text-fill: #9b59b6; -fx-font-family: monospace;" />
                                       </children>
                                    </HBox>
                                 </graphic>
                              </Button>
                           </children>
                        </VBox>
                     </children>
                  </VBox>
               </children>
            </VBox>
         </children>
      </HBox>
   </children>
</StackPane>
