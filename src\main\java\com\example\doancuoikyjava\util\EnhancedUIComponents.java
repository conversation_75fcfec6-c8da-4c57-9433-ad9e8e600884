package com.example.doancuoikyjava.util;

import javafx.animation.*;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Node;
import javafx.scene.control.*;
import javafx.scene.effect.DropShadow;
import javafx.scene.effect.GaussianBlur;
import javafx.scene.layout.*;
import javafx.scene.paint.Color;
import javafx.scene.text.Font;
import javafx.scene.text.FontWeight;
import javafx.util.Duration;

/**
 * Enhanced UI Components with modern styling and animations
 */
public class EnhancedUIComponents {
    
    // Color scheme
    public static final String PRIMARY_COLOR = "#2196F3";
    public static final String SECONDARY_COLOR = "#FFC107";
    public static final String SUCCESS_COLOR = "#4CAF50";
    public static final String WARNING_COLOR = "#FF9800";
    public static final String ERROR_COLOR = "#F44336";
    public static final String DARK_COLOR = "#212121";
    public static final String LIGHT_COLOR = "#FAFAFA";
    
    /**
     * Create enhanced button with modern styling
     */
    public static Button createEnhancedButton(String text, String colorClass) {
        Button button = new Button(text);
        button.getStyleClass().addAll("enhanced-button", colorClass);
        
        // Add hover animation
        addHoverAnimation(button);
        
        // Enhanced styling
        button.setStyle(
            "-fx-background-radius: 8px; " +
            "-fx-padding: 12px 24px; " +
            "-fx-font-size: 14px; " +
            "-fx-font-weight: bold; " +
            "-fx-cursor: hand; " +
            "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 4, 0, 0, 2);"
        );
        
        return button;
    }
    
    /**
     * Create primary button
     */
    public static Button createPrimaryButton(String text) {
        Button button = createEnhancedButton(text, "primary");
        button.setStyle(button.getStyle() + 
            "-fx-background-color: " + PRIMARY_COLOR + "; " +
            "-fx-text-fill: white;"
        );
        return button;
    }
    
    /**
     * Create success button
     */
    public static Button createSuccessButton(String text) {
        Button button = createEnhancedButton(text, "success");
        button.setStyle(button.getStyle() + 
            "-fx-background-color: " + SUCCESS_COLOR + "; " +
            "-fx-text-fill: white;"
        );
        return button;
    }
    
    /**
     * Create warning button
     */
    public static Button createWarningButton(String text) {
        Button button = createEnhancedButton(text, "warning");
        button.setStyle(button.getStyle() + 
            "-fx-background-color: " + WARNING_COLOR + "; " +
            "-fx-text-fill: white;"
        );
        return button;
    }
    
    /**
     * Create enhanced card container
     */
    public static VBox createCard(String title, Node content) {
        VBox card = new VBox(15);
        card.getStyleClass().add("enhanced-card");
        card.setPadding(new Insets(20));
        card.setStyle(
            "-fx-background-color: white; " +
            "-fx-background-radius: 12px; " +
            "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 8, 0, 0, 4);"
        );
        
        if (title != null && !title.isEmpty()) {
            Label titleLabel = new Label(title);
            titleLabel.setFont(Font.font("System", FontWeight.BOLD, 18));
            titleLabel.setTextFill(Color.web(DARK_COLOR));
            card.getChildren().add(titleLabel);
        }
        
        if (content != null) {
            card.getChildren().add(content);
        }
        
        // Add entrance animation
        addEntranceAnimation(card);
        
        return card;
    }
    
    /**
     * Create enhanced text field
     */
    public static TextField createEnhancedTextField(String promptText) {
        TextField textField = new TextField();
        textField.setPromptText(promptText);
        textField.getStyleClass().add("enhanced-textfield");
        textField.setStyle(
            "-fx-background-radius: 8px; " +
            "-fx-border-radius: 8px; " +
            "-fx-border-color: #E0E0E0; " +
            "-fx-border-width: 2px; " +
            "-fx-padding: 12px; " +
            "-fx-font-size: 14px;"
        );
        
        // Add focus animation
        addFocusAnimation(textField);
        
        return textField;
    }
    
    /**
     * Create enhanced combo box
     */
    public static <T> ComboBox<T> createEnhancedComboBox() {
        ComboBox<T> comboBox = new ComboBox<>();
        comboBox.getStyleClass().add("enhanced-combobox");
        comboBox.setStyle(
            "-fx-background-radius: 8px; " +
            "-fx-border-radius: 8px; " +
            "-fx-border-color: #E0E0E0; " +
            "-fx-border-width: 2px; " +
            "-fx-padding: 8px; " +
            "-fx-font-size: 14px;"
        );
        
        return comboBox;
    }
    
    /**
     * Create enhanced table view
     */
    public static <T> TableView<T> createEnhancedTableView() {
        TableView<T> tableView = new TableView<>();
        tableView.getStyleClass().add("enhanced-table");
        tableView.setStyle(
            "-fx-background-color: white; " +
            "-fx-background-radius: 8px; " +
            "-fx-border-radius: 8px; " +
            "-fx-border-color: #E0E0E0; " +
            "-fx-border-width: 1px;"
        );
        
        // Enhanced row factory for alternating colors
        tableView.setRowFactory(tv -> {
            TableRow<T> row = new TableRow<>();
            row.itemProperty().addListener((obs, oldItem, newItem) -> {
                if (newItem != null) {
                    if (row.getIndex() % 2 == 0) {
                        row.setStyle("-fx-background-color: #FAFAFA;");
                    } else {
                        row.setStyle("-fx-background-color: white;");
                    }
                }
            });
            return row;
        });
        
        return tableView;
    }
    
    /**
     * Create loading indicator
     */
    public static ProgressIndicator createLoadingIndicator() {
        ProgressIndicator indicator = new ProgressIndicator();
        indicator.setStyle("-fx-accent: " + PRIMARY_COLOR + ";");
        indicator.setPrefSize(50, 50);
        
        // Add rotation animation
        RotateTransition rotate = new RotateTransition(Duration.seconds(2), indicator);
        rotate.setByAngle(360);
        rotate.setCycleCount(Animation.INDEFINITE);
        rotate.play();
        
        return indicator;
    }
    
    /**
     * Create enhanced alert dialog
     */
    public static Alert createEnhancedAlert(Alert.AlertType type, String title, String message) {
        Alert alert = new Alert(type);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        
        // Style the dialog
        DialogPane dialogPane = alert.getDialogPane();
        dialogPane.getStylesheets().add(
            EnhancedUIComponents.class.getResource("/com/example/doancuoikyjava/enhanced-styles.css").toExternalForm()
        );
        dialogPane.getStyleClass().add("enhanced-dialog");
        
        return alert;
    }
    
    /**
     * Create notification toast
     */
    public static void showNotificationToast(String message, String type, Node parent) {
        Label toast = new Label(message);
        toast.getStyleClass().addAll("notification-toast", type);
        toast.setStyle(
            "-fx-background-color: " + getColorForType(type) + "; " +
            "-fx-text-fill: white; " +
            "-fx-padding: 12px 20px; " +
            "-fx-background-radius: 25px; " +
            "-fx-font-size: 14px; " +
            "-fx-font-weight: bold; " +
            "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 6, 0, 0, 3);"
        );
        
        // Position toast
        if (parent instanceof Pane) {
            Pane parentPane = (Pane) parent;
            toast.setLayoutX(parentPane.getWidth() - toast.getWidth() - 20);
            toast.setLayoutY(20);
            parentPane.getChildren().add(toast);
            
            // Auto-hide animation
            Timeline timeline = new Timeline(
                new KeyFrame(Duration.seconds(3), e -> {
                    FadeTransition fade = new FadeTransition(Duration.seconds(0.5), toast);
                    fade.setToValue(0);
                    fade.setOnFinished(event -> parentPane.getChildren().remove(toast));
                    fade.play();
                })
            );
            timeline.play();
        }
    }
    
    /**
     * Create statistics card
     */
    public static VBox createStatCard(String title, String value, String icon) {
        VBox card = new VBox(10);
        card.setAlignment(Pos.CENTER);
        card.setPadding(new Insets(20));
        card.getStyleClass().add("stat-card");
        card.setStyle(
            "-fx-background-color: linear-gradient(135deg, " + PRIMARY_COLOR + ", #1976D2); " +
            "-fx-background-radius: 12px; " +
            "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 8, 0, 0, 4);"
        );
        
        Label iconLabel = new Label(icon);
        iconLabel.setFont(Font.font(24));
        iconLabel.setTextFill(Color.WHITE);
        
        Label valueLabel = new Label(value);
        valueLabel.setFont(Font.font("System", FontWeight.BOLD, 32));
        valueLabel.setTextFill(Color.WHITE);
        
        Label titleLabel = new Label(title);
        titleLabel.setFont(Font.font("System", FontWeight.NORMAL, 14));
        titleLabel.setTextFill(Color.web("#E3F2FD"));
        
        card.getChildren().addAll(iconLabel, valueLabel, titleLabel);
        
        // Add hover effect
        addHoverAnimation(card);
        
        return card;
    }
    
    /**
     * Add hover animation to node
     */
    public static void addHoverAnimation(Node node) {
        ScaleTransition scaleIn = new ScaleTransition(Duration.millis(100), node);
        scaleIn.setToX(1.05);
        scaleIn.setToY(1.05);
        
        ScaleTransition scaleOut = new ScaleTransition(Duration.millis(100), node);
        scaleOut.setToX(1.0);
        scaleOut.setToY(1.0);
        
        node.setOnMouseEntered(e -> scaleIn.play());
        node.setOnMouseExited(e -> scaleOut.play());
    }
    
    /**
     * Add entrance animation
     */
    public static void addEntranceAnimation(Node node) {
        node.setOpacity(0);
        node.setTranslateY(20);
        
        FadeTransition fade = new FadeTransition(Duration.millis(500), node);
        fade.setToValue(1);
        
        TranslateTransition translate = new TranslateTransition(Duration.millis(500), node);
        translate.setToY(0);
        
        ParallelTransition parallel = new ParallelTransition(fade, translate);
        parallel.play();
    }
    
    /**
     * Add focus animation to text field
     */
    public static void addFocusAnimation(TextField textField) {
        textField.focusedProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal) {
                textField.setStyle(textField.getStyle() + "-fx-border-color: " + PRIMARY_COLOR + ";");
            } else {
                textField.setStyle(textField.getStyle().replace("-fx-border-color: " + PRIMARY_COLOR + ";", "-fx-border-color: #E0E0E0;"));
            }
        });
    }
    
    /**
     * Get color for notification type
     */
    private static String getColorForType(String type) {
        return switch (type.toLowerCase()) {
            case "success" -> SUCCESS_COLOR;
            case "warning" -> WARNING_COLOR;
            case "error" -> ERROR_COLOR;
            default -> PRIMARY_COLOR;
        };
    }
    
    /**
     * Create enhanced progress bar
     */
    public static ProgressBar createEnhancedProgressBar() {
        ProgressBar progressBar = new ProgressBar();
        progressBar.getStyleClass().add("enhanced-progress");
        progressBar.setStyle(
            "-fx-accent: " + PRIMARY_COLOR + "; " +
            "-fx-background-radius: 10px; " +
            "-fx-background-insets: 0;"
        );
        return progressBar;
    }
    
    /**
     * Create enhanced separator
     */
    public static Separator createEnhancedSeparator() {
        Separator separator = new Separator();
        separator.setStyle("-fx-background-color: #E0E0E0;");
        return separator;
    }

    /**
     * Create deep ocean blue gradient button
     */
    public static Button createDeepOceanButton(String text) {
        Button button = new Button(text);
        button.getStyleClass().addAll("enhanced-button", "primary");
        button.setStyle(
            "-fx-background-color: radial-gradient(circle at center, #0D47A1 0%, #1565C0 50%, #0A2E5C 100%); " +
            "-fx-background-radius: 35px; " +
            "-fx-text-fill: white; " +
            "-fx-font-weight: bold; " +
            "-fx-font-size: 18px; " +
            "-fx-padding: 20px 35px; " +
            "-fx-border-color: rgba(255,255,255,0.9); " +
            "-fx-border-width: 4px; " +
            "-fx-border-radius: 35px; " +
            "-fx-effect: dropshadow(gaussian, rgba(13,71,161,0.9), 25, 0, 0, 10);"
        );
        addHoverAnimation(button);
        addGlowEffect(button);
        return button;
    }

    /**
     * Create emerald green themed button
     */
    public static Button createEmeraldGreenButton(String text) {
        Button button = new Button(text);
        button.getStyleClass().addAll("enhanced-button", "success");
        button.setStyle(
            "-fx-background-color: radial-gradient(circle at center, #00C851 0%, #007E33 50%, #004D20 100%); " +
            "-fx-background-radius: 35px; " +
            "-fx-text-fill: white; " +
            "-fx-font-weight: bold; " +
            "-fx-font-size: 16px; " +
            "-fx-padding: 18px 32px; " +
            "-fx-border-color: rgba(255,255,255,0.8); " +
            "-fx-border-width: 3px; " +
            "-fx-border-radius: 35px; " +
            "-fx-effect: dropshadow(gaussian, rgba(0,200,81,0.8), 18, 0, 0, 7);"
        );
        addHoverAnimation(button);
        return button;
    }

    /**
     * Create sunset orange themed button
     */
    public static Button createSunsetOrangeButton(String text) {
        Button button = new Button(text);
        button.getStyleClass().addAll("enhanced-button", "warning");
        button.setStyle(
            "-fx-background-color: radial-gradient(circle at center, #FF9500 0%, #FF6D00 50%, #E65100 100%); " +
            "-fx-background-radius: 35px; " +
            "-fx-text-fill: white; " +
            "-fx-font-weight: bold; " +
            "-fx-font-size: 16px; " +
            "-fx-padding: 18px 32px; " +
            "-fx-border-color: rgba(255,255,255,0.8); " +
            "-fx-border-width: 3px; " +
            "-fx-border-radius: 35px; " +
            "-fx-effect: dropshadow(gaussian, rgba(255,149,0,0.8), 18, 0, 0, 7);"
        );
        addHoverAnimation(button);
        return button;
    }

    /**
     * Create purple themed button
     */
    public static Button createPurpleButton(String text) {
        Button button = new Button(text);
        button.getStyleClass().addAll("enhanced-button", "secondary");
        button.setStyle(
            "-fx-background-color: radial-gradient(circle at center, #9C27B0 0%, #7B1FA2 50%, #4A148C 100%); " +
            "-fx-background-radius: 35px; " +
            "-fx-text-fill: white; " +
            "-fx-font-weight: bold; " +
            "-fx-font-size: 16px; " +
            "-fx-padding: 18px 32px; " +
            "-fx-border-color: rgba(255,255,255,0.8); " +
            "-fx-border-width: 3px; " +
            "-fx-border-radius: 35px; " +
            "-fx-effect: dropshadow(gaussian, rgba(156,39,176,0.8), 18, 0, 0, 7);"
        );
        addHoverAnimation(button);
        return button;
    }

    /**
     * Create crimson red themed button
     */
    public static Button createCrimsonRedButton(String text) {
        Button button = new Button(text);
        button.getStyleClass().addAll("enhanced-button", "error");
        button.setStyle(
            "-fx-background-color: radial-gradient(circle at center, #DC143C 0%, #B71C1C 50%, #8B0000 100%); " +
            "-fx-background-radius: 35px; " +
            "-fx-text-fill: white; " +
            "-fx-font-weight: bold; " +
            "-fx-font-size: 16px; " +
            "-fx-padding: 18px 32px; " +
            "-fx-border-color: rgba(255,255,255,0.8); " +
            "-fx-border-width: 3px; " +
            "-fx-border-radius: 35px; " +
            "-fx-effect: dropshadow(gaussian, rgba(220,20,60,0.8), 18, 0, 0, 7);"
        );
        addHoverAnimation(button);
        return button;
    }

    /**
     * Create colorful card with gradient background
     */
    public static VBox createColorfulCard(String title, Node content, String gradientClass) {
        VBox card = new VBox(15);
        card.getStyleClass().addAll("enhanced-card", gradientClass);
        card.setPadding(new Insets(25));

        String gradientStyle = switch (gradientClass) {
            case "rainbow" -> "-fx-background-color: linear-gradient(135deg, #667eea 0%, #764ba2 100%);";
            case "ocean" -> "-fx-background-color: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);";
            case "sunset" -> "-fx-background-color: linear-gradient(135deg, #fa709a 0%, #fee140 100%);";
            case "forest" -> "-fx-background-color: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);";
            default -> "-fx-background-color: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);";
        };

        card.setStyle(
            gradientStyle +
            "-fx-background-radius: 20px; " +
            "-fx-effect: dropshadow(gaussian, rgba(102,126,234,0.4), 15, 0, 0, 8);"
        );

        if (title != null && !title.isEmpty()) {
            Label titleLabel = new Label(title);
            titleLabel.setFont(Font.font("System", FontWeight.BOLD, 20));
            titleLabel.setTextFill(Color.WHITE);
            titleLabel.getStyleClass().add("gradient-text");
            card.getChildren().add(titleLabel);
        }

        if (content != null) {
            card.getChildren().add(content);
        }

        addEntranceAnimation(card);
        addHoverAnimation(card);

        return card;
    }

    /**
     * Add glow effect to node
     */
    public static void addGlowEffect(Node node) {
        node.getStyleClass().add("glow");

        // Pulse animation
        ScaleTransition pulse = new ScaleTransition(Duration.seconds(1), node);
        pulse.setFromX(1.0);
        pulse.setFromY(1.0);
        pulse.setToX(1.02);
        pulse.setToY(1.02);
        pulse.setCycleCount(Animation.INDEFINITE);
        pulse.setAutoReverse(true);
        pulse.play();
    }

    /**
     * Add pulse effect to node
     */
    public static void addPulseEffect(Node node) {
        node.getStyleClass().add("pulse");

        Timeline pulse = new Timeline(
            new KeyFrame(Duration.ZERO, new KeyValue(node.scaleXProperty(), 1.0)),
            new KeyFrame(Duration.seconds(0.5), new KeyValue(node.scaleXProperty(), 1.05)),
            new KeyFrame(Duration.seconds(1.0), new KeyValue(node.scaleXProperty(), 1.0))
        );
        pulse.setCycleCount(Animation.INDEFINITE);
        pulse.play();
    }

    /**
     * Create neon text label
     */
    public static Label createNeonLabel(String text) {
        Label label = new Label(text);
        label.getStyleClass().add("neon-glow");
        label.setStyle(
            "-fx-text-fill: #00f2fe; " +
            "-fx-font-weight: bold; " +
            "-fx-font-size: 18px; " +
            "-fx-effect: dropshadow(gaussian, #00f2fe, 15, 0, 0, 0);"
        );
        return label;
    }

    /**
     * Create floating animation for node
     */
    public static void addFloatingAnimation(Node node) {
        TranslateTransition float1 = new TranslateTransition(Duration.seconds(2), node);
        float1.setFromY(0);
        float1.setToY(-10);
        float1.setCycleCount(Animation.INDEFINITE);
        float1.setAutoReverse(true);
        float1.play();
    }
}
