package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.*;
import com.example.doancuoikyjava.service.*;
import com.example.doancuoikyjava.util.SceneManager;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.HBox;

import java.io.IOException;
import java.net.URL;
import java.util.List;
import java.util.ResourceBundle;
import java.util.stream.Collectors;

public class TeacherStudentsController implements Initializable {
    
    @FXML private Label welcomeLabel;
    @FXML private Button backButton;
    @FXML private Button refreshBtn;
    @FXML private Button searchBtn;
    @FXML private Button exportBtn;
    @FXML private Button sendEmailBtn;
    
    @FXML private ComboBox<String> courseFilterComboBox;
    @FXML private TextField searchField;
    
    @FXML private TableView<StudentCourseInfo> studentsTableView;
    @FXML private TableColumn<StudentCourseInfo, String> studentIdColumn;
    @FXML private TableColumn<StudentCourseInfo, String> fullNameColumn;
    @FXML private TableColumn<StudentCourseInfo, String> classNameColumn;
    @FXML private TableColumn<StudentCourseInfo, String> majorColumn;
    @FXML private TableColumn<StudentCourseInfo, Integer> yearColumn;
    @FXML private TableColumn<StudentCourseInfo, String> emailColumn;
    @FXML private TableColumn<StudentCourseInfo, String> phoneColumn;
    @FXML private TableColumn<StudentCourseInfo, String> courseColumn;
    @FXML private TableColumn<StudentCourseInfo, Void> actionsColumn;
    
    @FXML private Label totalStudentsLabel;
    @FXML private Label totalCoursesLabel;
    @FXML private Label averageGpaLabel;
    @FXML private Label filteredStudentsLabel;
    
    private UserService userService;
    private CourseService courseService;
    private GradeService gradeService;
    private Teacher currentTeacher;
    private ObservableList<StudentCourseInfo> allStudents;
    private ObservableList<StudentCourseInfo> filteredStudents;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        userService = new UserService();
        courseService = new CourseService();
        gradeService = new GradeService();
        allStudents = FXCollections.observableArrayList();
        filteredStudents = FXCollections.observableArrayList();
        
        setupCurrentTeacher();
        setupWelcomeMessage();
        setupTableColumns();
        setupFilters();
        loadData();
    }
    
    private void setupCurrentTeacher() {
        User currentUser = SceneManager.getCurrentUser();
        if (currentUser instanceof Teacher) {
            currentTeacher = (Teacher) currentUser;
        }
    }
    
    private void setupWelcomeMessage() {
        if (currentTeacher != null) {
            welcomeLabel.setText("Xin chào, " + currentTeacher.getFullName());
        }
    }
    
    private void setupTableColumns() {
        studentIdColumn.setCellValueFactory(new PropertyValueFactory<>("studentId"));
        fullNameColumn.setCellValueFactory(new PropertyValueFactory<>("fullName"));
        classNameColumn.setCellValueFactory(new PropertyValueFactory<>("className"));
        majorColumn.setCellValueFactory(new PropertyValueFactory<>("major"));
        yearColumn.setCellValueFactory(new PropertyValueFactory<>("year"));
        emailColumn.setCellValueFactory(new PropertyValueFactory<>("email"));
        phoneColumn.setCellValueFactory(new PropertyValueFactory<>("phone"));
        courseColumn.setCellValueFactory(new PropertyValueFactory<>("courseName"));
        
        // Actions column with buttons
        actionsColumn.setCellFactory(param -> new TableCell<StudentCourseInfo, Void>() {
            private final Button viewBtn = new Button("Xem");
            private final Button gradeBtn = new Button("Điểm");
            private final HBox buttons = new HBox(5, viewBtn, gradeBtn);
            
            {
                viewBtn.setStyle("-fx-background-color: #2196F3; -fx-text-fill: white; -fx-font-size: 10px;");
                gradeBtn.setStyle("-fx-background-color: #4CAF50; -fx-text-fill: white; -fx-font-size: 10px;");
                
                viewBtn.setOnAction(e -> {
                    StudentCourseInfo info = getTableView().getItems().get(getIndex());
                    showStudentDetails(info);
                });
                
                gradeBtn.setOnAction(e -> {
                    StudentCourseInfo info = getTableView().getItems().get(getIndex());
                    showStudentGrades(info);
                });
            }
            
            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(buttons);
                }
            }
        });
        
        studentsTableView.setItems(filteredStudents);
    }
    
    private void setupFilters() {
        // Setup search functionality
        searchField.textProperty().addListener((obs, oldText, newText) -> filterStudents());
        courseFilterComboBox.valueProperty().addListener((obs, oldValue, newValue) -> filterStudents());
    }
    
    private void loadData() {
        if (currentTeacher == null) return;
        
        // Load courses taught by this teacher
        List<Course> teacherCourses = courseService.getCoursesByTeacher(currentTeacher.getTeacherId());
        
        // Update course filter
        List<String> courseNames = teacherCourses.stream()
                .map(Course::getCourseName)
                .collect(Collectors.toList());
        courseNames.add(0, "Tất cả môn học");
        courseFilterComboBox.setItems(FXCollections.observableArrayList(courseNames));
        courseFilterComboBox.setValue("Tất cả môn học");
        
        // Load students enrolled in teacher's courses
        allStudents.clear();
        for (Course course : teacherCourses) {
            for (String studentId : course.getEnrolledStudents()) {
                User user = userService.getUserById(studentId);
                if (user instanceof Student) {
                    Student student = (Student) user;
                    StudentCourseInfo info = new StudentCourseInfo();
                    info.setStudentId(student.getStudentId());
                    info.setFullName(student.getFullName());
                    info.setClassName(student.getClassName());
                    info.setMajor(student.getMajor());
                    info.setYear(student.getYear());
                    info.setEmail(student.getEmail());
                    info.setPhone(student.getPhone());
                    info.setCourseId(course.getCourseId());
                    info.setCourseName(course.getCourseName());
                    info.setGpa(student.getGpa());
                    
                    allStudents.add(info);
                }
            }
        }
        
        filterStudents();
        updateStatistics();
    }
    
    private void filterStudents() {
        String searchText = searchField.getText().toLowerCase();
        String selectedCourse = courseFilterComboBox.getValue();
        
        List<StudentCourseInfo> filtered = allStudents.stream()
                .filter(info -> {
                    boolean matchesSearch = searchText.isEmpty() || 
                            info.getFullName().toLowerCase().contains(searchText) ||
                            info.getStudentId().toLowerCase().contains(searchText);
                    
                    boolean matchesCourse = selectedCourse == null || 
                            selectedCourse.equals("Tất cả môn học") ||
                            info.getCourseName().equals(selectedCourse);
                    
                    return matchesSearch && matchesCourse;
                })
                .collect(Collectors.toList());
        
        filteredStudents.setAll(filtered);
        filteredStudentsLabel.setText("Hiển thị: " + filtered.size() + " sinh viên");
    }
    
    private void updateStatistics() {
        if (currentTeacher == null) return;
        
        // Count unique students
        long uniqueStudents = allStudents.stream()
                .map(StudentCourseInfo::getStudentId)
                .distinct()
                .count();
        
        totalStudentsLabel.setText(String.valueOf(uniqueStudents));
        
        // Count courses
        List<Course> teacherCourses = courseService.getCoursesByTeacher(currentTeacher.getTeacherId());
        totalCoursesLabel.setText(String.valueOf(teacherCourses.size()));
        
        // Calculate average GPA
        double avgGpa = allStudents.stream()
                .mapToDouble(StudentCourseInfo::getGpa)
                .average()
                .orElse(0.0);
        averageGpaLabel.setText(String.format("%.2f", avgGpa));
    }
    
    private void showStudentDetails(StudentCourseInfo info) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Thông tin sinh viên");
        alert.setHeaderText("Chi tiết sinh viên: " + info.getFullName());
        
        StringBuilder content = new StringBuilder();
        content.append("MSSV: ").append(info.getStudentId()).append("\n");
        content.append("Họ tên: ").append(info.getFullName()).append("\n");
        content.append("Lớp: ").append(info.getClassName()).append("\n");
        content.append("Ngành: ").append(info.getMajor()).append("\n");
        content.append("Năm học: ").append(info.getYear()).append("\n");
        content.append("Email: ").append(info.getEmail()).append("\n");
        content.append("Điện thoại: ").append(info.getPhone()).append("\n");
        content.append("GPA: ").append(String.format("%.2f", info.getGpa())).append("\n");
        content.append("Môn học đăng ký: ").append(info.getCourseName());
        
        alert.setContentText(content.toString());
        alert.showAndWait();
    }
    
    private void showStudentGrades(StudentCourseInfo info) {
        List<Grade> studentGrades = gradeService.getGradesByStudent(info.getStudentId());
        
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Bảng điểm sinh viên");
        alert.setHeaderText("Điểm số của: " + info.getFullName());
        
        if (studentGrades.isEmpty()) {
            alert.setContentText("Sinh viên chưa có điểm nào.");
        } else {
            StringBuilder content = new StringBuilder();
            content.append("BẢNG ĐIỂM:\n");
            content.append("Môn học\t\tĐiểm\tXếp loại\tHọc kỳ\n");
            content.append("----------------------------------------\n");
            
            for (Grade grade : studentGrades) {
                content.append(grade.getCourseName()).append("\t");
                content.append(grade.getScore()).append("\t");
                content.append(grade.getLetterGrade()).append("\t");
                content.append(grade.getSemester()).append("\n");
            }
            
            content.append("\nGPA: ").append(String.format("%.2f", info.getGpa()));
            
            TextArea textArea = new TextArea(content.toString());
            textArea.setEditable(false);
            textArea.setWrapText(true);
            textArea.setPrefSize(400, 300);
            
            alert.getDialogPane().setContent(textArea);
        }
        
        alert.showAndWait();
    }
    
    @FXML
    private void goBack() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/teacher-dashboard.fxml", 
                                   "Giáo viên - " + SceneManager.getCurrentUser().getFullName());
        } catch (IOException e) {
            showAlert("Lỗi", "Không thể quay lại trang chính: " + e.getMessage());
        }
    }
    
    @FXML
    private void refreshData() {
        loadData();
        showAlert("Thành công", "Dữ liệu đã được làm mới!");
    }
    
    @FXML
    private void searchStudents() {
        filterStudents();
    }
    
    @FXML
    private void exportStudentList() {
        if (filteredStudents.isEmpty()) {
            showAlert("Thông báo", "Không có dữ liệu để xuất.");
            return;
        }
        
        StringBuilder report = new StringBuilder();
        report.append("DANH SÁCH SINH VIÊN\n");
        report.append("==================\n\n");
        
        if (currentTeacher != null) {
            report.append("Giáo viên: ").append(currentTeacher.getFullName()).append("\n");
            report.append("Khoa: ").append(currentTeacher.getDepartment()).append("\n\n");
        }
        
        report.append("MSSV\tHọ tên\t\t\tLớp\tNgành\t\tMôn học\n");
        report.append("----------------------------------------------------------------\n");
        
        for (StudentCourseInfo info : filteredStudents) {
            report.append(info.getStudentId()).append("\t");
            report.append(info.getFullName()).append("\t");
            report.append(info.getClassName()).append("\t");
            report.append(info.getMajor()).append("\t");
            report.append(info.getCourseName()).append("\n");
        }
        
        // Show report in a dialog
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Danh sách sinh viên");
        alert.setHeaderText("Danh sách sinh viên của " + (currentTeacher != null ? currentTeacher.getFullName() : "giáo viên"));
        
        TextArea textArea = new TextArea(report.toString());
        textArea.setEditable(false);
        textArea.setWrapText(true);
        textArea.setPrefSize(600, 400);
        
        alert.getDialogPane().setContent(textArea);
        alert.showAndWait();
    }
    
    @FXML
    private void sendEmailToStudents() {
        if (filteredStudents.isEmpty()) {
            showAlert("Thông báo", "Không có sinh viên nào để gửi email.");
            return;
        }
        
        StringBuilder emails = new StringBuilder();
        emails.append("Danh sách email sinh viên:\n\n");
        
        for (StudentCourseInfo info : filteredStudents) {
            emails.append(info.getEmail()).append(" (").append(info.getFullName()).append(")\n");
        }
        
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Gửi email");
        alert.setHeaderText("Danh sách email sinh viên");
        alert.setContentText("Tính năng gửi email sẽ được phát triển trong tương lai.\n\n" + emails.toString());
        alert.showAndWait();
    }
    
    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    // Inner class for student-course information
    public static class StudentCourseInfo {
        private String studentId;
        private String fullName;
        private String className;
        private String major;
        private int year;
        private String email;
        private String phone;
        private String courseId;
        private String courseName;
        private double gpa;
        
        // Getters and setters
        public String getStudentId() { return studentId; }
        public void setStudentId(String studentId) { this.studentId = studentId; }
        
        public String getFullName() { return fullName; }
        public void setFullName(String fullName) { this.fullName = fullName; }
        
        public String getClassName() { return className; }
        public void setClassName(String className) { this.className = className; }
        
        public String getMajor() { return major; }
        public void setMajor(String major) { this.major = major; }
        
        public int getYear() { return year; }
        public void setYear(int year) { this.year = year; }
        
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
        
        public String getCourseId() { return courseId; }
        public void setCourseId(String courseId) { this.courseId = courseId; }
        
        public String getCourseName() { return courseName; }
        public void setCourseName(String courseName) { this.courseName = courseName; }
        
        public double getGpa() { return gpa; }
        public void setGpa(double gpa) { this.gpa = gpa; }
    }
}
