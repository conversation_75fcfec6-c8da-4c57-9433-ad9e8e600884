package com.example.doancuoikyjava.model;

import java.time.LocalDate;

public class Admin extends User {
    private String adminId;
    private String department;
    private String accessLevel;

    public Admin() {
        super();
        this.role = UserRole.ADMIN;
    }

    public Admin(String userId, String username, String password, String fullName,
                 String email, String phone, LocalDate dateOfBirth, String address,
                 String adminId, String department, String accessLevel) {
        super(userId, username, password, fullName, email, phone, dateOfBirth, address, UserRole.ADMIN);
        this.adminId = adminId;
        this.department = department;
        this.accessLevel = accessLevel;
    }

    // Getters and Setters
    public String getAdminId() { return adminId; }
    public void setAdminId(String adminId) { this.adminId = adminId; }

    public String getDepartment() { return department; }
    public void setDepartment(String department) { this.department = department; }

    public String getAccessLevel() { return accessLevel; }
    public void setAccessLevel(String accessLevel) { this.accessLevel = accessLevel; }

    @Override
    public String toString() {
        return "Admin{" +
                "adminId='" + adminId + '\'' +
                ", fullName='" + fullName + '\'' +
                ", department='" + department + '\'' +
                ", accessLevel='" + accessLevel + '\'' +
                '}';
    }
}
