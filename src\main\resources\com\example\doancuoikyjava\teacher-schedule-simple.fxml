<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.example.doancuoikyjava.controller.TeacherScheduleController">
   <top>
      <HBox alignment="CENTER_LEFT" spacing="20.0" style="-fx-background-color: #f8f9fa; -fx-border-color: #e0e0e0; -fx-border-width: 0 0 1px 0; -fx-padding: 15px 20px;">
         <children>
            <Button fx:id="backButton" onAction="#goBack" text="← Quay lại" style="-fx-background-color: transparent; -fx-text-fill: #424242;" />
            <Label text="📅 LỊCH GIẢNG DẠY" style="-fx-font-size: 16px; -fx-font-weight: bold;" />
            <Region HBox.hgrow="ALWAYS" />
            <Label fx:id="welcomeLabel" text="Xin chào, Giáo viên" style="-fx-font-size: 14px;" />
         </children>
      </HBox>
   </top>
   
   <center>
      <ScrollPane fitToWidth="true" style="-fx-background-color: #f5f5f5;">
         <content>
            <VBox spacing="20.0" style="-fx-padding: 20px;">
               <children>
                  <!-- Header -->
                  <HBox alignment="CENTER_LEFT" spacing="20.0">
                     <children>
                        <Label text="Lịch giảng dạy" style="-fx-font-size: 24px; -fx-font-weight: bold;" />
                        <Region HBox.hgrow="ALWAYS" />
                        <Button fx:id="refreshBtn" onAction="#refreshData" text="🔄 Làm mới" style="-fx-background-color: #6c757d; -fx-text-fill: white; -fx-background-radius: 5px; -fx-padding: 8px 16px;" />
                     </children>
                  </HBox>
                  
                  <!-- Statistics Cards -->
                  <HBox spacing="20.0">
                     <children>
                        <VBox alignment="CENTER" spacing="10.0" style="-fx-background-color: #2196F3; -fx-background-radius: 12px; -fx-padding: 20px; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.3), 8, 0, 0, 4);">
                           <children>
                              <Label text="📚" style="-fx-font-size: 24px; -fx-text-fill: white;" />
                              <Label fx:id="totalCoursesLabel" text="0" style="-fx-font-size: 32px; -fx-font-weight: bold; -fx-text-fill: white;" />
                              <Label text="MÔN HỌC" style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #E3F2FD;" />
                           </children>
                        </VBox>

                        <VBox alignment="CENTER" spacing="10.0" style="-fx-background-color: #4CAF50; -fx-background-radius: 12px; -fx-padding: 20px; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.3), 8, 0, 0, 4);">
                           <children>
                              <Label text="⏰" style="-fx-font-size: 24px; -fx-text-fill: white;" />
                              <Label fx:id="totalClassesLabel" text="0" style="-fx-font-size: 32px; -fx-font-weight: bold; -fx-text-fill: white;" />
                              <Label text="TIẾT/TUẦN" style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #E8F5E8;" />
                           </children>
                        </VBox>

                        <VBox alignment="CENTER" spacing="10.0" style="-fx-background-color: #FF9800; -fx-background-radius: 12px; -fx-padding: 20px; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.3), 8, 0, 0, 4);">
                           <children>
                              <Label text="👥" style="-fx-font-size: 24px; -fx-text-fill: white;" />
                              <Label fx:id="totalStudentsLabel" text="0" style="-fx-font-size: 32px; -fx-font-weight: bold; -fx-text-fill: white;" />
                              <Label text="SINH VIÊN" style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #FFF3E0;" />
                           </children>
                        </VBox>
                     </children>
                  </HBox>
                  
                  <!-- Schedule Table -->
                  <VBox spacing="15.0" style="-fx-background-color: white; -fx-background-radius: 12px; -fx-padding: 20px; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.2), 8, 0, 0, 4);">
                     <children>
                        <Label text="Lịch tuần này" style="-fx-font-size: 18px; -fx-font-weight: bold;" />
                        <TableView fx:id="scheduleTableView" prefHeight="300.0" style="-fx-background-color: white; -fx-background-radius: 8px;">
                           <columns>
                              <TableColumn fx:id="dayColumn" prefWidth="100.0" text="Thứ" />
                              <TableColumn fx:id="timeColumn" prefWidth="120.0" text="Giờ học" />
                              <TableColumn fx:id="courseColumn" prefWidth="200.0" text="Môn học" />
                              <TableColumn fx:id="classroomColumn" prefWidth="100.0" text="Phòng" />
                              <TableColumn fx:id="studentsCountColumn" prefWidth="80.0" text="SV" />
                              <TableColumn fx:id="creditsColumn" prefWidth="80.0" text="Tín chỉ" />
                              <TableColumn fx:id="statusColumn" prefWidth="100.0" text="Trạng thái" />
                              <TableColumn fx:id="actionsColumn" prefWidth="120.0" text="Thao tác" />
                           </columns>
                        </TableView>
                     </children>
                  </VBox>
                  
                  <!-- Today and Next Week Schedule -->
                  <HBox spacing="20.0">
                     <children>
                        <VBox spacing="15.0" HBox.hgrow="ALWAYS" style="-fx-background-color: white; -fx-background-radius: 12px; -fx-padding: 20px; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.2), 8, 0, 0, 4);">
                           <children>
                              <Label text="Lịch hôm nay" style="-fx-font-size: 18px; -fx-font-weight: bold;" />
                              <ListView fx:id="todayScheduleListView" prefHeight="200.0" />
                           </children>
                        </VBox>

                        <VBox spacing="15.0" HBox.hgrow="ALWAYS" style="-fx-background-color: white; -fx-background-radius: 12px; -fx-padding: 20px; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.2), 8, 0, 0, 4);">
                           <children>
                              <Label text="Lịch tuần tới" style="-fx-font-size: 18px; -fx-font-weight: bold;" />
                              <ListView fx:id="nextWeekScheduleListView" prefHeight="200.0" />
                           </children>
                        </VBox>
                     </children>
                  </HBox>
                  
                  <!-- Notes Section -->
                  <VBox spacing="15.0" style="-fx-background-color: white; -fx-background-radius: 12px; -fx-padding: 20px; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.2), 8, 0, 0, 4);">
                     <children>
                        <Label text="Ghi chú và nhắc nhở" style="-fx-font-size: 18px; -fx-font-weight: bold;" />
                        <TextArea fx:id="notesTextArea" prefHeight="100.0" promptText="Nhập ghi chú về lịch giảng dạy..." style="-fx-background-radius: 8px; -fx-border-radius: 8px;" />
                        <HBox spacing="10.0">
                           <children>
                              <Button fx:id="saveNotesBtn" onAction="#saveNotes" text="💾 Lưu ghi chú" style="-fx-background-color: #2196F3; -fx-text-fill: white; -fx-background-radius: 5px; -fx-padding: 8px 16px;" />
                              <Button fx:id="clearNotesBtn" onAction="#clearNotes" text="🗑 Xóa ghi chú" style="-fx-background-color: #6c757d; -fx-text-fill: white; -fx-background-radius: 5px; -fx-padding: 8px 16px;" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Footer -->
                  <HBox alignment="CENTER_LEFT" spacing="20.0">
                     <children>
                        <Label fx:id="currentTimeLabel" text="Thời gian hiện tại: " style="-fx-font-size: 14px;" />
                        <Region HBox.hgrow="ALWAYS" />
                        <Button fx:id="exportBtn" onAction="#exportSchedule" text="📊 Xuất lịch dạy" style="-fx-background-color: #4CAF50; -fx-text-fill: white; -fx-background-radius: 5px; -fx-padding: 8px 16px;" />
                        <Button fx:id="printBtn" onAction="#printSchedule" text="🖨 In lịch dạy" style="-fx-background-color: #FF9800; -fx-text-fill: white; -fx-background-radius: 5px; -fx-padding: 8px 16px;" />
                     </children>
                  </HBox>
               </children>
            </VBox>
         </content>
      </ScrollPane>
   </center>
</BorderPane>
