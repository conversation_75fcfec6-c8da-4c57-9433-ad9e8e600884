package com.example.doancuoikyjava.util;

import com.example.doancuoikyjava.model.User;

/**
 * Session manager to store current user information
 */
public class SessionManager {
    
    private static User currentUser;
    
    public static void setCurrentUser(User user) {
        currentUser = user;
    }
    
    public static User getCurrentUser() {
        return currentUser;
    }
    
    public static void clearSession() {
        currentUser = null;
    }
    
    public static boolean isLoggedIn() {
        return currentUser != null;
    }
    
    public static String getCurrentUserId() {
        return currentUser != null ? currentUser.getUserId() : null;
    }
    
    public static String getCurrentUserName() {
        return currentUser != null ? currentUser.getFullName() : null;
    }
    
    public static User.UserRole getCurrentUserRole() {
        return currentUser != null ? currentUser.getRole() : null;
    }
}
