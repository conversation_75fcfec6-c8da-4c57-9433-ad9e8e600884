package com.example.doancuoikyjava.test;

import com.example.doancuoikyjava.model.Teacher;
import com.example.doancuoikyjava.model.User;
import com.example.doancuoikyjava.service.DatabaseUserService;
import com.example.doancuoikyjava.util.DatabaseConnection;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

public class TestTeacherCourses {
    
    public static void main(String[] args) {
        System.out.println("🧪 Testing Teacher Courses Functionality");
        System.out.println("==========================================");
        
        // Test database connection
        if (!DatabaseConnection.testConnection()) {
            System.err.println("❌ Không thể kết nối database!");
            return;
        }
        
        DatabaseUserService userService = new DatabaseUserService();
        
        // Test 1: Thêm giáo viên mới với môn dạy
        testAddTeacherWithCourses(userService);
        
        // Test 2: Load giáo viên và kiểm tra môn dạy
        testLoadTeacherCourses(userService);
        
        // Test 3: Cập nhật môn dạy của giáo viên
        testUpdateTeacherCourses(userService);
        
        System.out.println("\n✅ Tất cả test đã hoàn thành!");
    }
    
    private static void testAddTeacherWithCourses(DatabaseUserService userService) {
        System.out.println("\n📝 Test 1: Thêm giáo viên mới với môn dạy");
        System.out.println("------------------------------------------");
        
        try {
            Teacher teacher = new Teacher();
            
            // Generate new ID
            String teacherId = userService.generateNextUserId(User.UserRole.TEACHER);
            teacher.setUserId(teacherId);
            teacher.setTeacherId(teacherId);
            
            // Set basic info
            teacher.setUsername("test_teacher_" + System.currentTimeMillis());
            teacher.setPassword("password123");
            teacher.setFullName("Nguyễn Thị Test");
            teacher.setEmail("<EMAIL>");
            teacher.setPhone("0987654321");
            teacher.setAddress("456 Test Avenue");
            teacher.setDateOfBirth(LocalDate.of(1980, 5, 15));
            teacher.setRole(User.UserRole.TEACHER);
            
            // Set teacher specific info
            teacher.setDepartment("Khoa CNTT");
            teacher.setPosition("Giảng viên");
            teacher.setSalary(15000000.0);
            teacher.setQualification("Thạc sĩ");
            teacher.setExperienceYears(5);
            
            // Set teaching courses
            List<String> teachingCourses = Arrays.asList(
                "Lập trình Java",
                "Cơ sở dữ liệu", 
                "Thiết kế web"
            );
            teacher.setTeachingCourses(teachingCourses);
            
            System.out.println("📝 Thông tin giáo viên:");
            System.out.println("  - ID: " + teacher.getTeacherId());
            System.out.println("  - Username: " + teacher.getUsername());
            System.out.println("  - Full Name: " + teacher.getFullName());
            System.out.println("  - Teaching Courses: " + teacher.getTeachingCourses());
            
            boolean result = userService.addUser(teacher);
            
            if (result) {
                System.out.println("✅ Thêm giáo viên thành công!");
            } else {
                System.out.println("❌ Thêm giáo viên thất bại!");
            }
            
        } catch (Exception e) {
            System.err.println("❌ Lỗi test add teacher: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testLoadTeacherCourses(DatabaseUserService userService) {
        System.out.println("\n📖 Test 2: Load giáo viên và kiểm tra môn dạy");
        System.out.println("----------------------------------------------");
        
        try {
            List<User> teachers = userService.getUsersByRole(User.UserRole.TEACHER);
            
            System.out.println("📋 Danh sách giáo viên và môn dạy:");
            for (User user : teachers) {
                if (user instanceof Teacher) {
                    Teacher teacher = (Teacher) user;
                    System.out.println("  👨‍🏫 " + teacher.getFullName() + " (" + teacher.getTeacherId() + ")");
                    System.out.println("     Môn dạy: " + teacher.getTeachingCourses());
                }
            }
            
        } catch (Exception e) {
            System.err.println("❌ Lỗi test load teachers: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testUpdateTeacherCourses(DatabaseUserService userService) {
        System.out.println("\n🔄 Test 3: Cập nhật môn dạy của giáo viên");
        System.out.println("------------------------------------------");
        
        try {
            // Lấy giáo viên đầu tiên
            List<User> teachers = userService.getUsersByRole(User.UserRole.TEACHER);
            if (!teachers.isEmpty() && teachers.get(0) instanceof Teacher) {
                Teacher teacher = (Teacher) teachers.get(0);
                
                System.out.println("📝 Cập nhật môn dạy cho: " + teacher.getFullName());
                System.out.println("  Môn dạy cũ: " + teacher.getTeachingCourses());
                
                // Cập nhật môn dạy
                List<String> newCourses = Arrays.asList(
                    "Lập trình Java nâng cao",
                    "Spring Framework",
                    "Microservices"
                );
                teacher.setTeachingCourses(newCourses);
                
                boolean result = userService.updateUser(teacher);
                
                if (result) {
                    System.out.println("✅ Cập nhật thành công!");
                    
                    // Load lại để kiểm tra
                    Teacher updatedTeacher = (Teacher) userService.getUserById(teacher.getUserId());
                    System.out.println("  Môn dạy mới: " + updatedTeacher.getTeachingCourses());
                } else {
                    System.out.println("❌ Cập nhật thất bại!");
                }
            } else {
                System.out.println("⚠️ Không tìm thấy giáo viên để test update");
            }
            
        } catch (Exception e) {
            System.err.println("❌ Lỗi test update teacher: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
