package com.example.doancuoikyjava.util;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * Advanced Cache Manager with TTL support and automatic cleanup
 */
public class CacheManager {
    
    private static final CacheManager INSTANCE = new CacheManager();
    private final ConcurrentHashMap<String, CacheEntry<?>> cache = new ConcurrentHashMap<>();
    private final ScheduledExecutorService cleanupExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread t = new Thread(r, "CacheCleanup");
        t.setDaemon(true);
        return t;
    });
    
    // Cache statistics
    private volatile long hits = 0;
    private volatile long misses = 0;
    private volatile long evictions = 0;
    
    // Default TTL values (in milliseconds)
    public static final long DEFAULT_TTL = 300_000; // 5 minutes
    public static final long SHORT_TTL = 60_000;    // 1 minute
    public static final long MEDIUM_TTL = 600_000;  // 10 minutes
    public static final long LONG_TTL = 1_800_000;  // 30 minutes
    
    private CacheManager() {
        // Start cleanup task every minute
        cleanupExecutor.scheduleAtFixedRate(this::cleanup, 1, 1, TimeUnit.MINUTES);
        System.out.println("🗄️ Cache Manager initialized with automatic cleanup");
    }
    
    public static CacheManager getInstance() {
        return INSTANCE;
    }
    
    /**
     * Cache entry with TTL support
     */
    private static class CacheEntry<T> {
        private final T value;
        private final long expirationTime;
        private final long creationTime;
        private volatile long lastAccessTime;
        private volatile int accessCount;
        
        public CacheEntry(T value, long ttl) {
            this.value = value;
            this.creationTime = System.currentTimeMillis();
            this.expirationTime = creationTime + ttl;
            this.lastAccessTime = creationTime;
            this.accessCount = 0;
        }
        
        public T getValue() {
            this.lastAccessTime = System.currentTimeMillis();
            this.accessCount++;
            return value;
        }
        
        public boolean isExpired() {
            return System.currentTimeMillis() > expirationTime;
        }
        
        public long getAge() {
            return System.currentTimeMillis() - creationTime;
        }
        
        public long getTimeSinceLastAccess() {
            return System.currentTimeMillis() - lastAccessTime;
        }
        
        public int getAccessCount() {
            return accessCount;
        }
    }
    
    /**
     * Put value in cache with default TTL
     */
    public <T> void put(String key, T value) {
        put(key, value, DEFAULT_TTL);
    }
    
    /**
     * Put value in cache with custom TTL
     */
    public <T> void put(String key, T value, long ttl) {
        if (key == null || value == null) {
            return;
        }
        
        CacheEntry<T> entry = new CacheEntry<>(value, ttl);
        cache.put(key, entry);
        
        // Log cache operations for debugging
        if (cache.size() % 100 == 0) {
            System.out.println("📊 Cache size: " + cache.size() + " entries");
        }
    }
    
    /**
     * Get value from cache
     */
    @SuppressWarnings("unchecked")
    public <T> T get(String key) {
        if (key == null) {
            return null;
        }
        
        CacheEntry<?> entry = cache.get(key);
        if (entry == null) {
            misses++;
            return null;
        }
        
        if (entry.isExpired()) {
            cache.remove(key);
            evictions++;
            misses++;
            return null;
        }
        
        hits++;
        return (T) entry.getValue();
    }
    
    /**
     * Get value from cache or compute if not present
     */
    public <T> T getOrCompute(String key, Supplier<T> supplier) {
        return getOrCompute(key, supplier, DEFAULT_TTL);
    }
    
    /**
     * Get value from cache or compute if not present with custom TTL
     */
    @SuppressWarnings("unchecked")
    public <T> T getOrCompute(String key, Supplier<T> supplier, long ttl) {
        if (key == null || supplier == null) {
            return null;
        }
        
        // Try to get from cache first
        T cachedValue = get(key);
        if (cachedValue != null) {
            return cachedValue;
        }
        
        // Compute new value
        T newValue = supplier.get();
        if (newValue != null) {
            put(key, newValue, ttl);
        }
        
        return newValue;
    }
    
    /**
     * Remove value from cache
     */
    public void remove(String key) {
        if (key != null) {
            cache.remove(key);
        }
    }
    
    /**
     * Clear all cache entries
     */
    public void clear() {
        int size = cache.size();
        cache.clear();
        evictions += size;
        System.out.println("🗑️ Cache cleared: " + size + " entries removed");
    }
    
    /**
     * Clear cache entries by prefix
     */
    public void clearByPrefix(String prefix) {
        if (prefix == null) {
            return;
        }
        
        int removed = 0;
        for (String key : cache.keySet()) {
            if (key.startsWith(prefix)) {
                cache.remove(key);
                removed++;
            }
        }
        
        evictions += removed;
        if (removed > 0) {
            System.out.println("🗑️ Cache cleared by prefix '" + prefix + "': " + removed + " entries removed");
        }
    }
    
    /**
     * Check if key exists in cache and is not expired
     */
    public boolean containsKey(String key) {
        if (key == null) {
            return false;
        }
        
        CacheEntry<?> entry = cache.get(key);
        if (entry == null) {
            return false;
        }
        
        if (entry.isExpired()) {
            cache.remove(key);
            evictions++;
            return false;
        }
        
        return true;
    }
    
    /**
     * Get cache size
     */
    public int size() {
        return cache.size();
    }
    
    /**
     * Check if cache is empty
     */
    public boolean isEmpty() {
        return cache.isEmpty();
    }
    
    /**
     * Get cache statistics
     */
    public CacheStatistics getStatistics() {
        return new CacheStatistics(hits, misses, evictions, cache.size());
    }
    
    /**
     * Cache statistics class
     */
    public static class CacheStatistics {
        private final long hits;
        private final long misses;
        private final long evictions;
        private final int currentSize;
        
        public CacheStatistics(long hits, long misses, long evictions, int currentSize) {
            this.hits = hits;
            this.misses = misses;
            this.evictions = evictions;
            this.currentSize = currentSize;
        }
        
        public long getHits() { return hits; }
        public long getMisses() { return misses; }
        public long getEvictions() { return evictions; }
        public int getCurrentSize() { return currentSize; }
        
        public double getHitRatio() {
            long total = hits + misses;
            return total == 0 ? 0.0 : (double) hits / total;
        }
        
        @Override
        public String toString() {
            return String.format(
                "Cache Stats - Size: %d, Hits: %d, Misses: %d, Evictions: %d, Hit Ratio: %.2f%%",
                currentSize, hits, misses, evictions, getHitRatio() * 100
            );
        }
    }
    
    /**
     * Cleanup expired entries
     */
    private void cleanup() {
        int removed = 0;
        long currentTime = System.currentTimeMillis();
        
        for (String key : cache.keySet()) {
            CacheEntry<?> entry = cache.get(key);
            if (entry != null && entry.isExpired()) {
                cache.remove(key);
                removed++;
            }
        }
        
        evictions += removed;
        
        if (removed > 0) {
            System.out.println("🧹 Cache cleanup: " + removed + " expired entries removed");
        }
    }
    
    /**
     * Get detailed cache information
     */
    public String getDetailedInfo() {
        StringBuilder info = new StringBuilder();
        info.append("📊 Cache Detailed Information:\n");
        info.append("   • Total Entries: ").append(cache.size()).append("\n");
        info.append("   • Statistics: ").append(getStatistics()).append("\n");
        
        // Analyze cache entries
        long now = System.currentTimeMillis();
        int expiredCount = 0;
        long totalAge = 0;
        int totalAccess = 0;
        
        for (CacheEntry<?> entry : cache.values()) {
            if (entry.isExpired()) {
                expiredCount++;
            }
            totalAge += entry.getAge();
            totalAccess += entry.getAccessCount();
        }
        
        if (!cache.isEmpty()) {
            info.append("   • Expired Entries: ").append(expiredCount).append("\n");
            info.append("   • Average Age: ").append(totalAge / cache.size()).append("ms\n");
            info.append("   • Average Access Count: ").append(totalAccess / cache.size()).append("\n");
        }
        
        return info.toString();
    }
    
    /**
     * Shutdown cache manager
     */
    public void shutdown() {
        cleanupExecutor.shutdown();
        try {
            if (!cleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                cleanupExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            cleanupExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        clear();
        System.out.println("🔒 Cache Manager shutdown completed");
    }
    
    // Predefined cache keys for common operations
    public static final class Keys {
        public static final String USER_PREFIX = "user:";
        public static final String COURSE_PREFIX = "course:";
        public static final String GRADE_PREFIX = "grade:";
        public static final String SCHEDULE_PREFIX = "schedule:";
        public static final String NOTIFICATION_PREFIX = "notification:";
        public static final String STATISTICS_PREFIX = "stats:";
        
        public static String userKey(String userId) {
            return USER_PREFIX + userId;
        }
        
        public static String courseKey(String courseId) {
            return COURSE_PREFIX + courseId;
        }
        
        public static String gradeKey(String studentId, String courseId) {
            return GRADE_PREFIX + studentId + ":" + courseId;
        }
        
        public static String scheduleKey(String userId) {
            return SCHEDULE_PREFIX + userId;
        }
        
        public static String notificationKey(String userId) {
            return NOTIFICATION_PREFIX + userId;
        }
        
        public static String statisticsKey(String type) {
            return STATISTICS_PREFIX + type;
        }
    }
}
