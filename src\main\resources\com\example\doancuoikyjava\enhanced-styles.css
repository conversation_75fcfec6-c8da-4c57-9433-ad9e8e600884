/* Enhanced Modern Styles for Student Management System */

/* Root Variables */
.root {
    -fx-primary-color: #2196F3;
    -fx-secondary-color: #FFC107;
    -fx-success-color: #4CAF50;
    -fx-warning-color: #FF9800;
    -fx-error-color: #F44336;
    -fx-dark-color: #212121;
    -fx-light-color: #FAFAFA;
    -fx-border-color: #E0E0E0;
    -fx-text-color: #424242;
    -fx-background-color: #F5F5F5;
}

/* Enhanced Buttons */
.enhanced-button {
    -fx-background-radius: 8px;
    -fx-padding: 12px 24px;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 4, 0, 0, 2);
    -fx-border-width: 0;
}

.enhanced-button:hover {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 6, 0, 0, 3);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

.enhanced-button:pressed {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.4), 2, 0, 0, 1);
    -fx-scale-x: 0.98;
    -fx-scale-y: 0.98;
}

.enhanced-button.primary {
    -fx-background-color: linear-gradient(to bottom, #2196F3, #1976D2);
    -fx-text-fill: white;
}

.enhanced-button.success {
    -fx-background-color: linear-gradient(to bottom, #4CAF50, #388E3C);
    -fx-text-fill: white;
}

.enhanced-button.warning {
    -fx-background-color: linear-gradient(to bottom, #FF9800, #F57C00);
    -fx-text-fill: white;
}

.enhanced-button.error {
    -fx-background-color: linear-gradient(to bottom, #F44336, #D32F2F);
    -fx-text-fill: white;
}

/* Enhanced Cards */
.enhanced-card {
    -fx-background-color: white;
    -fx-background-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 8, 0, 0, 4);
    -fx-padding: 20px;
    -fx-spacing: 15px;
}

.enhanced-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 12, 0, 0, 6);
}

/* Enhanced Text Fields */
.enhanced-textfield {
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-border-color: #E0E0E0;
    -fx-border-width: 2px;
    -fx-padding: 12px;
    -fx-font-size: 14px;
    -fx-background-color: white;
}

.enhanced-textfield:focused {
    -fx-border-color: #2196F3;
    -fx-effect: dropshadow(gaussian, rgba(33, 150, 243, 0.3), 4, 0, 0, 0);
}

/* Enhanced ComboBox */
.enhanced-combobox {
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-border-color: #E0E0E0;
    -fx-border-width: 2px;
    -fx-padding: 8px;
    -fx-font-size: 14px;
    -fx-background-color: white;
}

.enhanced-combobox:focused {
    -fx-border-color: #2196F3;
}

/* Enhanced Table */
.enhanced-table {
    -fx-background-color: white;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-border-color: #E0E0E0;
    -fx-border-width: 1px;
}

.enhanced-table .column-header {
    -fx-background-color: linear-gradient(to bottom, #F5F5F5, #EEEEEE);
    -fx-font-weight: bold;
    -fx-text-fill: #424242;
    -fx-padding: 12px;
    -fx-border-color: #E0E0E0;
}

.enhanced-table .table-row-cell {
    -fx-padding: 8px;
    -fx-border-color: transparent;
}

.enhanced-table .table-row-cell:even {
    -fx-background-color: #FAFAFA;
}

.enhanced-table .table-row-cell:odd {
    -fx-background-color: white;
}

.enhanced-table .table-row-cell:hover {
    -fx-background-color: #E3F2FD;
}

.enhanced-table .table-row-cell:selected {
    -fx-background-color: #BBDEFB;
}

/* Statistics Cards */
.stat-card {
    -fx-background-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 8, 0, 0, 4);
    -fx-padding: 20px;
    -fx-alignment: center;
    -fx-spacing: 10px;
}

.stat-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 12, 0, 0, 6);
}

/* Navigation Bar */
.nav-bar {
    -fx-background-color: linear-gradient(to bottom, white, #F8F9FA);
    -fx-border-color: #E0E0E0;
    -fx-border-width: 0 0 1px 0;
    -fx-padding: 15px 20px;
    -fx-spacing: 20px;
    -fx-alignment: center-left;
}

.nav-button {
    -fx-background-color: transparent;
    -fx-text-fill: #424242;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-padding: 8px 16px;
    -fx-background-radius: 6px;
    -fx-cursor: hand;
}

.nav-button:hover {
    -fx-background-color: #E3F2FD;
    -fx-text-fill: #1976D2;
}

/* Sidebar */
.sidebar {
    -fx-background-color: linear-gradient(to bottom, #263238, #37474F);
    -fx-padding: 20px;
    -fx-spacing: 10px;
    -fx-min-width: 250px;
}

.sidebar-button {
    -fx-background-color: transparent;
    -fx-text-fill: #B0BEC5;
    -fx-font-size: 14px;
    -fx-padding: 12px 16px;
    -fx-background-radius: 8px;
    -fx-cursor: hand;
    -fx-alignment: center-left;
    -fx-min-width: 200px;
}

.sidebar-button:hover {
    -fx-background-color: rgba(255, 255, 255, 0.1);
    -fx-text-fill: white;
}

.sidebar-button.active {
    -fx-background-color: #2196F3;
    -fx-text-fill: white;
    -fx-effect: dropshadow(gaussian, rgba(33, 150, 243, 0.4), 4, 0, 0, 2);
}

/* Content Container */
.content-container {
    -fx-background-color: #F5F5F5;
    -fx-padding: 20px;
}

.dashboard-container {
    -fx-spacing: 20px;
    -fx-padding: 0;
}

/* Labels and Typography */
.label-title {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
    -fx-text-fill: #212121;
}

.label-subtitle {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #424242;
}

.card-header {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #212121;
}

.stats-number {
    -fx-font-size: 36px;
    -fx-font-weight: bold;
    -fx-text-fill: white;
}

.stats-label {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: rgba(255, 255, 255, 0.8);
}

/* Enhanced Dialog */
.enhanced-dialog {
    -fx-background-color: white;
    -fx-background-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 16, 0, 0, 8);
}

.enhanced-dialog .header-panel {
    -fx-background-color: #2196F3;
    -fx-text-fill: white;
    -fx-padding: 20px;
    -fx-background-radius: 12px 12px 0 0;
}

/* Notification Toast */
.notification-toast {
    -fx-background-radius: 25px;
    -fx-padding: 12px 20px;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: white;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 6, 0, 0, 3);
}

.notification-toast.success {
    -fx-background-color: #4CAF50;
}

.notification-toast.warning {
    -fx-background-color: #FF9800;
}

.notification-toast.error {
    -fx-background-color: #F44336;
}

.notification-toast.info {
    -fx-background-color: #2196F3;
}

/* Enhanced Progress Bar */
.enhanced-progress {
    -fx-accent: #2196F3;
    -fx-background-radius: 10px;
    -fx-background-insets: 0;
}

.enhanced-progress .bar {
    -fx-background-radius: 10px;
    -fx-background-insets: 0;
    -fx-background-color: linear-gradient(to right, #2196F3, #21CBF3);
}

/* Loading Indicator */
.loading-indicator {
    -fx-accent: #2196F3;
    -fx-background-color: transparent;
}

/* Form Styling */
.form-container {
    -fx-spacing: 15px;
    -fx-padding: 20px;
    -fx-background-color: white;
    -fx-background-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 8, 0, 0, 4);
}

.form-row {
    -fx-spacing: 15px;
    -fx-alignment: center-left;
}

.form-label {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #424242;
    -fx-min-width: 120px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        -fx-min-width: 200px;
    }
    
    .nav-bar {
        -fx-padding: 10px 15px;
    }
    
    .content-container {
        -fx-padding: 15px;
    }
}

/* Animation Classes */
.fade-in {
    -fx-opacity: 0;
}

.slide-in-left {
    -fx-translate-x: -100;
}

.slide-in-right {
    -fx-translate-x: 100;
}

.slide-in-up {
    -fx-translate-y: 50;
}

/* Utility Classes */
.full-width {
    -fx-max-width: infinity;
}

.text-center {
    -fx-alignment: center;
}

.text-left {
    -fx-alignment: center-left;
}

.text-right {
    -fx-alignment: center-right;
}

.margin-top {
    -fx-padding: 20px 0 0 0;
}

.margin-bottom {
    -fx-padding: 0 0 20px 0;
}

.no-padding {
    -fx-padding: 0;
}

.no-margin {
    -fx-spacing: 0;
}
