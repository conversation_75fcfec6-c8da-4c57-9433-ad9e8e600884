/* Blue Ocean Theme Styles */

.root {
    /* Blue Ocean Theme Variables */
    -fx-primary: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    -fx-secondary: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    -fx-success: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    -fx-warning: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    -fx-error: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    -fx-background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    -fx-surface: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    -fx-text: #0d47a1;
    -fx-text-secondary: #1976d2;
    -fx-border: #2196f3;
    
    /* Apply ocean background */
    -fx-background-color: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* Main Container Backgrounds - Ocean Blue */
.main-container {
    -fx-background-color: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    -fx-background-radius: 15px;
    -fx-effect: dropshadow(gaussian, rgba(79,172,254,0.5), 20, 0, 0, 8);
}

.content-area {
    -fx-background-color: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    -fx-background-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(79,172,254,0.4), 12, 0, 0, 4);
}

/* Sidebar Ocean Theme */
.sidebar {
    -fx-background-color: linear-gradient(180deg, #4facfe 0%, #00f2fe 100%);
    -fx-border-color: transparent;
    -fx-border-width: 0;
    -fx-background-radius: 0 15px 15px 0;
    -fx-effect: dropshadow(gaussian, rgba(79,172,254,0.7), 15, 0, 3, 0);
}

.sidebar-button {
    -fx-background-color: transparent;
    -fx-text-fill: #FFFFFF;
    -fx-font-weight: bold;
    -fx-background-radius: 25px;
    -fx-border-radius: 25px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 5, 0, 0, 2);
}

.sidebar-button:hover {
    -fx-background-color: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
    -fx-effect: dropshadow(gaussian, rgba(67,233,123,0.8), 12, 0, 0, 5);
}

.sidebar-button.active {
    -fx-background-color: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    -fx-text-fill: #0d47a1;
    -fx-font-weight: bold;
    -fx-effect: dropshadow(gaussian, rgba(168,237,234,0.9), 15, 0, 0, 6);
}

/* Card Ocean Theme */
.enhanced-card {
    -fx-background-color: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    -fx-border-color: transparent;
    -fx-border-width: 0;
    -fx-background-radius: 20px;
    -fx-effect: dropshadow(gaussian, rgba(79,172,254,0.3), 12, 0, 0, 5);
}

.enhanced-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(79,172,254,0.5), 18, 0, 0, 8);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

.stat-card {
    -fx-background-color: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    -fx-background-radius: 20px;
    -fx-effect: dropshadow(gaussian, rgba(79,172,254,0.6), 15, 0, 0, 6);
}

.stat-card:hover {
    -fx-background-color: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    -fx-effect: dropshadow(gaussian, rgba(67,233,123,0.8), 20, 0, 0, 8);
}

/* Button Ocean Theme */
.button {
    -fx-background-color: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    -fx-border-color: transparent;
    -fx-text-fill: #0d47a1;
    -fx-background-radius: 25px;
    -fx-border-radius: 25px;
    -fx-font-weight: bold;
    -fx-effect: dropshadow(gaussian, rgba(79,172,254,0.3), 8, 0, 0, 3);
}

.button:hover {
    -fx-background-color: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    -fx-text-fill: #FFFFFF;
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
    -fx-effect: dropshadow(gaussian, rgba(79,172,254,0.6), 12, 0, 0, 5);
}

.button.btn-primary {
    -fx-background-color: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    -fx-text-fill: #FFFFFF;
    -fx-border-color: transparent;
    -fx-effect: dropshadow(gaussian, rgba(79,172,254,0.5), 10, 0, 0, 4);
}

.button.btn-primary:hover {
    -fx-background-color: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    -fx-effect: dropshadow(gaussian, rgba(67,233,123,0.7), 15, 0, 0, 6);
}

.button.btn-success {
    -fx-background-color: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    -fx-text-fill: #FFFFFF;
    -fx-border-color: transparent;
    -fx-effect: dropshadow(gaussian, rgba(67,233,123,0.5), 10, 0, 0, 4);
}

.button.btn-success:hover {
    -fx-background-color: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    -fx-text-fill: #0d47a1;
    -fx-effect: dropshadow(gaussian, rgba(168,237,234,0.7), 15, 0, 0, 6);
}

/* Text and Labels */
.label {
    -fx-text-fill: #0d47a1;
}

.label.label-title {
    -fx-text-fill: #0d47a1;
    -fx-font-weight: bold;
}

.label.label-subtitle {
    -fx-text-fill: #1976d2;
}

/* Header Ocean Theme */
.header-container {
    -fx-background-color: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    -fx-border-color: transparent;
    -fx-border-width: 0;
    -fx-background-radius: 15px 15px 0 0;
    -fx-effect: dropshadow(gaussian, rgba(79,172,254,0.4), 8, 0, 0, 3);
}

.header-title {
    -fx-text-fill: #FFFFFF;
    -fx-font-weight: bold;
}

.header-subtitle {
    -fx-text-fill: #e3f2fd;
}

/* Text Fields Ocean Theme */
.enhanced-textfield {
    -fx-background-color: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    -fx-border-color: #2196f3;
    -fx-text-fill: #0d47a1;
    -fx-prompt-text-fill: #1976d2;
    -fx-background-radius: 15px;
    -fx-border-radius: 15px;
}

.enhanced-textfield:focused {
    -fx-border-color: #4facfe;
    -fx-effect: dropshadow(gaussian, rgba(79,172,254,0.5), 8, 0, 0, 3);
}

/* Table Ocean Theme */
.enhanced-table {
    -fx-background-color: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    -fx-border-color: #2196f3;
    -fx-background-radius: 15px;
    -fx-border-radius: 15px;
}

.enhanced-table .column-header {
    -fx-background-color: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    -fx-text-fill: #FFFFFF;
    -fx-border-color: #2196f3;
    -fx-font-weight: bold;
}

.enhanced-table .table-row-cell {
    -fx-background-color: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    -fx-text-fill: #0d47a1;
}

.enhanced-table .table-row-cell:hover {
    -fx-background-color: rgba(79,172,254,0.2);
}

.enhanced-table .table-row-cell:selected {
    -fx-background-color: rgba(79,172,254,0.4);
}

/* Progress Bar Ocean Theme */
.progress-bar {
    -fx-background-color: #bbdefb;
    -fx-background-radius: 15px;
}

.progress-bar .bar {
    -fx-background-color: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    -fx-background-radius: 15px;
}

/* Scrollbar Ocean Theme */
.scroll-bar .track {
    -fx-background-color: rgba(79,172,254,0.2);
    -fx-background-radius: 10px;
}

.scroll-bar .thumb {
    -fx-background-color: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    -fx-background-radius: 10px;
}

.scroll-bar .thumb:hover {
    -fx-background-color: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}
