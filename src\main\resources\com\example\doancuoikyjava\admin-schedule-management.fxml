<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.example.doancuoikyjava.controller.AdminScheduleController">
   <top>
      <HBox alignment="CENTER_LEFT" spacing="20.0" style="-fx-background-color: #f8f9fa; -fx-border-color: #e0e0e0; -fx-border-width: 0 0 1px 0; -fx-padding: 15px 20px;">
         <children>
            <Button fx:id="backButton" onAction="#goBack" text="← Quay lại" style="-fx-background-color: transparent; -fx-text-fill: #424242;" />
            <Label text="🕒 QUẢN LÝ LỊCH HỌC" style="-fx-font-size: 16px; -fx-font-weight: bold;" />
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="addScheduleBtn" onAction="#showAddScheduleDialog" text="➕ Thêm lịch học" style="-fx-background-color: #2196F3; -fx-text-fill: white; -fx-background-radius: 5px; -fx-padding: 8px 16px;" />
         </children>
      </HBox>
   </top>
   
   <center>
      <ScrollPane fitToWidth="true" style="-fx-background-color: #f5f5f5;">
         <content>
            <VBox spacing="20.0" style="-fx-padding: 20px;">
               <children>
                  <!-- Filter Section -->
                  <VBox spacing="15.0" style="-fx-background-color: white; -fx-background-radius: 12px; -fx-padding: 20px; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.2), 8, 0, 0, 4);">
                     <children>
                        <Label text="Bộ lọc" style="-fx-font-size: 18px; -fx-font-weight: bold;" />
                        <HBox spacing="15.0" alignment="CENTER_LEFT">
                           <children>
                              <Label text="Môn học:" style="-fx-min-width: 80px;" />
                              <ComboBox fx:id="courseFilterCombo" prefWidth="200.0" promptText="Chọn môn học" />
                              
                              <Label text="Giảng viên:" style="-fx-min-width: 80px;" />
                              <ComboBox fx:id="teacherFilterCombo" prefWidth="200.0" promptText="Chọn giảng viên" />
                              
                              <Label text="Thứ:" style="-fx-min-width: 50px;" />
                              <ComboBox fx:id="dayFilterCombo" prefWidth="120.0" promptText="Chọn thứ" />
                              
                              <Button fx:id="filterBtn" onAction="#applyFilter" text="🔍 Lọc" style="-fx-background-color: #4CAF50; -fx-text-fill: white; -fx-background-radius: 5px; -fx-padding: 8px 16px;" />
                              <Button fx:id="clearFilterBtn" onAction="#clearFilter" text="🗑 Xóa lọc" style="-fx-background-color: #6c757d; -fx-text-fill: white; -fx-background-radius: 5px; -fx-padding: 8px 16px;" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Schedule Table -->
                  <VBox spacing="15.0" style="-fx-background-color: white; -fx-background-radius: 12px; -fx-padding: 20px; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.2), 8, 0, 0, 4);">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="20.0">
                           <children>
                              <Label text="Danh sách lịch học" style="-fx-font-size: 18px; -fx-font-weight: bold;" />
                              <Region HBox.hgrow="ALWAYS" />
                              <Button fx:id="refreshBtn" onAction="#refreshData" text="🔄 Làm mới" style="-fx-background-color: #6c757d; -fx-text-fill: white; -fx-background-radius: 5px; -fx-padding: 8px 16px;" />
                           </children>
                        </HBox>
                        
                        <TableView fx:id="scheduleTableView" prefHeight="400.0" style="-fx-background-color: white; -fx-background-radius: 8px;">
                           <columns>
                              <TableColumn fx:id="courseNameColumn" prefWidth="200.0" text="Môn học" />
                              <TableColumn fx:id="teacherNameColumn" prefWidth="150.0" text="Giảng viên" />
                              <TableColumn fx:id="dayColumn" prefWidth="80.0" text="Thứ" />
                              <TableColumn fx:id="timeColumn" prefWidth="120.0" text="Giờ học" />
                              <TableColumn fx:id="classroomColumn" prefWidth="100.0" text="Phòng học" />
                              <TableColumn fx:id="weekTypeColumn" prefWidth="100.0" text="Loại tuần" />
                              <TableColumn fx:id="semesterColumn" prefWidth="100.0" text="Học kỳ" />
                              <TableColumn fx:id="statusColumn" prefWidth="100.0" text="Trạng thái" />
                              <TableColumn fx:id="actionsColumn" prefWidth="150.0" text="Thao tác" />
                           </columns>
                        </TableView>
                     </children>
                  </VBox>
                  
                  <!-- Weekly Schedule View -->
                  <VBox spacing="15.0" style="-fx-background-color: white; -fx-background-radius: 12px; -fx-padding: 20px; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.2), 8, 0, 0, 4);">
                     <children>
                        <Label text="Lịch học theo tuần" style="-fx-font-size: 18px; -fx-font-weight: bold;" />
                        
                        <GridPane fx:id="weeklyScheduleGrid" hgap="10.0" vgap="10.0">
                           <columnConstraints>
                              <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                              <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                              <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                              <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                              <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                              <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                              <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                           </columnConstraints>
                           <rowConstraints>
                              <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                           </rowConstraints>
                           
                           <!-- Headers -->
                           <children>
                              <Label text="Thứ 2" style="-fx-font-weight: bold; -fx-alignment: center;" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                              <Label text="Thứ 3" style="-fx-font-weight: bold; -fx-alignment: center;" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                              <Label text="Thứ 4" style="-fx-font-weight: bold; -fx-alignment: center;" GridPane.columnIndex="2" GridPane.rowIndex="0" />
                              <Label text="Thứ 5" style="-fx-font-weight: bold; -fx-alignment: center;" GridPane.columnIndex="3" GridPane.rowIndex="0" />
                              <Label text="Thứ 6" style="-fx-font-weight: bold; -fx-alignment: center;" GridPane.columnIndex="4" GridPane.rowIndex="0" />
                              <Label text="Thứ 7" style="-fx-font-weight: bold; -fx-alignment: center;" GridPane.columnIndex="5" GridPane.rowIndex="0" />
                              <Label text="CN" style="-fx-font-weight: bold; -fx-alignment: center;" GridPane.columnIndex="6" GridPane.rowIndex="0" />
                           </children>
                        </GridPane>
                     </children>
                  </VBox>
                  
                  <!-- Statistics -->
                  <HBox spacing="20.0">
                     <children>
                        <VBox alignment="CENTER" spacing="10.0" style="-fx-background-color: #2196F3; -fx-background-radius: 12px; -fx-padding: 20px; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.3), 8, 0, 0, 4);">
                           <children>
                              <Label text="📅" style="-fx-font-size: 24px; -fx-text-fill: white;" />
                              <Label fx:id="totalSchedulesLabel" text="0" style="-fx-font-size: 32px; -fx-font-weight: bold; -fx-text-fill: white;" />
                              <Label text="TỔNG LỊCH HỌC" style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #E3F2FD;" />
                           </children>
                        </VBox>
                        
                        <VBox alignment="CENTER" spacing="10.0" style="-fx-background-color: #4CAF50; -fx-background-radius: 12px; -fx-padding: 20px; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.3), 8, 0, 0, 4);">
                           <children>
                              <Label text="✅" style="-fx-font-size: 24px; -fx-text-fill: white;" />
                              <Label fx:id="activeSchedulesLabel" text="0" style="-fx-font-size: 32px; -fx-font-weight: bold; -fx-text-fill: white;" />
                              <Label text="ĐANG HOẠT ĐỘNG" style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #E8F5E8;" />
                           </children>
                        </VBox>
                        
                        <VBox alignment="CENTER" spacing="10.0" style="-fx-background-color: #FF9800; -fx-background-radius: 12px; -fx-padding: 20px; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.3), 8, 0, 0, 4);">
                           <children>
                              <Label text="⚠️" style="-fx-font-size: 24px; -fx-text-fill: white;" />
                              <Label fx:id="conflictsLabel" text="0" style="-fx-font-size: 32px; -fx-font-weight: bold; -fx-text-fill: white;" />
                              <Label text="XUNG ĐỘT" style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #FFF3E0;" />
                           </children>
                        </VBox>
                     </children>
                  </HBox>
               </children>
            </VBox>
         </content>
      </ScrollPane>
   </center>
</BorderPane>
