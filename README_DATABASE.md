# 🗃️ HƯỚNG DẪN SỬ DỤNG DATABASE CHO HỆ THỐNG QUẢN LÝ SINH VIÊN

## 📋 TỔNG QUAN

Hệ thống hỗ trợ **2 phương thức lưu trữ dữ liệu**:
1. **SQL Server Database** (khuyến nghị)
2. **File-based Storage** (fallback)

Ứng dụng sẽ tự động kiểm tra kết nối SQL Server. Nếu không kết nối được, sẽ chuyển sang file-based storage.

## 🔧 CÀI ĐẶT SQL SERVER

### Bước 1: Tải và cài đặt SQL Server
```bash
# Tải SQL Server Express (miễn phí)
https://www.microsoft.com/en-us/sql-server/sql-server-downloads

# Tải SQL Server Management Studio (SSMS)
https://docs.microsoft.com/en-us/sql/ssms/download-sql-server-management-studio-ssms
```

### Bước 2: Cấu hình SQL Server
1. **Mở SQL Server Configuration Manager**
2. **Enable TCP/IP** cho SQL Server
3. **Restart SQL Server Service**
4. **Tạo user login** (hoặc sử dụng sa account)

### Bước 3: Tạo Database
**Cách 1: Sử dụng script SQL**
```sql
-- Chạy file create_database.sql trong SSMS
-- File này sẽ tạo database và dữ liệu mẫu
```

**Cách 2: Để ứng dụng tự tạo**
```sql
-- Chỉ cần tạo database trống
CREATE DATABASE StudentManagementDB;
```

### Bước 4: Cấu hình kết nối
Sửa file `DatabaseConnection.java`:
```java
private static final String SERVER = "localhost";         // Tên server
private static final String DATABASE = "StudentManagementDB"; // Tên database
private static final String USERNAME = "sa";              // Username
private static final String PASSWORD = "your_password";   // Password
```

## 🏗️ CẤU TRÚC DATABASE

### Bảng Users (Thông tin người dùng chính)
```sql
- user_id: NVARCHAR(50) PRIMARY KEY
- username: NVARCHAR(50) UNIQUE
- password: NVARCHAR(255)
- full_name: NVARCHAR(255)
- email: NVARCHAR(255)
- phone: NVARCHAR(20)
- date_of_birth: DATE
- address: NVARCHAR(500)
- role: NVARCHAR(20) -- ADMIN, TEACHER, STUDENT
- created_date: DATETIME
```

### Bảng Students (Chi tiết sinh viên)
```sql
- student_id: NVARCHAR(50) PRIMARY KEY
- user_id: NVARCHAR(50) FOREIGN KEY
- class_name: NVARCHAR(50)
- major: NVARCHAR(100)
- year_of_study: INT
- gpa: DECIMAL(3,2)
```

### Bảng Teachers (Chi tiết giáo viên)
```sql
- teacher_id: NVARCHAR(50) PRIMARY KEY
- user_id: NVARCHAR(50) FOREIGN KEY
- department: NVARCHAR(100)
- position: NVARCHAR(50)
- salary: DECIMAL(15,2)
- qualification: NVARCHAR(100)
- experience_years: INT
```

### Bảng Courses (Môn học)
```sql
- course_id: NVARCHAR(50) PRIMARY KEY
- course_name: NVARCHAR(255)
- description: NTEXT
- credits: INT
- teacher_id: NVARCHAR(50) FOREIGN KEY
- teacher_name: NVARCHAR(255)
- schedule: NVARCHAR(255)
- classroom: NVARCHAR(50)
- max_students: INT
```

### Bảng Course_Enrollments (Đăng ký môn học)
```sql
- enrollment_id: INT IDENTITY PRIMARY KEY
- course_id: NVARCHAR(50) FOREIGN KEY
- student_id: NVARCHAR(50) FOREIGN KEY
- enrollment_date: DATETIME
```

### Bảng Grades (Điểm số)
```sql
- grade_id: NVARCHAR(50) PRIMARY KEY
- student_id: NVARCHAR(50) FOREIGN KEY
- course_id: NVARCHAR(50) FOREIGN KEY
- course_name: NVARCHAR(255)
- score: DECIMAL(5,2)
- credits: INT
- letter_grade: NVARCHAR(2)
- grade_point: DECIMAL(3,2)
- semester: NVARCHAR(20)
- teacher_id: NVARCHAR(50) FOREIGN KEY
- date_recorded: DATETIME
```

## 🚀 CHẠY ỨNG DỤNG

### Với SQL Server:
```bash
# 1. Đảm bảo SQL Server đang chạy
# 2. Cấu hình kết nối trong DatabaseConnection.java
# 3. Chạy ứng dụng
mvnw clean javafx:run

# Console sẽ hiển thị:
# 🔗 Sử dụng SQL Server Database
# ✅ Kết nối SQL Server thành công!
# ✅ Database và tables đã được tạo thành công!
```

### Không có SQL Server:
```bash
# Ứng dụng tự động fallback
mvnw clean javafx:run

# Console sẽ hiển thị:
# 📁 Sử dụng File-based Storage
```

## 🔐 TÀI KHOẢN MẶC ĐỊNH

**Admin:**
- Username: `admin`
- Password: `admin123`

**Teacher:**
- Username: `teacher`
- Password: `teacher123`

**Students:**
- Username: `student`, `student2`, `student3`, `student4`, `student5`
- Password: `student123`

## 🎯 TÍNH NĂNG DATABASE

### ✅ Ưu điểm SQL Server:
- **Hiệu suất cao** với large dataset
- **ACID transactions** đảm bảo tính toàn vẹn
- **Concurrent access** nhiều user cùng lúc
- **Backup & Recovery** dễ dàng
- **Scalability** mở rộng tốt
- **Security** bảo mật cao

### ✅ Ưu điểm File-based:
- **Không cần cài đặt** database server
- **Portable** dễ di chuyển
- **Simple setup** cài đặt đơn giản
- **No dependencies** không phụ thuộc

## 🔧 TROUBLESHOOTING

### Lỗi kết nối SQL Server:
```
❌ Lỗi kết nối SQL Server: Login failed for user 'sa'
```
**Giải pháp:**
1. Kiểm tra username/password
2. Enable SQL Server Authentication
3. Kiểm tra SQL Server service đang chạy
4. Kiểm tra firewall port 1433

### Lỗi tạo database:
```
❌ Lỗi khi tạo database: Invalid object name
```
**Giải pháp:**
1. Tạo database manually trong SSMS
2. Kiểm tra quyền user
3. Chạy script create_database.sql

### Fallback to file storage:
```
📁 Sử dụng File-based Storage
```
**Nguyên nhân:**
- SQL Server không chạy
- Cấu hình kết nối sai
- Database không tồn tại

## 📊 MONITORING

### Kiểm tra kết nối:
```java
if (DatabaseConnection.testConnection()) {
    System.out.println("✅ Database connected");
} else {
    System.out.println("❌ Database disconnected");
}
```

### Xem logs:
- Console output hiển thị trạng thái kết nối
- SQL errors được log chi tiết
- Transaction status được theo dõi

## 🔄 MIGRATION

### Từ File sang Database:
1. Cài đặt SQL Server
2. Chạy ứng dụng (tự động tạo tables)
3. Import dữ liệu từ files (nếu cần)

### Từ Database sang File:
1. Export dữ liệu từ database
2. Tắt SQL Server
3. Chạy ứng dụng (tự động fallback)

## 🎉 KẾT LUẬN

Hệ thống đã được thiết kế **linh hoạt** với khả năng:
- **Dual storage support** (SQL Server + File)
- **Automatic fallback** khi không có database
- **Easy configuration** cấu hình đơn giản
- **Production ready** sẵn sàng triển khai

Chọn SQL Server cho **production environment** và file-based cho **development/testing**!
