package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.*;
import com.example.doancuoikyjava.service.*;
import com.example.doancuoikyjava.util.SceneManager;

import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.VBox;
import javafx.geometry.Insets;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.stage.Modality;
import javafx.stage.Stage;

import java.io.IOException;
import java.net.URL;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;
import java.util.stream.Collectors;

/**
 * Controller for admin schedule management
 */
public class AdminScheduleController implements Initializable {
    
    @FXML private ComboBox<String> courseFilterCombo;
    @FXML private ComboBox<String> teacherFilterCombo;
    @FXML private ComboBox<String> dayFilterCombo;
    @FXML private TableView<CourseSchedule> scheduleTableView;
    @FXML private TableColumn<CourseSchedule, String> courseNameColumn;
    @FXML private TableColumn<CourseSchedule, String> teacherNameColumn;
    @FXML private TableColumn<CourseSchedule, String> dayColumn;
    @FXML private TableColumn<CourseSchedule, String> timeColumn;
    @FXML private TableColumn<CourseSchedule, String> classroomColumn;
    @FXML private TableColumn<CourseSchedule, String> weekTypeColumn;
    @FXML private TableColumn<CourseSchedule, String> semesterColumn;
    @FXML private TableColumn<CourseSchedule, String> statusColumn;
    @FXML private TableColumn<CourseSchedule, Void> actionsColumn;
    @FXML private GridPane weeklyScheduleGrid;
    @FXML private Label totalSchedulesLabel;
    @FXML private Label activeSchedulesLabel;
    @FXML private Label conflictsLabel;
    
    private ScheduleService scheduleService;
    private CourseService courseService;
    private DatabaseUserService userService;
    private ObservableList<CourseSchedule> scheduleData;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        System.out.println("🔧 AdminScheduleController: Initializing...");
        
        try {
            // Initialize services
            scheduleService = new ScheduleService();
            courseService = new CourseService();
            userService = new DatabaseUserService();
            scheduleData = FXCollections.observableArrayList();
            
            setupTableColumns();
            setupFilterComboBoxes();
            loadData();
            
            System.out.println("✅ AdminScheduleController: Initialization completed");
            
        } catch (Exception e) {
            System.err.println("❌ AdminScheduleController: Initialization failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void setupTableColumns() {
        courseNameColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(cellData.getValue().getCourseName()));
        
        teacherNameColumn.setCellValueFactory(cellData -> {
            // Get teacher name from course
            String courseId = cellData.getValue().getCourseId();
            Course course = courseService.getCourseById(courseId);
            return new SimpleStringProperty(course != null ? course.getTeacherName() : "N/A");
        });
        
        dayColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(cellData.getValue().getDayOfWeekText()));
        
        timeColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(cellData.getValue().getTimeRange()));
        
        classroomColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(cellData.getValue().getClassroom()));
        
        weekTypeColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(cellData.getValue().getWeekTypeText()));
        
        semesterColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(cellData.getValue().getSemester()));
        
        statusColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(cellData.getValue().isActive() ? "Hoạt động" : "Tạm dừng"));
        
        // Setup actions column
        actionsColumn.setCellFactory(param -> new TableCell<CourseSchedule, Void>() {
            private final Button editBtn = new Button("✏️ Sửa");
            private final Button deleteBtn = new Button("🗑 Xóa");
            
            {
                editBtn.setStyle("-fx-background-color: #2196F3; -fx-text-fill: white; -fx-background-radius: 3px; -fx-padding: 4px 8px;");
                deleteBtn.setStyle("-fx-background-color: #F44336; -fx-text-fill: white; -fx-background-radius: 3px; -fx-padding: 4px 8px;");
                
                editBtn.setOnAction(e -> {
                    CourseSchedule schedule = getTableView().getItems().get(getIndex());
                    editSchedule(schedule);
                });
                
                deleteBtn.setOnAction(e -> {
                    CourseSchedule schedule = getTableView().getItems().get(getIndex());
                    deleteSchedule(schedule);
                });
            }
            
            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    javafx.scene.layout.HBox buttons = new javafx.scene.layout.HBox(5);
                    buttons.getChildren().addAll(editBtn, deleteBtn);
                    setGraphic(buttons);
                }
            }
        });
        
        scheduleTableView.setItems(scheduleData);
    }
    
    private void setupFilterComboBoxes() {
        // Setup day filter
        dayFilterCombo.setItems(FXCollections.observableArrayList(
            "Tất cả", "Thứ 2", "Thứ 3", "Thứ 4", "Thứ 5", "Thứ 6", "Thứ 7", "Chủ nhật"
        ));
        dayFilterCombo.setValue("Tất cả");
        
        // Load courses and teachers for filters
        loadFilterData();
    }
    
    private void loadFilterData() {
        try {
            // Load courses
            List<Course> courses = courseService.getAllCourses();
            ObservableList<String> courseNames = FXCollections.observableArrayList("Tất cả");
            courseNames.addAll(courses.stream()
                .map(Course::getCourseName)
                .distinct()
                .sorted()
                .collect(Collectors.toList()));
            courseFilterCombo.setItems(courseNames);
            courseFilterCombo.setValue("Tất cả");
            
            // Load teachers
            List<User> teachers = userService.getUsersByRole(User.UserRole.TEACHER);
            ObservableList<String> teacherNames = FXCollections.observableArrayList("Tất cả");
            teacherNames.addAll(teachers.stream()
                .map(User::getFullName)
                .distinct()
                .sorted()
                .collect(Collectors.toList()));
            teacherFilterCombo.setItems(teacherNames);
            teacherFilterCombo.setValue("Tất cả");
            
        } catch (Exception e) {
            System.err.println("❌ Error loading filter data: " + e.getMessage());
        }
    }
    
    private void loadData() {
        try {
            System.out.println("📅 Loading schedule data...");
            
            // Load all schedules
            List<Course> allCourses = courseService.getAllCourses();
            scheduleData.clear();
            
            for (Course course : allCourses) {
                List<CourseSchedule> courseSchedules = scheduleService.getSchedulesByCourse(course.getCourseId());
                for (CourseSchedule schedule : courseSchedules) {
                    schedule.setCourseName(course.getCourseName());
                    scheduleData.add(schedule);
                }
            }
            
            System.out.println("📊 Loaded " + scheduleData.size() + " schedule entries");
            
            updateStatistics();
            loadWeeklyScheduleView();
            
        } catch (Exception e) {
            System.err.println("❌ Error loading schedule data: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void updateStatistics() {
        int totalSchedules = scheduleData.size();
        int activeSchedules = (int) scheduleData.stream().filter(CourseSchedule::isActive).count();
        int conflicts = detectScheduleConflicts();
        
        totalSchedulesLabel.setText(String.valueOf(totalSchedules));
        activeSchedulesLabel.setText(String.valueOf(activeSchedules));
        conflictsLabel.setText(String.valueOf(conflicts));
    }
    
    private int detectScheduleConflicts() {
        int conflicts = 0;
        List<CourseSchedule> activeSchedules = scheduleData.stream()
            .filter(CourseSchedule::isActive)
            .collect(Collectors.toList());
        
        for (int i = 0; i < activeSchedules.size(); i++) {
            for (int j = i + 1; j < activeSchedules.size(); j++) {
                CourseSchedule schedule1 = activeSchedules.get(i);
                CourseSchedule schedule2 = activeSchedules.get(j);
                
                if (schedule1.isConflictWith(schedule2) && 
                    schedule1.getClassroom().equals(schedule2.getClassroom())) {
                    conflicts++;
                }
            }
        }
        
        return conflicts;
    }
    
    private void loadWeeklyScheduleView() {
        // Clear existing content (keep headers)
        weeklyScheduleGrid.getChildren().removeIf(node -> GridPane.getRowIndex(node) != null && GridPane.getRowIndex(node) > 0);
        
        // Group schedules by day
        Map<Integer, List<CourseSchedule>> weeklySchedules = scheduleData.stream()
            .filter(CourseSchedule::isActive)
            .collect(Collectors.groupingBy(CourseSchedule::getDayOfWeek));
        
        int maxRowsPerDay = 10; // Maximum rows to display
        
        for (int day = 2; day <= 8; day++) { // Monday to Sunday
            List<CourseSchedule> daySchedules = weeklySchedules.get(day);
            if (daySchedules != null) {
                daySchedules.sort((s1, s2) -> s1.getStartTime().compareTo(s2.getStartTime()));
                
                for (int i = 0; i < Math.min(daySchedules.size(), maxRowsPerDay); i++) {
                    CourseSchedule schedule = daySchedules.get(i);
                    
                    VBox scheduleBox = new VBox(2);
                    scheduleBox.setPadding(new Insets(5));
                    scheduleBox.setStyle("-fx-background-color: #E3F2FD; -fx-background-radius: 5px; -fx-border-color: #2196F3; -fx-border-radius: 5px;");
                    
                    Label timeLabel = new Label(schedule.getTimeRange());
                    timeLabel.setStyle("-fx-font-size: 10px; -fx-font-weight: bold;");
                    
                    Label courseLabel = new Label(schedule.getCourseName());
                    courseLabel.setStyle("-fx-font-size: 9px;");
                    courseLabel.setWrapText(true);
                    
                    Label roomLabel = new Label(schedule.getClassroom());
                    roomLabel.setStyle("-fx-font-size: 8px; -fx-text-fill: #666;");
                    
                    scheduleBox.getChildren().addAll(timeLabel, courseLabel, roomLabel);
                    
                    int columnIndex = day - 2; // Convert to 0-based index
                    int rowIndex = i + 1; // Start from row 1 (row 0 is headers)
                    
                    weeklyScheduleGrid.add(scheduleBox, columnIndex, rowIndex);
                }
            }
        }
    }
    
    @FXML
    private void showAddScheduleDialog() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/com/example/doancuoikyjava/add-schedule-dialog.fxml"));
            Parent root = loader.load();

            AddScheduleDialogController controller = loader.getController();

            Stage dialogStage = new Stage();
            dialogStage.setTitle("Thêm lịch học mới");
            dialogStage.initModality(Modality.WINDOW_MODAL);
            dialogStage.initOwner(scheduleTableView.getScene().getWindow());
            dialogStage.setScene(new Scene(root));
            dialogStage.setResizable(false);

            dialogStage.showAndWait();

            // Refresh data if schedule was saved
            if (controller.isSaved()) {
                loadData();
                showAlert("Thành công", "Lịch học đã được thêm thành công!");
            }

        } catch (IOException e) {
            System.err.println("❌ Error opening add schedule dialog: " + e.getMessage());
            showAlert("Lỗi", "Không thể mở dialog thêm lịch học: " + e.getMessage());
        }
    }
    
    @FXML
    private void applyFilter() {
        // Implementation for filtering
        showAlert("Thông báo", "Tính năng lọc đang được phát triển!");
    }
    
    @FXML
    private void clearFilter() {
        courseFilterCombo.setValue("Tất cả");
        teacherFilterCombo.setValue("Tất cả");
        dayFilterCombo.setValue("Tất cả");
        loadData();
    }
    
    @FXML
    private void refreshData() {
        loadData();
        showAlert("Thành công", "Dữ liệu đã được làm mới!");
    }
    
    @FXML
    private void goBack() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/admin-dashboard.fxml", 
                                   "Admin Dashboard");
        } catch (IOException e) {
            showAlert("Lỗi", "Không thể quay lại trang chính: " + e.getMessage());
        }
    }
    
    private void editSchedule(CourseSchedule schedule) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/com/example/doancuoikyjava/add-schedule-dialog.fxml"));
            Parent root = loader.load();

            AddScheduleDialogController controller = loader.getController();
            controller.setEditingSchedule(schedule); // Set schedule for editing

            Stage dialogStage = new Stage();
            dialogStage.setTitle("Chỉnh sửa lịch học");
            dialogStage.initModality(Modality.WINDOW_MODAL);
            dialogStage.initOwner(scheduleTableView.getScene().getWindow());
            dialogStage.setScene(new Scene(root));
            dialogStage.setResizable(false);

            dialogStage.showAndWait();

            // Refresh data if schedule was saved
            if (controller.isSaved()) {
                loadData();
                showAlert("Thành công", "Lịch học đã được cập nhật thành công!");
            }

        } catch (IOException e) {
            System.err.println("❌ Error opening edit schedule dialog: " + e.getMessage());
            showAlert("Lỗi", "Không thể mở dialog chỉnh sửa lịch học: " + e.getMessage());
        }
    }
    
    private void deleteSchedule(CourseSchedule schedule) {
        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("Xác nhận xóa");
        confirmAlert.setHeaderText("Bạn có chắc chắn muốn xóa lịch học này?");
        confirmAlert.setContentText(schedule.getCourseName() + " - " + schedule.getFullScheduleText());
        
        confirmAlert.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                boolean success = scheduleService.deleteSchedule(schedule.getScheduleId());
                if (success) {
                    loadData();
                    showAlert("Thành công", "Đã xóa lịch học!");
                } else {
                    showAlert("Lỗi", "Không thể xóa lịch học!");
                }
            }
        });
    }
    
    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
