package com.example.doancuoikyjava.test;

import com.example.doancuoikyjava.model.Teacher;
import com.example.doancuoikyjava.model.User;
import com.example.doancuoikyjava.model.Course;
import com.example.doancuoikyjava.service.DatabaseUserService;
import com.example.doancuoikyjava.service.CourseService;
import com.example.doancuoikyjava.util.DatabaseConnection;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

public class TestTeacherCourseSync {
    
    public static void main(String[] args) {
        System.out.println("🧪 Testing Teacher-Course Sync Functionality");
        System.out.println("==============================================");
        
        // Test database connection
        if (!DatabaseConnection.testConnection()) {
            System.err.println("❌ Không thể kết nối database!");
            return;
        }
        
        DatabaseUserService userService = new DatabaseUserService();
        CourseService courseService = new CourseService();
        
        // Test 1: Thêm giảng viên mới với môn dạy
        testAddTeacherWithNewCourses(userService, courseService);
        
        // Test 2: Kiểm tra môn học đã được tạo trong admin
        testCoursesCreatedInAdmin(courseService);
        
        // Test 3: Cập nhật môn dạy của giảng viên
        testUpdateTeacherCourses(userService, courseService);
        
        System.out.println("\n✅ Tất cả test đã hoàn thành!");
    }
    
    private static void testAddTeacherWithNewCourses(DatabaseUserService userService, CourseService courseService) {
        System.out.println("\n📝 Test 1: Thêm giảng viên với môn dạy mới");
        System.out.println("------------------------------------------");
        
        try {
            Teacher teacher = new Teacher();
            
            // Generate new ID
            String teacherId = userService.generateNextUserId(User.UserRole.TEACHER);
            teacher.setUserId(teacherId);
            teacher.setTeacherId(teacherId);
            
            // Set basic info
            teacher.setUsername("sync_test_teacher_" + System.currentTimeMillis());
            teacher.setPassword("password123");
            teacher.setFullName("Nguyễn Văn Sync Test");
            teacher.setEmail("<EMAIL>");
            teacher.setPhone("0987654321");
            teacher.setAddress("123 Sync Test Street");
            teacher.setDateOfBirth(LocalDate.of(1985, 3, 15));
            teacher.setRole(User.UserRole.TEACHER);
            
            // Set teacher specific info
            teacher.setDepartment("Khoa CNTT");
            teacher.setPosition("Giảng viên");
            teacher.setSalary(18000000.0);
            teacher.setQualification("Tiến sĩ");
            teacher.setExperienceYears(8);
            
            // Set teaching courses - môn học mới chưa có trong hệ thống
            List<String> teachingCourses = Arrays.asList(
                "Machine Learning Cơ bản",
                "Trí tuệ nhân tạo",
                "Phân tích dữ liệu lớn"
            );
            teacher.setTeachingCourses(teachingCourses);
            
            System.out.println("📝 Thông tin giảng viên:");
            System.out.println("  - ID: " + teacher.getTeacherId());
            System.out.println("  - Full Name: " + teacher.getFullName());
            System.out.println("  - Teaching Courses: " + teacher.getTeachingCourses());
            
            boolean result = userService.addUser(teacher);
            
            if (result) {
                System.out.println("✅ Thêm giảng viên thành công!");
                System.out.println("🔄 Hệ thống sẽ tự động tạo các môn học tương ứng...");
            } else {
                System.out.println("❌ Thêm giảng viên thất bại!");
            }
            
        } catch (Exception e) {
            System.err.println("❌ Lỗi test add teacher: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testCoursesCreatedInAdmin(CourseService courseService) {
        System.out.println("\n📖 Test 2: Kiểm tra môn học đã được tạo");
        System.out.println("----------------------------------------");
        
        try {
            List<Course> allCourses = courseService.getAllCourses();
            
            System.out.println("📋 Danh sách tất cả môn học trong hệ thống:");
            for (Course course : allCourses) {
                System.out.println("  📚 " + course.getCourseName() + 
                                 " (ID: " + course.getCourseId() + 
                                 ", Giảng viên: " + course.getTeacherName() + ")");
            }
            
            // Kiểm tra các môn học mới
            String[] newCourses = {"Machine Learning Cơ bản", "Trí tuệ nhân tạo", "Phân tích dữ liệu lớn"};
            
            for (String courseName : newCourses) {
                boolean found = allCourses.stream()
                    .anyMatch(course -> course.getCourseName().equals(courseName));
                
                if (found) {
                    System.out.println("✅ Môn học '" + courseName + "' đã được tạo tự động");
                } else {
                    System.out.println("❌ Môn học '" + courseName + "' KHÔNG được tạo");
                }
            }
            
        } catch (Exception e) {
            System.err.println("❌ Lỗi test courses: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testUpdateTeacherCourses(DatabaseUserService userService, CourseService courseService) {
        System.out.println("\n🔄 Test 3: Cập nhật môn dạy của giảng viên");
        System.out.println("------------------------------------------");
        
        try {
            // Lấy giảng viên vừa tạo
            List<User> teachers = userService.getUsersByRole(User.UserRole.TEACHER);
            Teacher testTeacher = null;
            
            for (User user : teachers) {
                if (user instanceof Teacher && user.getFullName().contains("Sync Test")) {
                    testTeacher = (Teacher) user;
                    break;
                }
            }
            
            if (testTeacher == null) {
                System.out.println("⚠️ Không tìm thấy giảng viên test để cập nhật");
                return;
            }
            
            System.out.println("📝 Cập nhật môn dạy cho: " + testTeacher.getFullName());
            System.out.println("  Môn dạy cũ: " + testTeacher.getTeachingCourses());
            
            // Cập nhật môn dạy - thêm môn mới, bỏ môn cũ
            List<String> newCourses = Arrays.asList(
                "Machine Learning Cơ bản", // Giữ lại
                "Deep Learning", // Môn mới
                "Computer Vision", // Môn mới
                "Natural Language Processing" // Môn mới
            );
            testTeacher.setTeachingCourses(newCourses);
            
            boolean result = userService.updateUser(testTeacher);
            
            if (result) {
                System.out.println("✅ Cập nhật thành công!");
                
                // Load lại để kiểm tra
                Teacher updatedTeacher = (Teacher) userService.getUserById(testTeacher.getUserId());
                System.out.println("  Môn dạy mới: " + updatedTeacher.getTeachingCourses());
                
                // Kiểm tra trong admin courses
                System.out.println("\n📋 Kiểm tra môn học sau khi cập nhật:");
                List<Course> allCourses = courseService.getAllCourses();
                
                for (String courseName : newCourses) {
                    Course course = allCourses.stream()
                        .filter(c -> c.getCourseName().equals(courseName))
                        .findFirst()
                        .orElse(null);
                    
                    if (course != null) {
                        System.out.println("  ✅ " + courseName + " → Giảng viên: " + course.getTeacherName());
                    } else {
                        System.out.println("  ❌ " + courseName + " → Không tìm thấy");
                    }
                }
                
            } else {
                System.out.println("❌ Cập nhật thất bại!");
            }
            
        } catch (Exception e) {
            System.err.println("❌ Lỗi test update teacher: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
