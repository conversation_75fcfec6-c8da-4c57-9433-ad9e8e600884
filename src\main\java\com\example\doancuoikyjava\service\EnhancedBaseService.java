package com.example.doancuoikyjava.service;

import com.example.doancuoikyjava.util.DatabaseConnection;
import com.example.doancuoikyjava.util.EnhancedDatabaseConnection;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;

/**
 * Enhanced Base Service with improved performance, caching, and error handling
 */
public abstract class EnhancedBaseService {
    
    protected boolean useDatabaseStorage;
    protected boolean useEnhancedConnection;
    private static final ExecutorService executorService = Executors.newFixedThreadPool(5);
    
    // Cache for frequently accessed data
    protected static final java.util.Map<String, Object> cache = new java.util.concurrent.ConcurrentHashMap<>();
    protected static final long CACHE_EXPIRY = 5 * 60 * 1000; // 5 minutes
    
    public EnhancedBaseService() {
        this.useDatabaseStorage = DatabaseConnection.testConnection();
        this.useEnhancedConnection = true; // Enable enhanced features
        
        if (useDatabaseStorage) {
            System.out.println("🚀 " + getServiceName() + ": Using Enhanced SQL Server Database");
        } else {
            System.out.println("📁 " + getServiceName() + ": Using File-based Storage (Fallback)");
        }
    }
    
    /**
     * Get service name for logging
     */
    protected abstract String getServiceName();
    
    /**
     * Execute query with enhanced error handling and performance monitoring
     */
    protected <T> List<T> executeQuery(String sql, Function<ResultSet, T> mapper, Object... params) {
        List<T> results = new ArrayList<>();
        long startTime = System.currentTimeMillis();
        
        try {
            if (useEnhancedConnection) {
                try (ResultSet rs = EnhancedDatabaseConnection.executeQuery(sql, params)) {
                    while (rs.next()) {
                        T item = mapper.apply(rs);
                        if (item != null) {
                            results.add(item);
                        }
                    }
                }
            } else {
                // Fallback to regular connection
                try (Connection conn = DatabaseConnection.getConnection();
                     PreparedStatement stmt = conn.prepareStatement(sql)) {
                    
                    for (int i = 0; i < params.length; i++) {
                        stmt.setObject(i + 1, params[i]);
                    }
                    
                    try (ResultSet rs = stmt.executeQuery()) {
                        while (rs.next()) {
                            T item = mapper.apply(rs);
                            if (item != null) {
                                results.add(item);
                            }
                        }
                    }
                }
            }
            
            long duration = System.currentTimeMillis() - startTime;
            if (duration > 500) {
                System.out.println("⚠️ " + getServiceName() + " slow query (" + duration + "ms): " + 
                                 sql.substring(0, Math.min(50, sql.length())) + "...");
            }
            
        } catch (SQLException e) {
            long duration = System.currentTimeMillis() - startTime;
            System.err.println("❌ " + getServiceName() + " query failed (" + duration + "ms): " + e.getMessage());
            handleSQLException(e, sql);
        }
        
        return results;
    }
    
    /**
     * Execute update with enhanced error handling
     */
    protected boolean executeUpdate(String sql, Object... params) {
        long startTime = System.currentTimeMillis();
        
        try {
            int result;
            if (useEnhancedConnection) {
                result = EnhancedDatabaseConnection.executeUpdate(sql, params);
            } else {
                // Fallback to regular connection
                try (Connection conn = DatabaseConnection.getConnection();
                     PreparedStatement stmt = conn.prepareStatement(sql)) {
                    
                    for (int i = 0; i < params.length; i++) {
                        stmt.setObject(i + 1, params[i]);
                    }
                    
                    result = stmt.executeUpdate();
                }
            }
            
            long duration = System.currentTimeMillis() - startTime;
            if (duration > 300) {
                System.out.println("⚠️ " + getServiceName() + " slow update (" + duration + "ms)");
            }
            
            // Clear related cache entries on successful update
            if (result > 0) {
                clearRelatedCache();
            }
            
            return result > 0;
            
        } catch (SQLException e) {
            long duration = System.currentTimeMillis() - startTime;
            System.err.println("❌ " + getServiceName() + " update failed (" + duration + "ms): " + e.getMessage());
            handleSQLException(e, sql);
            return false;
        }
    }
    
    /**
     * Execute query asynchronously
     */
    protected <T> CompletableFuture<List<T>> executeQueryAsync(String sql, Function<ResultSet, T> mapper, Object... params) {
        return CompletableFuture.supplyAsync(() -> executeQuery(sql, mapper, params), executorService);
    }
    
    /**
     * Get from cache or execute query
     */
    protected <T> T getFromCacheOrExecute(String cacheKey, java.util.function.Supplier<T> supplier) {
        CacheEntry entry = (CacheEntry) cache.get(cacheKey);
        
        if (entry != null && !entry.isExpired()) {
            System.out.println("📋 " + getServiceName() + " cache hit: " + cacheKey);
            return (T) entry.getValue();
        }
        
        // Execute and cache result
        T result = supplier.get();
        if (result != null) {
            cache.put(cacheKey, new CacheEntry(result, System.currentTimeMillis() + CACHE_EXPIRY));
            System.out.println("💾 " + getServiceName() + " cached: " + cacheKey);
        }
        
        return result;
    }
    
    /**
     * Clear cache entry
     */
    protected void clearCache(String cacheKey) {
        cache.remove(cacheKey);
        System.out.println("🗑️ " + getServiceName() + " cache cleared: " + cacheKey);
    }
    
    /**
     * Clear all related cache entries (override in subclasses)
     */
    protected void clearRelatedCache() {
        // Default implementation - clear all cache
        cache.clear();
        System.out.println("🧹 " + getServiceName() + " all cache cleared");
    }
    
    /**
     * Handle SQL exceptions with detailed logging
     */
    protected void handleSQLException(SQLException e, String sql) {
        System.err.println("🔍 " + getServiceName() + " SQL Error Details:");
        System.err.println("   SQL State: " + e.getSQLState());
        System.err.println("   Error Code: " + e.getErrorCode());
        System.err.println("   Message: " + e.getMessage());
        System.err.println("   SQL: " + (sql.length() > 100 ? sql.substring(0, 100) + "..." : sql));
        
        // Log stack trace for debugging
        if (e.getErrorCode() == 2) { // Connection timeout
            System.err.println("💡 Suggestion: Check database server connection and network");
        } else if (e.getErrorCode() == 18456) { // Login failed
            System.err.println("💡 Suggestion: Check username and password");
        }
    }
    
    /**
     * Validate input parameters
     */
    protected boolean validateInput(Object... params) {
        for (Object param : params) {
            if (param == null) {
                System.err.println("❌ " + getServiceName() + " validation failed: null parameter");
                return false;
            }
            if (param instanceof String && ((String) param).trim().isEmpty()) {
                System.err.println("❌ " + getServiceName() + " validation failed: empty string parameter");
                return false;
            }
        }
        return true;
    }
    
    /**
     * Get service statistics
     */
    public String getServiceStats() {
        return String.format(
            "%s Statistics:\n" +
            "- Database Storage: %s\n" +
            "- Enhanced Connection: %s\n" +
            "- Cache Entries: %d\n" +
            "- Service Status: %s",
            getServiceName(),
            useDatabaseStorage ? "Enabled" : "Disabled",
            useEnhancedConnection ? "Enabled" : "Disabled",
            cache.size(),
            "Active"
        );
    }
    
    /**
     * Cache entry with expiration
     */
    private static class CacheEntry {
        private final Object value;
        private final long expiryTime;
        
        public CacheEntry(Object value, long expiryTime) {
            this.value = value;
            this.expiryTime = expiryTime;
        }
        
        public Object getValue() {
            return value;
        }
        
        public boolean isExpired() {
            return System.currentTimeMillis() > expiryTime;
        }
    }
    
    /**
     * Cleanup resources
     */
    public static void cleanup() {
        cache.clear();
        executorService.shutdown();
        System.out.println("🧹 Enhanced Base Service cleanup completed");
    }
}
