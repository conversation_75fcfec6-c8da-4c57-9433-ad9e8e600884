package com.example.doancuoikyjava.service;

import com.example.doancuoikyjava.model.Grade;
import com.example.doancuoikyjava.util.DatabaseConnection;

import java.sql.*;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

public class DatabaseGradeService {
    
    public List<Grade> getAllGrades() {
        List<Grade> grades = new ArrayList<>();
        String sql = "SELECT * FROM Grades ORDER BY date_recorded DESC";
        
        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            ResultSet rs = stmt.executeQuery();
            
            while (rs.next()) {
                grades.add(createGradeFromResultSet(rs));
            }
            
        } catch (SQLException e) {
            System.err.println("❌ Lỗi lấy danh sách grades: " + e.getMessage());
        }
        
        return grades;
    }
    
    public List<Grade> getGradesByStudent(String studentId) {
        List<Grade> grades = new ArrayList<>();
        String sql = "SELECT * FROM Grades WHERE student_id = ? ORDER BY date_recorded DESC";
        
        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, studentId);
            ResultSet rs = stmt.executeQuery();
            
            while (rs.next()) {
                grades.add(createGradeFromResultSet(rs));
            }
            
        } catch (SQLException e) {
            System.err.println("❌ Lỗi lấy grades theo student: " + e.getMessage());
        }
        
        return grades;
    }
    
    public List<Grade> getGradesByCourse(String courseId) {
        List<Grade> grades = new ArrayList<>();
        String sql = "SELECT * FROM Grades WHERE course_id = ? ORDER BY date_recorded DESC";
        
        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, courseId);
            ResultSet rs = stmt.executeQuery();
            
            while (rs.next()) {
                grades.add(createGradeFromResultSet(rs));
            }
            
        } catch (SQLException e) {
            System.err.println("❌ Lỗi lấy grades theo course: " + e.getMessage());
        }
        
        return grades;
    }
    
    public List<Grade> getGradesByTeacher(String teacherId) {
        List<Grade> grades = new ArrayList<>();
        String sql = "SELECT * FROM Grades WHERE teacher_id = ? ORDER BY date_recorded DESC";
        
        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, teacherId);
            ResultSet rs = stmt.executeQuery();
            
            while (rs.next()) {
                grades.add(createGradeFromResultSet(rs));
            }
            
        } catch (SQLException e) {
            System.err.println("❌ Lỗi lấy grades theo teacher: " + e.getMessage());
        }
        
        return grades;
    }
    
    public boolean addGrade(Grade grade) {
        String sql = """
            INSERT INTO Grades (grade_id, student_id, course_id, course_name, score, credits, 
                              letter_grade, grade_point, semester, teacher_id, date_recorded)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """;
        
        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            
            // Generate grade ID if not set
            if (grade.getGradeId() == null || grade.getGradeId().isEmpty()) {
                grade.setGradeId(generateNextGradeId());
            }
            
            // Set date if not set
            if (grade.getDateRecorded() == null) {
                grade.setDateRecorded(LocalDate.now());
            }
            
            stmt.setString(1, grade.getGradeId());
            stmt.setString(2, grade.getStudentId());
            stmt.setString(3, grade.getCourseId());
            stmt.setString(4, grade.getCourseName());
            stmt.setDouble(5, grade.getScore());
            stmt.setInt(6, grade.getCredits());
            stmt.setString(7, grade.getLetterGrade());
            stmt.setDouble(8, grade.getGradePoint());
            stmt.setString(9, grade.getSemester());
            stmt.setString(10, grade.getTeacherId());
            stmt.setDate(11, Date.valueOf(grade.getDateRecorded()));
            
            int result = stmt.executeUpdate();
            
            if (result > 0) {
                // Update student GPA
                updateStudentGPA(grade.getStudentId());
                return true;
            }
            
        } catch (SQLException e) {
            System.err.println("❌ Lỗi thêm grade: " + e.getMessage());
        }
        
        return false;
    }
    
    public boolean updateGrade(Grade grade) {
        String sql = """
            UPDATE Grades SET score = ?, credits = ?, letter_grade = ?, grade_point = ?, 
                            semester = ?, course_name = ?
            WHERE grade_id = ?
            """;
        
        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            
            stmt.setDouble(1, grade.getScore());
            stmt.setInt(2, grade.getCredits());
            stmt.setString(3, grade.getLetterGrade());
            stmt.setDouble(4, grade.getGradePoint());
            stmt.setString(5, grade.getSemester());
            stmt.setString(6, grade.getCourseName());
            stmt.setString(7, grade.getGradeId());
            
            int result = stmt.executeUpdate();
            
            if (result > 0) {
                // Update student GPA
                updateStudentGPA(grade.getStudentId());
                return true;
            }
            
        } catch (SQLException e) {
            System.err.println("❌ Lỗi cập nhật grade: " + e.getMessage());
        }
        
        return false;
    }
    
    public boolean deleteGrade(String gradeId) {
        // Get student ID before deleting
        String studentId = null;
        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement selectStmt = conn.prepareStatement("SELECT student_id FROM Grades WHERE grade_id = ?");
            selectStmt.setString(1, gradeId);
            ResultSet rs = selectStmt.executeQuery();
            if (rs.next()) {
                studentId = rs.getString("student_id");
            }
        } catch (SQLException e) {
            System.err.println("❌ Lỗi lấy student ID: " + e.getMessage());
        }
        
        String sql = "DELETE FROM Grades WHERE grade_id = ?";
        
        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, gradeId);
            
            int result = stmt.executeUpdate();
            
            if (result > 0 && studentId != null) {
                // Update student GPA
                updateStudentGPA(studentId);
                return true;
            }
            
        } catch (SQLException e) {
            System.err.println("❌ Lỗi xóa grade: " + e.getMessage());
        }
        
        return false;
    }
    
    private void updateStudentGPA(String studentId) {
        try {
            // Calculate new GPA
            String sql = """
                SELECT AVG(CAST(grade_point * credits AS FLOAT) / credits) as gpa
                FROM Grades 
                WHERE student_id = ?
                """;
            
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, studentId);
            ResultSet rs = stmt.executeQuery();
            
            double newGpa = 0.0;
            if (rs.next()) {
                newGpa = rs.getDouble("gpa");
            }
            
            // Update student GPA in Students table
            String updateSql = "UPDATE Students SET gpa = ? WHERE student_id = ?";
            PreparedStatement updateStmt = conn.prepareStatement(updateSql);
            updateStmt.setDouble(1, newGpa);
            updateStmt.setString(2, studentId);
            updateStmt.executeUpdate();
            
        } catch (SQLException e) {
            System.err.println("❌ Lỗi cập nhật GPA: " + e.getMessage());
        }
    }
    
    private String generateNextGradeId() {
        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(
                "SELECT MAX(CAST(SUBSTRING(grade_id, 4, LEN(grade_id)) AS INT)) FROM Grades WHERE grade_id LIKE 'GRD%'"
            );
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                int maxNumber = rs.getInt(1);
                return "GRD" + String.format("%03d", maxNumber + 1);
            }
            
        } catch (SQLException e) {
            System.err.println("❌ Lỗi tạo grade ID: " + e.getMessage());
        }
        
        return "GRD001";
    }
    
    private Grade createGradeFromResultSet(ResultSet rs) throws SQLException {
        Grade grade = new Grade();
        grade.setGradeId(rs.getString("grade_id"));
        grade.setStudentId(rs.getString("student_id"));
        grade.setCourseId(rs.getString("course_id"));
        grade.setCourseName(rs.getString("course_name"));
        grade.setScore(rs.getDouble("score"));
        grade.setCredits(rs.getInt("credits"));
        grade.setLetterGrade(rs.getString("letter_grade"));
        grade.setGradePoint(rs.getDouble("grade_point"));
        grade.setSemester(rs.getString("semester"));
        grade.setTeacherId(rs.getString("teacher_id"));
        
        Date dateRecorded = rs.getDate("date_recorded");
        if (dateRecorded != null) {
            grade.setDateRecorded(dateRecorded.toLocalDate());
        }
        
        return grade;
    }
    
    public double getAverageGradeByCourse(String courseId) {
        String sql = "SELECT AVG(score) as avg_score FROM Grades WHERE course_id = ?";
        
        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, courseId);
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                return rs.getDouble("avg_score");
            }
            
        } catch (SQLException e) {
            System.err.println("❌ Lỗi tính điểm trung bình: " + e.getMessage());
        }
        
        return 0.0;
    }
    
    public long getTotalGrades() {
        String sql = "SELECT COUNT(*) FROM Grades";
        
        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                return rs.getLong(1);
            }
            
        } catch (SQLException e) {
            System.err.println("❌ Lỗi đếm grades: " + e.getMessage());
        }
        
        return 0;
    }
}
