// IMPORTANT: If you see "JavaFX runtime components are missing", you must add VM options:
// --module-path "path\to\javafx-sdk-XX\lib" --add-modules javafx.controls,javafx.fxml
// Replace "path\to\javafx-sdk-XX\lib" with the path to your JavaFX SDK lib folder.
// In IntelliJ: Run > Edit Configurations > VM options
// In command line: java --module-path ... --add-modules ...
package com.example.doancuoikyjava;

import com.example.doancuoikyjava.util.SceneManager;
import javafx.application.Application;
import javafx.stage.Stage;

/**
 * Main Application Entry Point
 * Shows login screen and handles role-based navigation
 */
public class MainApplication extends Application {

    @Override
    public void start(Stage primaryStage) {
        try {
            // Initialize SceneManager
            SceneManager.setPrimaryStage(primaryStage);
            
            // Show login screen
            SceneManager.switchScene("/com/example/doancuoikyjava/login.fxml", 
                                   "🎓 <PERSON><PERSON> thống <PERSON>uản lý <PERSON>h viên - Đăng nhập");
            
            System.out.println("🚀 Student Management System launched successfully!");
            System.out.println("📋 Please login with your credentials:");
            System.out.println("   👤 Admin: admin/admin123");
            System.out.println("   👨‍🏫 Teacher: teacher/teacher123");
            System.out.println("   🎓 Student: student/student123");
            
        } catch (Exception e) {
            System.err.println("❌ Error launching application: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        // NOTE: If you use Java 11+ and JavaFX SDK, add VM options:
        // --module-path "path\to\javafx-sdk-XX\lib" --add-modules javafx.controls,javafx.fxml
        // Replace "path\to\javafx-sdk-XX\lib" with your JavaFX SDK lib folder.
        // Example for IntelliJ IDEA: Run/Debug Configurations > VM options
        System.out.println("🎓 Starting Student Management System");
        System.out.println("════════════════════════════════════════");
        System.out.println("📋 System Features:");
        System.out.println("   🔧 Admin: Complete system management");
        System.out.println("   👨‍🏫 Teacher: Course and schedule management");
        System.out.println("   🎓 Student: Learning and enrollment");
        System.out.println("════════════════════════════════════════");
        
        launch(args);
    }
}