package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.Notification;
import com.example.doancuoikyjava.service.NotificationService;
import com.example.doancuoikyjava.util.SceneManager;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.geometry.Insets;

import java.io.IOException;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.ResourceBundle;

public class NotificationManagementController implements Initializable {
    
    @FXML private Label welcomeLabel;
    @FXML private Button backButton;
    @FXML private Button addNotificationButton;
    @FXML private Button refreshButton;
    
    // Statistics
    @FXML private Label totalNotificationsLabel;
    @FXML private Label activeNotificationsLabel;
    @FXML private Label urgentNotificationsLabel;
    @FXML private Label totalViewsLabel;
    
    // Filters
    @FXML private ComboBox<String> typeFilterComboBox;
    @FXML private ComboBox<String> targetFilterComboBox;
    @FXML private ComboBox<String> priorityFilterComboBox;
    @FXML private ComboBox<String> statusFilterComboBox;
    @FXML private TextField searchField;
    
    // Table
    @FXML private TableView<Notification> notificationsTableView;
    @FXML private TableColumn<Notification, String> idColumn;
    @FXML private TableColumn<Notification, String> titleColumn;
    @FXML private TableColumn<Notification, String> typeColumn;
    @FXML private TableColumn<Notification, String> targetColumn;
    @FXML private TableColumn<Notification, String> priorityColumn;
    @FXML private TableColumn<Notification, String> statusColumn;
    @FXML private TableColumn<Notification, String> createdAtColumn;
    @FXML private TableColumn<Notification, String> viewsColumn;
    @FXML private TableColumn<Notification, Void> actionsColumn;
    
    private NotificationService notificationService;
    private ObservableList<Notification> allNotifications;
    private ObservableList<Notification> filteredNotifications;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        notificationService = new NotificationService();
        allNotifications = FXCollections.observableArrayList();
        filteredNotifications = FXCollections.observableArrayList();
        
        setupUI();
        setupTableColumns();
        setupFilters();
        loadNotifications();
    }
    
    private void setupUI() {
        welcomeLabel.setText("Quản lý Thông báo - " + SceneManager.getCurrentUser().getFullName());
    }
    
    private void setupTableColumns() {
        idColumn.setCellValueFactory(new PropertyValueFactory<>("notificationId"));
        titleColumn.setCellValueFactory(new PropertyValueFactory<>("title"));
        
        typeColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(cellData.getValue().getTypeIcon() + " " + cellData.getValue().getType()));
        
        targetColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(cellData.getValue().getTargetIcon() + " " + cellData.getValue().getTargetAudience()));
        
        priorityColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(cellData.getValue().getPriorityIcon() + " " + cellData.getValue().getPriority()));
        
        statusColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(cellData.getValue().getStatusText()));
        
        createdAtColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(cellData.getValue().getFormattedCreatedAt()));
        
        viewsColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(String.valueOf(cellData.getValue().getViewCount())));
        
        // Actions column
        actionsColumn.setCellFactory(param -> new TableCell<Notification, Void>() {
            private final Button viewBtn = new Button("👁️");
            private final Button editBtn = new Button("✏️");
            private final Button toggleBtn = new Button("🔄");
            private final Button deleteBtn = new Button("🗑️");
            private final HBox buttonBox = new HBox(5);
            
            {
                viewBtn.setStyle("-fx-background-color: #3498db; -fx-text-fill: white; -fx-font-size: 10px;");
                editBtn.setStyle("-fx-background-color: #f39c12; -fx-text-fill: white; -fx-font-size: 10px;");
                toggleBtn.setStyle("-fx-background-color: #9b59b6; -fx-text-fill: white; -fx-font-size: 10px;");
                deleteBtn.setStyle("-fx-background-color: #e74c3c; -fx-text-fill: white; -fx-font-size: 10px;");
                
                viewBtn.setOnAction(e -> {
                    Notification notification = getTableView().getItems().get(getIndex());
                    viewNotification(notification);
                });
                
                editBtn.setOnAction(e -> {
                    Notification notification = getTableView().getItems().get(getIndex());
                    editNotification(notification);
                });
                
                toggleBtn.setOnAction(e -> {
                    Notification notification = getTableView().getItems().get(getIndex());
                    toggleNotificationStatus(notification);
                });
                
                deleteBtn.setOnAction(e -> {
                    Notification notification = getTableView().getItems().get(getIndex());
                    deleteNotification(notification);
                });
                
                buttonBox.getChildren().addAll(viewBtn, editBtn, toggleBtn, deleteBtn);
            }
            
            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(buttonBox);
                }
            }
        });
        
        notificationsTableView.setItems(filteredNotifications);
    }
    
    private void setupFilters() {
        // Type filter
        typeFilterComboBox.getItems().addAll(
            "Tất cả loại", "GENERAL", "ACADEMIC", "EXAM", "EVENT", "URGENT"
        );
        typeFilterComboBox.setValue("Tất cả loại");
        
        // Target filter
        targetFilterComboBox.getItems().addAll(
            "Tất cả đối tượng", "ALL", "STUDENTS", "TEACHERS", "SPECIFIC"
        );
        targetFilterComboBox.setValue("Tất cả đối tượng");
        
        // Priority filter
        priorityFilterComboBox.getItems().addAll(
            "Tất cả mức độ", "LOW", "NORMAL", "HIGH", "URGENT"
        );
        priorityFilterComboBox.setValue("Tất cả mức độ");
        
        // Status filter
        statusFilterComboBox.getItems().addAll(
            "Tất cả trạng thái", "Đang hoạt động", "Đã tắt", "Đã hết hạn"
        );
        statusFilterComboBox.setValue("Tất cả trạng thái");
        
        // Search functionality
        searchField.textProperty().addListener((observable, oldValue, newValue) -> filterNotifications());
        typeFilterComboBox.setOnAction(e -> filterNotifications());
        targetFilterComboBox.setOnAction(e -> filterNotifications());
        priorityFilterComboBox.setOnAction(e -> filterNotifications());
        statusFilterComboBox.setOnAction(e -> filterNotifications());
    }
    
    private void loadNotifications() {
        List<Notification> notifications = notificationService.getAllNotifications();
        allNotifications.setAll(notifications);
        filterNotifications();
        updateStatistics();
    }
    
    private void filterNotifications() {
        String searchText = searchField.getText().toLowerCase();
        String selectedType = typeFilterComboBox.getValue();
        String selectedTarget = targetFilterComboBox.getValue();
        String selectedPriority = priorityFilterComboBox.getValue();
        String selectedStatus = statusFilterComboBox.getValue();
        
        List<Notification> filtered = allNotifications.stream()
                .filter(notification -> {
                    boolean matchesSearch = searchText.isEmpty() || 
                            notification.getTitle().toLowerCase().contains(searchText) ||
                            notification.getContent().toLowerCase().contains(searchText);
                    
                    boolean matchesType = selectedType == null || selectedType.equals("Tất cả loại") ||
                            notification.getType().toString().equals(selectedType);
                    
                    boolean matchesTarget = selectedTarget == null || selectedTarget.equals("Tất cả đối tượng") ||
                            notification.getTargetAudience().toString().equals(selectedTarget);
                    
                    boolean matchesPriority = selectedPriority == null || selectedPriority.equals("Tất cả mức độ") ||
                            notification.getPriority().toString().equals(selectedPriority);
                    
                    boolean matchesStatus = selectedStatus == null || selectedStatus.equals("Tất cả trạng thái") ||
                            notification.getStatusText().equals(selectedStatus);
                    
                    return matchesSearch && matchesType && matchesTarget && matchesPriority && matchesStatus;
                })
                .toList();
        
        filteredNotifications.setAll(filtered);
    }
    
    private void updateStatistics() {
        int total = allNotifications.size();
        long active = allNotifications.stream().filter(Notification::isActive).count();
        long urgent = allNotifications.stream()
                .filter(n -> n.getPriority() == Notification.Priority.URGENT).count();
        int totalViews = allNotifications.stream().mapToInt(Notification::getViewCount).sum();
        
        totalNotificationsLabel.setText(String.valueOf(total));
        activeNotificationsLabel.setText(String.valueOf(active));
        urgentNotificationsLabel.setText(String.valueOf(urgent));
        totalViewsLabel.setText(String.valueOf(totalViews));
    }
    
    @FXML
    private void addNotification() {
        Optional<Notification> result = showNotificationDialog(null);
        result.ifPresent(notification -> {
            notification.setNotificationId(notificationService.generateNotificationId());
            notification.setCreatedBy(SceneManager.getCurrentUser().getUserId());
            
            if (notificationService.addNotification(notification)) {
                showAlert("Thành công", "Thêm thông báo thành công!");
                loadNotifications();
            } else {
                showAlert("Lỗi", "Không thể thêm thông báo!");
            }
        });
    }
    
    private void viewNotification(Notification notification) {
        notificationService.incrementViewCount(notification.getNotificationId());
        
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Chi tiết thông báo");
        alert.setHeaderText(notification.getTypeIcon() + " " + notification.getTitle());
        
        StringBuilder content = new StringBuilder();
        content.append("📋 Nội dung:\n").append(notification.getContent()).append("\n\n");
        content.append("🎯 Đối tượng: ").append(notification.getTargetAudience()).append("\n");
        content.append("⚡ Mức độ: ").append(notification.getPriority()).append("\n");
        content.append("👤 Tạo bởi: ").append(notification.getCreatedBy()).append("\n");
        content.append("📅 Ngày tạo: ").append(notification.getFormattedCreatedAt()).append("\n");
        content.append("⏰ Hết hạn: ").append(notification.getFormattedExpiryDate()).append("\n");
        content.append("👁️ Lượt xem: ").append(notification.getViewCount()).append("\n");
        
        TextArea textArea = new TextArea(content.toString());
        textArea.setEditable(false);
        textArea.setWrapText(true);
        textArea.setPrefSize(500, 400);
        
        alert.getDialogPane().setContent(textArea);
        alert.showAndWait();
        
        loadNotifications(); // Refresh to update view count
    }
    
    private void editNotification(Notification notification) {
        Optional<Notification> result = showNotificationDialog(notification);
        result.ifPresent(updatedNotification -> {
            updatedNotification.setNotificationId(notification.getNotificationId());
            updatedNotification.setCreatedBy(notification.getCreatedBy());
            updatedNotification.setCreatedAt(notification.getCreatedAt());
            updatedNotification.setViewCount(notification.getViewCount());
            
            if (notificationService.updateNotification(updatedNotification)) {
                showAlert("Thành công", "Cập nhật thông báo thành công!");
                loadNotifications();
            } else {
                showAlert("Lỗi", "Không thể cập nhật thông báo!");
            }
        });
    }
    
    private void toggleNotificationStatus(Notification notification) {
        if (notificationService.toggleNotificationStatus(notification.getNotificationId())) {
            showAlert("Thành công", "Đã thay đổi trạng thái thông báo!");
            loadNotifications();
        } else {
            showAlert("Lỗi", "Không thể thay đổi trạng thái thông báo!");
        }
    }

    private void deleteNotification(Notification notification) {
        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("Xác nhận xóa");
        confirmAlert.setHeaderText("Xóa thông báo");
        confirmAlert.setContentText("Bạn có chắc chắn muốn xóa thông báo \"" + notification.getTitle() + "\"?");

        Optional<ButtonType> result = confirmAlert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            if (notificationService.deleteNotification(notification.getNotificationId())) {
                showAlert("Thành công", "Xóa thông báo thành công!");
                loadNotifications();
            } else {
                showAlert("Lỗi", "Không thể xóa thông báo!");
            }
        }
    }

    private Optional<Notification> showNotificationDialog(Notification notification) {
        Dialog<Notification> dialog = new Dialog<>();
        dialog.setTitle(notification == null ? "Thêm thông báo mới" : "Chỉnh sửa thông báo");
        dialog.setHeaderText(notification == null ? "Tạo thông báo mới" : "Cập nhật thông báo");

        // Create form fields
        TextField titleField = new TextField();
        titleField.setPromptText("Tiêu đề thông báo");

        TextArea contentArea = new TextArea();
        contentArea.setPromptText("Nội dung thông báo");
        contentArea.setPrefRowCount(5);
        contentArea.setWrapText(true);

        ComboBox<Notification.NotificationType> typeComboBox = new ComboBox<>();
        typeComboBox.getItems().addAll(Notification.NotificationType.values());

        ComboBox<Notification.TargetAudience> targetComboBox = new ComboBox<>();
        targetComboBox.getItems().addAll(Notification.TargetAudience.values());

        ComboBox<Notification.Priority> priorityComboBox = new ComboBox<>();
        priorityComboBox.getItems().addAll(Notification.Priority.values());

        DatePicker expiryDatePicker = new DatePicker();

        CheckBox activeCheckBox = new CheckBox("Kích hoạt ngay");
        activeCheckBox.setSelected(true);

        // Fill fields if editing
        if (notification != null) {
            titleField.setText(notification.getTitle());
            contentArea.setText(notification.getContent());
            typeComboBox.setValue(notification.getType());
            targetComboBox.setValue(notification.getTargetAudience());
            priorityComboBox.setValue(notification.getPriority());
            if (notification.getExpiryDate() != null) {
                expiryDatePicker.setValue(notification.getExpiryDate().toLocalDate());
            }
            activeCheckBox.setSelected(notification.isActive());
        }

        // Create layout
        GridPane grid = new GridPane();
        grid.setHgap(10);
        grid.setVgap(10);
        grid.setPadding(new Insets(20, 150, 10, 10));

        grid.add(new Label("Tiêu đề:"), 0, 0);
        grid.add(titleField, 1, 0);
        grid.add(new Label("Nội dung:"), 0, 1);
        grid.add(contentArea, 1, 1);
        grid.add(new Label("Loại:"), 0, 2);
        grid.add(typeComboBox, 1, 2);
        grid.add(new Label("Đối tượng:"), 0, 3);
        grid.add(targetComboBox, 1, 3);
        grid.add(new Label("Mức độ:"), 0, 4);
        grid.add(priorityComboBox, 1, 4);
        grid.add(new Label("Ngày hết hạn:"), 0, 5);
        grid.add(expiryDatePicker, 1, 5);
        grid.add(activeCheckBox, 1, 6);

        dialog.getDialogPane().setContent(grid);

        // Add buttons
        ButtonType saveButtonType = new ButtonType("Lưu", ButtonBar.ButtonData.OK_DONE);
        dialog.getDialogPane().getButtonTypes().addAll(saveButtonType, ButtonType.CANCEL);

        // Convert result
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == saveButtonType) {
                // Validation
                if (titleField.getText().trim().isEmpty()) {
                    showAlert("Lỗi", "Tiêu đề không được để trống!");
                    return null;
                }
                if (contentArea.getText().trim().isEmpty()) {
                    showAlert("Lỗi", "Nội dung không được để trống!");
                    return null;
                }
                if (typeComboBox.getValue() == null) {
                    showAlert("Lỗi", "Vui lòng chọn loại thông báo!");
                    return null;
                }
                if (targetComboBox.getValue() == null) {
                    showAlert("Lỗi", "Vui lòng chọn đối tượng!");
                    return null;
                }
                if (priorityComboBox.getValue() == null) {
                    showAlert("Lỗi", "Vui lòng chọn mức độ ưu tiên!");
                    return null;
                }

                Notification newNotification = new Notification();
                newNotification.setTitle(titleField.getText().trim());
                newNotification.setContent(contentArea.getText().trim());
                newNotification.setType(typeComboBox.getValue());
                newNotification.setTargetAudience(targetComboBox.getValue());
                newNotification.setPriority(priorityComboBox.getValue());
                newNotification.setActive(activeCheckBox.isSelected());

                if (expiryDatePicker.getValue() != null) {
                    newNotification.setExpiryDate(expiryDatePicker.getValue().atStartOfDay());
                }

                return newNotification;
            }
            return null;
        });

        return dialog.showAndWait();
    }

    @FXML
    private void refreshData() {
        loadNotifications();
        showAlert("Thành công", "Dữ liệu đã được làm mới!");
    }

    @FXML
    private void goBack() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/admin-dashboard.fxml",
                                   "Quản trị viên - " + SceneManager.getCurrentUser().getFullName());
        } catch (IOException e) {
            showAlert("Lỗi", "Không thể quay lại trang chính: " + e.getMessage());
        }
    }

    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
