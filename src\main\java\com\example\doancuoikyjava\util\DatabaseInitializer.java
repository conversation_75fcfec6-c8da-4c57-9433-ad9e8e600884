package com.example.doancuoikyjava.util;

import com.example.doancuoikyjava.model.*;
import com.example.doancuoikyjava.service.DatabaseUserService;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;

public class DatabaseInitializer {
    
    public static void initializeDefaultData() {
        try {
            // Kiểm tra xem đã có dữ liệu chưa
            if (hasData()) {
                System.out.println("📊 Database đã có dữ liệu, bỏ qua khởi tạo");
                return;
            }
            
            System.out.println("🔄 Đang khởi tạo dữ liệu mẫu...");
            
            DatabaseUserService userService = new DatabaseUserService();
            
            // Tạo Admin mặc định
            createDefaultAdmin(userService);
            
            // Tạo Teacher mặc định
            createDefaultTeacher(userService);
            
            // Tạo Students mặc định
            createDefaultStudents(userService);
            
            // Tạo Courses mặc định
            createDefaultCourses();
            
            System.out.println("✅ Khởi tạo dữ liệu mẫu thành công!");
            
        } catch (Exception e) {
            System.err.println("❌ Lỗi khởi tạo dữ liệu: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static boolean hasData() {
        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement("SELECT COUNT(*) FROM Users");
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                return rs.getInt(1) > 0;
            }
        } catch (SQLException e) {
            System.err.println("❌ Lỗi kiểm tra dữ liệu: " + e.getMessage());
        }
        return false;
    }
    
    private static void createDefaultAdmin(DatabaseUserService userService) {
        Admin admin = new Admin("ADM001", "admin", "admin123", "Quản trị viên",
                "<EMAIL>", "0123456789", LocalDate.of(1980, 1, 1),
                "123 Đường ABC, TP.HCM", "ADM001", "Quản trị", "Quản trị viên");

        if (userService.addUser(admin)) {
            System.out.println("✅ Tạo Admin mặc định thành công");
        } else {
            System.err.println("❌ Lỗi tạo Admin mặc định");
        }
    }
    
    private static void createDefaultTeacher(DatabaseUserService userService) {
        Teacher teacher = new Teacher("TCH001", "teacher", "teacher123", "Nguyễn Văn A",
                "<EMAIL>", "0987654321", LocalDate.of(1985, 5, 15),
                "456 Đường XYZ, TP.HCM", "TCH001", "Công nghệ thông tin", 
                "Giảng viên", 15000000, "Thạc sĩ", 10);
        
        if (userService.addUser(teacher)) {
            System.out.println("✅ Tạo Teacher mặc định thành công");
        } else {
            System.err.println("❌ Lỗi tạo Teacher mặc định");
        }
    }
    
    private static void createDefaultStudents(DatabaseUserService userService) {
        // Sinh viên 1 - Giỏi
        Student student1 = new Student("STU001", "student", "student123", "Trần Thị B",
                "<EMAIL>", "0369852147", LocalDate.of(2002, 8, 20),
                "789 Đường DEF, TP.HCM", "STU001", "CNTT01", "Công nghệ thông tin", 2);
        student1.setGpa(3.8);
        
        // Sinh viên 2 - Khá
        Student student2 = new Student("STU002", "student2", "student123", "Nguyễn Văn C",
                "<EMAIL>", "0369852148", LocalDate.of(2002, 3, 15),
                "456 Đường GHI, TP.HCM", "STU002", "CNTT01", "Công nghệ thông tin", 2);
        student2.setGpa(3.4);
        
        // Sinh viên 3 - Trung bình
        Student student3 = new Student("STU003", "student3", "student123", "Lê Thị D",
                "<EMAIL>", "0369852149", LocalDate.of(2002, 7, 10),
                "123 Đường JKL, TP.HCM", "STU003", "CNTT02", "Công nghệ thông tin", 2);
        student3.setGpa(2.8);
        
        // Sinh viên 4 - Yếu
        Student student4 = new Student("STU004", "student4", "student123", "Phạm Văn E",
                "<EMAIL>", "0369852150", LocalDate.of(2002, 12, 5),
                "789 Đường MNO, TP.HCM", "STU004", "KT01", "Kế toán", 3);
        student4.setGpa(2.2);
        
        // Sinh viên 5 - Kém
        Student student5 = new Student("STU005", "student5", "student123", "Hoàng Thị F",
                "<EMAIL>", "0369852151", LocalDate.of(2001, 9, 25),
                "321 Đường PQR, TP.HCM", "STU005", "KT01", "Kế toán", 3);
        student5.setGpa(1.8);
        
        Student[] students = {student1, student2, student3, student4, student5};
        
        for (Student student : students) {
            if (userService.addUser(student)) {
                System.out.println("✅ Tạo sinh viên " + student.getFullName() + " thành công");
            } else {
                System.err.println("❌ Lỗi tạo sinh viên " + student.getFullName());
            }
        }
    }
    
    private static void createDefaultCourses() {
        try {
            Connection conn = DatabaseConnection.getConnection();

            // Kiểm tra xem đã có môn học chưa
            PreparedStatement checkStmt = conn.prepareStatement("SELECT COUNT(*) FROM Courses");
            ResultSet rs = checkStmt.executeQuery();
            rs.next();
            int courseCount = rs.getInt(1);

            if (courseCount > 0) {
                System.out.println("📚 Đã có " + courseCount + " môn học trong database");
                return;
            }

            String sql = """
                INSERT INTO Courses (course_id, course_name, description, credits, teacher_id, teacher_name,
                                   schedule, classroom, max_students)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """;

            PreparedStatement stmt = conn.prepareStatement(sql);

            // Danh sách môn học
            Object[][] courses = {
                {"CS101", "Lập trình Java cơ bản", "Khóa học lập trình Java cơ bản cho người mới bắt đầu", 3, "TCH001", "Nguyễn Văn A", "Thứ 2, 4, 6 - 7:30-9:30", "A101", 30},
                {"CS102", "Cơ sở dữ liệu", "Khóa học về hệ quản trị cơ sở dữ liệu", 3, "TCH001", "Nguyễn Văn A", "Thứ 3, 5, 7 - 9:30-11:30", "A102", 25},
                {"CS103", "Lập trình Java nâng cao", "Khóa học lập trình Java nâng cao với Spring Framework", 4, "TCH001", "Nguyễn Văn A", "Thứ 2, 4 - 13:30-16:30", "B201", 35},
                {"MATH101", "Giải tích", "Toán giải tích cơ bản cho sinh viên kỹ thuật", 3, "TCH001", "Nguyễn Văn A", "Thứ 3, 5 - 7:30-10:30", "A201", 40},
                {"CS201", "Cấu trúc dữ liệu và giải thuật", "Các cấu trúc dữ liệu cơ bản và thuật toán", 4, "TCH001", "Nguyễn Văn A", "Thứ 2, 6 - 9:30-12:30", "B301", 30},
                {"CS301", "Thiết kế web", "Thiết kế và phát triển website với HTML, CSS, JavaScript", 3, "TCH001", "Nguyễn Văn A", "Thứ 4, 6 - 13:30-16:30", "C101", 25},
                {"ENG201", "Tiếng Anh 2", "Tiếng Anh cơ bản cấp độ 2", 2, "TCH001", "Nguyễn Văn A", "Thứ 3, 7 - 7:30-9:30", "D101", 45},
                {"ENG301", "Tiếng Anh chuyên ngành 2", "Tiếng Anh chuyên ngành công nghệ thông tin", 2, "TCH001", "Nguyễn Văn A", "Thứ 5, 7 - 7:30-9:30", "D102", 40},
                {"BUS101", "Khởi nghiệp đổi mới sáng tạo", "Kỹ năng khởi nghiệp và tư duy sáng tạo", 2, "TCH001", "Nguyễn Văn A", "Thứ 6 - 13:30-15:30", "E101", 50},
                {"PE101", "Giáo dục thể chất", "Rèn luyện sức khỏe và thể lực", 1, "TCH001", "Nguyễn Văn A", "Thứ 7 - 6:30-8:30", "Sân thể thao", 60}
            };

            // Thêm từng môn học
            for (Object[] course : courses) {
                stmt.setString(1, (String) course[0]);
                stmt.setString(2, (String) course[1]);
                stmt.setString(3, (String) course[2]);
                stmt.setInt(4, (Integer) course[3]);
                stmt.setString(5, (String) course[4]);
                stmt.setString(6, (String) course[5]);
                stmt.setString(7, (String) course[6]);
                stmt.setString(8, (String) course[7]);
                stmt.setInt(9, (Integer) course[8]);
                stmt.executeUpdate();

                System.out.println("✅ Thêm môn học: " + course[1]);
            }

            // Đăng ký sinh viên vào một số môn học
            createDefaultEnrollments(conn);

            // Đồng bộ dữ liệu với bảng Teacher_Courses
            syncExistingCoursesToTeacherCourses(conn);

            // Tạo lịch học mẫu
            createSampleSchedules(conn);

            System.out.println("✅ Tạo " + courses.length + " môn học thành công");

        } catch (SQLException e) {
            System.err.println("❌ Lỗi tạo môn học: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void createDefaultEnrollments(Connection conn) throws SQLException {
        String enrollSql = """
            INSERT INTO Course_Enrollments (course_id, student_id)
            VALUES (?, ?)
            """;

        PreparedStatement enrollStmt = conn.prepareStatement(enrollSql);

        // Đăng ký sinh viên vào các môn học
        String[][] enrollments = {
            {"CS101", "STU001"}, {"CS102", "STU001"}, {"MATH101", "STU001"}, {"ENG201", "STU001"},
            {"CS101", "STU002"}, {"CS103", "STU002"}, {"CS201", "STU002"}, {"ENG301", "STU002"},
            {"CS102", "STU003"}, {"CS301", "STU003"}, {"BUS101", "STU003"}, {"PE101", "STU003"},
            {"MATH101", "STU004"}, {"ENG201", "STU004"}, {"BUS101", "STU004"}, {"PE101", "STU004"},
            {"CS101", "STU005"}, {"ENG201", "STU005"}, {"PE101", "STU005"}
        };

        for (String[] enrollment : enrollments) {
            try {
                enrollStmt.setString(1, enrollment[0]);
                enrollStmt.setString(2, enrollment[1]);
                enrollStmt.executeUpdate();
            } catch (SQLException e) {
                // Ignore duplicate enrollment errors
                if (!e.getMessage().contains("UNIQUE")) {
                    throw e;
                }
            }
        }

        System.out.println("✅ Tạo đăng ký môn học mẫu thành công");
    }

    /**
     * Đồng bộ dữ liệu từ bảng Courses sang bảng Teacher_Courses
     */
    private static void syncExistingCoursesToTeacherCourses(Connection conn) throws SQLException {
        System.out.println("🔄 Đang đồng bộ dữ liệu môn học với Teacher_Courses...");

        // Lấy tất cả môn học hiện có
        String selectSql = "SELECT teacher_id, course_name FROM Courses WHERE teacher_id IS NOT NULL";
        PreparedStatement selectStmt = conn.prepareStatement(selectSql);
        ResultSet rs = selectStmt.executeQuery();

        // Insert vào Teacher_Courses
        String insertSql = """
            IF NOT EXISTS (SELECT 1 FROM Teacher_Courses WHERE teacher_id = ? AND course_name = ?)
            INSERT INTO Teacher_Courses (teacher_id, course_name) VALUES (?, ?)
            """;
        PreparedStatement insertStmt = conn.prepareStatement(insertSql);

        int syncCount = 0;
        while (rs.next()) {
            String teacherId = rs.getString("teacher_id");
            String courseName = rs.getString("course_name");

            if (teacherId != null && courseName != null) {
                insertStmt.setString(1, teacherId);
                insertStmt.setString(2, courseName);
                insertStmt.setString(3, teacherId);
                insertStmt.setString(4, courseName);

                try {
                    insertStmt.executeUpdate();
                    syncCount++;
                } catch (SQLException e) {
                    // Ignore duplicate entries
                    if (!e.getMessage().contains("UNIQUE")) {
                        throw e;
                    }
                }
            }
        }

        System.out.println("✅ Đồng bộ " + syncCount + " môn học vào Teacher_Courses");
    }

    /**
     * Tạo lịch học mẫu cho các môn học
     */
    private static void createSampleSchedules(Connection conn) throws SQLException {
        System.out.println("🕒 Tạo lịch học mẫu...");

        // Lấy danh sách môn học hiện có
        String selectCoursesSql = "SELECT course_id, course_name FROM Courses";
        PreparedStatement selectStmt = conn.prepareStatement(selectCoursesSql);
        ResultSet rs = selectStmt.executeQuery();

        String insertScheduleSql = """
            INSERT INTO Course_Schedule (course_id, day_of_week, start_time, end_time, classroom,
                                       week_type, semester, academic_year, is_active)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """;
        PreparedStatement insertStmt = conn.prepareStatement(insertScheduleSql);

        int scheduleCount = 0;

        while (rs.next()) {
            String courseId = rs.getString("course_id");
            String courseName = rs.getString("course_name");

            // Tạo 2-3 lịch học cho mỗi môn (các ngày khác nhau)
            Object[][] schedules = getSchedulesForCourse(courseName);

            for (Object[] schedule : schedules) {
                insertStmt.setString(1, courseId);
                insertStmt.setInt(2, (Integer) schedule[0]); // day_of_week
                insertStmt.setString(3, (String) schedule[1]); // start_time
                insertStmt.setString(4, (String) schedule[2]); // end_time
                insertStmt.setString(5, (String) schedule[3]); // classroom
                insertStmt.setString(6, "ALL"); // week_type
                insertStmt.setString(7, "HK1 2024-2025"); // semester
                insertStmt.setString(8, "2024-2025"); // academic_year
                insertStmt.setBoolean(9, true); // is_active

                try {
                    insertStmt.executeUpdate();
                    scheduleCount++;
                } catch (SQLException e) {
                    // Ignore duplicates
                    if (!e.getMessage().contains("UNIQUE") && !e.getMessage().contains("PRIMARY KEY")) {
                        System.err.println("⚠️ Error creating schedule for " + courseName + ": " + e.getMessage());
                    }
                }
            }
        }

        System.out.println("✅ Tạo " + scheduleCount + " lịch học mẫu");
    }

    /**
     * Lấy lịch học mẫu cho từng môn học
     */
    private static Object[][] getSchedulesForCourse(String courseName) {
        // Trả về mảng [day_of_week, start_time, end_time, classroom]

        if (courseName.contains("Lập trình java") || courseName.contains("Java")) {
            return new Object[][] {
                {2, "07:30:00", "09:30:00", "Lab A101"}, // Thứ 2
                {4, "13:30:00", "15:30:00", "Lab A101"}, // Thứ 4
                {6, "09:45:00", "11:45:00", "Lab A102"}  // Thứ 6
            };
        } else if (courseName.contains("Giải tích")) {
            return new Object[][] {
                {2, "09:45:00", "11:45:00", "A201"},
                {5, "07:30:00", "09:30:00", "A201"}
            };
        } else if (courseName.contains("Cấu trúc dữ liệu")) {
            return new Object[][] {
                {3, "07:30:00", "09:30:00", "Lab B201"},
                {6, "13:30:00", "15:30:00", "Lab B201"}
            };
        } else if (courseName.contains("Tiếng anh")) {
            return new Object[][] {
                {2, "13:30:00", "15:30:00", "B301"},
                {4, "09:45:00", "11:45:00", "B301"}
            };
        } else if (courseName.contains("Thiết kế web")) {
            return new Object[][] {
                {3, "13:30:00", "15:30:00", "Lab C101"},
                {5, "13:30:00", "15:30:00", "Lab C101"}
            };
        } else if (courseName.contains("Giáo dục thể chất")) {
            return new Object[][] {
                {4, "15:45:00", "17:45:00", "Sân thể thao"},
                {6, "15:45:00", "17:45:00", "Sân thể thao"}
            };
        } else if (courseName.contains("Machine Learning")) {
            return new Object[][] {
                {2, "15:45:00", "17:45:00", "Lab AI01"},
                {4, "15:45:00", "17:45:00", "Lab AI01"}
            };
        } else if (courseName.contains("Trí tuệ nhân tạo")) {
            return new Object[][] {
                {3, "09:45:00", "11:45:00", "Lab AI02"},
                {5, "09:45:00", "11:45:00", "Lab AI02"}
            };
        } else if (courseName.contains("Phân tích dữ liệu")) {
            return new Object[][] {
                {3, "15:45:00", "17:45:00", "Lab Data01"},
                {6, "07:30:00", "09:30:00", "Lab Data01"}
            };
        } else {
            // Default schedule for other courses
            return new Object[][] {
                {2, "09:45:00", "11:45:00", "A301"},
                {4, "07:30:00", "09:30:00", "A301"}
            };
        }
    }
}
