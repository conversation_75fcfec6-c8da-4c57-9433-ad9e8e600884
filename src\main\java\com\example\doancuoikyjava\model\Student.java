package com.example.doancuoikyjava.model;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

public class Student extends User {
    private String studentId;
    private String className;
    private String major;
    private int year;
    private double gpa;
    private List<String> enrolledCourses;
    private List<Grade> grades;

    public Student() {
        super();
        this.role = UserRole.STUDENT;
        this.enrolledCourses = new ArrayList<>();
        this.grades = new ArrayList<>();
    }

    public Student(String userId, String username, String password, String fullName,
                   String email, String phone, LocalDate dateOfBirth, String address,
                   String studentId, String className, String major, int year) {
        super(userId, username, password, fullName, email, phone, dateOfBirth, address, UserRole.STUDENT);
        this.studentId = studentId;
        this.className = className;
        this.major = major;
        this.year = year;
        this.gpa = 0.0;
        this.enrolledCourses = new ArrayList<>();
        this.grades = new ArrayList<>();
    }

    // Getters and Setters
    public String getStudentId() { return studentId; }
    public void setStudentId(String studentId) { this.studentId = studentId; }

    public String getClassName() { return className; }
    public void setClassName(String className) { this.className = className; }

    public String getMajor() { return major; }
    public void setMajor(String major) { this.major = major; }

    public int getYear() { return year; }
    public void setYear(int year) { this.year = year; }

    public double getGpa() { return gpa; }
    public void setGpa(double gpa) { this.gpa = gpa; }

    public List<String> getEnrolledCourses() { return enrolledCourses; }
    public void setEnrolledCourses(List<String> enrolledCourses) { this.enrolledCourses = enrolledCourses; }

    public List<Grade> getGrades() { return grades; }
    public void setGrades(List<Grade> grades) { this.grades = grades; }

    // Utility methods
    public void enrollInCourse(String courseId) {
        if (!enrolledCourses.contains(courseId)) {
            enrolledCourses.add(courseId);
        }
    }

    public void addGrade(Grade grade) {
        grades.add(grade);
        calculateGPA();
    }

    private void calculateGPA() {
        if (grades.isEmpty()) {
            this.gpa = 0.0;
            return;
        }
        
        double totalPoints = 0.0;
        int totalCredits = 0;
        
        for (Grade grade : grades) {
            totalPoints += grade.getGradePoint() * grade.getCredits();
            totalCredits += grade.getCredits();
        }
        
        this.gpa = totalCredits > 0 ? totalPoints / totalCredits : 0.0;
    }

    @Override
    public String toString() {
        return "Student{" +
                "studentId='" + studentId + '\'' +
                ", fullName='" + fullName + '\'' +
                ", className='" + className + '\'' +
                ", major='" + major + '\'' +
                ", year=" + year +
                ", gpa=" + gpa +
                '}';
    }
}
