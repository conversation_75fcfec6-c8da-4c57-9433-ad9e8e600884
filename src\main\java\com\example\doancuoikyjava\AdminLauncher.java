// IMPORTANT: If you see "JavaFX runtime components are missing", you must add VM options:
// --module-path "path\to\javafx-sdk-XX\lib" --add-modules javafx.controls,javafx.fxml
// Replace "path\to\javafx-sdk-XX\lib" with the path to your JavaFX SDK lib folder.
// In IntelliJ: Run > Edit Configurations > VM options
// In command line: java --module-path ... --add-modules ...
package com.example.doancuoikyjava;

import com.example.doancuoikyjava.model.Admin;
import com.example.doancuoikyjava.model.User;
import com.example.doancuoikyjava.service.UserService;
import com.example.doancuoikyjava.util.SceneManager;
import javafx.application.Application;
import javafx.stage.Stage;

/**
 * Admin Launcher - Direct access to Admin Dashboard
 */
public class AdminLauncher extends Application {

    private UserService userService;

    @Override
    public void start(Stage primaryStage) {
        try {
            this.userService = new UserService();
            
            // Initialize default admin if not exists
            initializeDefaultAdmin();
            
            // Get admin user
            User admin = userService.getUserById("ADMIN001");
            if (admin == null) {
                System.err.println("❌ Could not find or create admin user!");
                return;
            }

            // Set up SceneManager
            SceneManager.setPrimaryStage(primaryStage);
            SceneManager.setCurrentUser(admin);
            
            // Launch admin dashboard directly
            SceneManager.switchScene("/com/example/doancuoikyjava/admin-dashboard.fxml", 
                                   "🔧 ADMIN DASHBOARD - Hệ thống quản lý sinh viên");
            
            System.out.println("🔧 Admin Dashboard launched successfully!");
            System.out.println("👤 Logged in as: " + admin.getFullName());
            
        } catch (Exception e) {
            System.err.println("❌ Error launching Admin Dashboard: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void initializeDefaultAdmin() {
        try {
            if (userService.getUserById("ADMIN001") == null) {
                User admin = new Admin();
                admin.setUserId("ADMIN001");
                admin.setUsername("admin");
                admin.setPassword("admin123");
                admin.setFullName("Quản trị viên hệ thống");
                admin.setEmail("<EMAIL>");
                admin.setPhone("0123456789");
                admin.setRole(User.UserRole.ADMIN);
                
                userService.addUser(admin);
                System.out.println("✅ Created default admin user: admin/admin123");
            }
        } catch (Exception e) {
            System.err.println("❌ Error creating default admin: " + e.getMessage());
        }
    }

    public static void main(String[] args) {
        // NOTE: If you use Java 11+ and JavaFX SDK, add VM options:
        // --module-path "path\to\javafx-sdk-XX\lib" --add-modules javafx.controls,javafx.fxml
        // Replace "path\to\javafx-sdk-XX\lib" with your JavaFX SDK lib folder.
        // Example for IntelliJ IDEA: Run/Debug Configurations > VM options
        System.out.println("🔧 Starting Admin Dashboard");
        System.out.println("════════════════════════════════════════");
        System.out.println("👤 Auto-login as Administrator");
        System.out.println("🎯 Direct access to admin functions");
        System.out.println("════════════════════════════════════════");
        
        launch(args);
    }
}