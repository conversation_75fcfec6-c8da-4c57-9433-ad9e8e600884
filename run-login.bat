@echo off
title Login System - Student Management System
color 0D

echo.
echo ==========================================
echo      🔑 LOGIN SYSTEM LAUNCHER
echo ==========================================
echo.
echo 👤 Starting login interface...
echo 🎯 Custom authentication system
echo 📋 Enter your own credentials
echo.

REM Check Java
java -version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ ERROR: Java is not installed or not in PATH
    echo Please install Java and try again
    pause
    exit /b 1
)

echo 🔨 Compiling application...

REM Try to find Maven
set MVN_CMD=mvn
where mvn >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 🔍 Maven not in PATH, trying IntelliJ Maven...
    if exist "C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.3\plugins\maven\lib\maven3\bin\mvn.cmd" (
        set MVN_CMD="C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.3\plugins\maven\lib\maven3\bin\mvn.cmd"
    ) else if exist "C:\Users\<USER>\.m2\wrapper\dists\apache-maven-3.8.5-bin\5i5jha092a3i37g0paqnfr15e0\apache-maven-3.8.5\bin\mvn.cmd" (
        set MVN_CMD="C:\Users\<USER>\.m2\wrapper\dists\apache-maven-3.8.5-bin\5i5jha092a3i37g0paqnfr15e0\apache-maven-3.8.5\bin\mvn.cmd"
    )
)

call %MVN_CMD% clean compile -q

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Compilation failed!
    echo Trying alternative compilation...
    
    if not exist "target\classes" mkdir target\classes
    javac -cp "src/main/java" -d target/classes src/main/java/com/example/doancuoikyjava/MainApplication.java
    
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ Alternative compilation also failed!
        pause
        exit /b 1
    )
)

echo 🚀 Launching Login System...
echo.

REM Build classpath first
echo 📦 Building classpath...
call %MVN_CMD% dependency:build-classpath -Dmdep.outputFile=temp-cp.txt -q
if exist temp-cp.txt (
    set /p CP=<temp-cp.txt
    del temp-cp.txt
) else (
    echo ❌ Failed to build classpath!
    pause
    exit /b 1
)

REM Try direct Java with proper JavaFX configuration
echo 🚀 Starting with JavaFX runtime...
java --module-path "%CP%" ^
     --add-modules javafx.controls,javafx.fxml,javafx.base,javafx.graphics,javafx.swing ^
     --add-opens javafx.controls/com.sun.javafx.scene.control.behavior=ALL-UNNAMED ^
     --add-opens javafx.controls/com.sun.javafx.scene.control=ALL-UNNAMED ^
     --add-opens javafx.base/com.sun.javafx.binding=ALL-UNNAMED ^
     --add-opens javafx.graphics/com.sun.javafx.stage=ALL-UNNAMED ^
     --add-opens javafx.graphics/com.sun.javafx.application=ALL-UNNAMED ^
     -cp "target/classes;%CP%" ^
     com.example.doancuoikyjava.MainApplication

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ⚠️ Direct Java failed, trying JavaFX plugin...
    call %MVN_CMD% javafx:run -Djavafx.mainClass="com.example.doancuoikyjava.MainApplication" -q

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ⚠️ JavaFX plugin failed, trying exec plugin...
    call mvn exec:java -Dexec.mainClass="com.example.doancuoikyjava.MainApplication" -q
    
    if %ERRORLEVEL% NEQ 0 (
        echo.
        echo ⚠️ Maven exec failed, trying direct Java...
        
        REM Build classpath
        for /f "delims=" %%i in ('mvn dependency:build-classpath -Dmdep.outputFile=temp-cp.txt -q ^&^& type temp-cp.txt') do set CP=%%i
        
        java --module-path "%CP%" --add-modules javafx.controls,javafx.fxml,javafx.base,javafx.graphics --add-opens javafx.controls/com.sun.javafx.scene.control.behavior=ALL-UNNAMED --add-opens javafx.controls/com.sun.javafx.scene.control=ALL-UNNAMED --add-opens javafx.base/com.sun.javafx.binding=ALL-UNNAMED --add-opens javafx.graphics/com.sun.javafx.stage=ALL-UNNAMED -cp "target/classes;%CP%" com.example.doancuoikyjava.MainApplication
        
        if exist temp-cp.txt del temp-cp.txt
    )
)

echo.
echo 👋 Login System closed
pause
