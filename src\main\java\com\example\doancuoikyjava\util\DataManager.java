package com.example.doancuoikyjava.util;

import com.example.doancuoikyjava.model.*;
import java.io.*;
import java.time.LocalDate;
import java.util.*;

public class DataManager {
    private static final String DATA_DIR = "data/";
    private static final String USERS_FILE = DATA_DIR + "users.txt";
    private static final String COURSES_FILE = DATA_DIR + "courses.txt";
    private static final String GRADES_FILE = DATA_DIR + "grades.txt";

    static {
        createDataDirectory();
        initializeDefaultData();
    }

    private static void createDataDirectory() {
        File dir = new File(DATA_DIR);
        if (!dir.exists()) {
            dir.mkdirs();
        }
    }

    public static void initializeDefaultData() {
        if (!new File(USERS_FILE).exists()) {
            createDefaultUsers();
        }
        if (!new File(COURSES_FILE).exists()) {
            createDefaultCourses();
        }
    }

    private static void createDefaultUsers() {
        List<User> defaultUsers = new ArrayList<>();
        
        // Default Admin
        Admin admin = new Admin("ADM001", "admin", "admin123", "Quản trị viên",
                "<EMAIL>", "0123456789", LocalDate.of(1980, 1, 1),
                "123 Đường ABC, TP.HCM", "ADM001", "IT", "FULL");
        defaultUsers.add(admin);

        // Default Teacher
        Teacher teacher = new Teacher("TCH001", "teacher", "teacher123", "Nguyễn Văn A",
                "<EMAIL>", "0987654321", LocalDate.of(1985, 5, 15),
                "456 Đường XYZ, TP.HCM", "TCH001", "Khoa học máy tính", "Giảng viên",
                15000000, "Thạc sĩ", 5);
        defaultUsers.add(teacher);

        // Default Students with different GPAs for testing statistics
        Student student1 = new Student("STU001", "student", "student123", "Trần Thị B",
                "<EMAIL>", "0369852147", LocalDate.of(2002, 8, 20),
                "789 Đường DEF, TP.HCM", "STU001", "CNTT01", "Công nghệ thông tin", 2);
        student1.setGpa(3.8); // Giỏi
        defaultUsers.add(student1);

        Student student2 = new Student("STU002", "student2", "student123", "Nguyễn Văn C",
                "<EMAIL>", "0369852148", LocalDate.of(2002, 3, 15),
                "456 Đường GHI, TP.HCM", "STU002", "CNTT01", "Công nghệ thông tin", 2);
        student2.setGpa(3.4); // Khá
        defaultUsers.add(student2);

        Student student3 = new Student("STU003", "student3", "student123", "Lê Thị D",
                "<EMAIL>", "0369852149", LocalDate.of(2002, 7, 10),
                "123 Đường JKL, TP.HCM", "STU003", "CNTT02", "Công nghệ thông tin", 2);
        student3.setGpa(2.8); // Trung bình
        defaultUsers.add(student3);

        Student student4 = new Student("STU004", "student4", "student123", "Phạm Văn E",
                "<EMAIL>", "0369852150", LocalDate.of(2002, 12, 5),
                "789 Đường MNO, TP.HCM", "STU004", "KT01", "Kế toán", 3);
        student4.setGpa(2.2); // Yếu
        defaultUsers.add(student4);

        Student student5 = new Student("STU005", "student5", "student123", "Hoàng Thị F",
                "<EMAIL>", "0369852151", LocalDate.of(2001, 9, 25),
                "321 Đường PQR, TP.HCM", "STU005", "KT01", "Kế toán", 3);
        student5.setGpa(1.8); // Kém
        defaultUsers.add(student5);

        saveUsers(defaultUsers);
    }

    private static void createDefaultCourses() {
        List<Course> defaultCourses = new ArrayList<>();

        Course course1 = new Course("CS101", "Lập trình Java", "Khóa học lập trình Java cơ bản", 3,
                "TCH001", "Thứ 2, 4, 6 - 7:30-9:30", "A101", 30);
        course1.setTeacherName("Nguyễn Văn A");
        course1.addStudent("STU001"); // Add default student to course

        Course course2 = new Course("CS102", "Cơ sở dữ liệu", "Khóa học về hệ quản trị cơ sở dữ liệu", 3,
                "TCH001", "Thứ 3, 5, 7 - 9:30-11:30", "A102", 25);
        course2.setTeacherName("Nguyễn Văn A");
        course2.addStudent("STU001"); // Add default student to course

        defaultCourses.add(course1);
        defaultCourses.add(course2);

        saveCourses(defaultCourses);

        // Create some default grades
        createDefaultGrades();
    }

    private static void createDefaultGrades() {
        // This will be handled by GradeService when needed
        // Just create the grades file structure
        File gradesFile = new File("data/grades.txt");
        if (!gradesFile.exists()) {
            try {
                gradesFile.createNewFile();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    // User management methods
    public static List<User> loadUsers() {
        List<User> users = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(new FileReader(USERS_FILE))) {
            String line;
            while ((line = reader.readLine()) != null) {
                User user = parseUser(line);
                if (user != null) {
                    users.add(user);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return users;
    }

    public static void saveUsers(List<User> users) {
        try (PrintWriter writer = new PrintWriter(new FileWriter(USERS_FILE))) {
            for (User user : users) {
                writer.println(userToString(user));
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static User parseUser(String line) {
        if (line == null || line.trim().isEmpty()) {
            return null;
        }

        String[] parts = line.split("\\|");
        if (parts.length < 8) return null; // Minimum required fields

        // Handle old format without avatar field
        String role = parts.length > 8 ? parts[8] : "STUDENT"; // Default to STUDENT for old format
        User user = null;

        // Determine role-specific field positions based on format
        int avatarIndex = 9;
        int roleSpecificStart = 10;

        // Handle old format (without avatar field)
        if (parts.length < 10 && parts.length >= 8) {
            role = parts.length > 8 ? parts[8] : "STUDENT";
            avatarIndex = -1; // No avatar field
            roleSpecificStart = 9;
        }

        switch (role) {
            case "ADMIN":
                int adminRequiredFields = avatarIndex == -1 ? 12 : 13;
                if (parts.length >= adminRequiredFields) {
                    user = new Admin();
                    ((Admin) user).setAdminId(parts[roleSpecificStart]);
                    ((Admin) user).setDepartment(parts[roleSpecificStart + 1]);
                    ((Admin) user).setAccessLevel(parts[roleSpecificStart + 2]);
                }
                break;
            case "TEACHER":
                int teacherRequiredFields = avatarIndex == -1 ? 15 : 16;
                if (parts.length >= teacherRequiredFields) {
                    user = new Teacher();
                    ((Teacher) user).setTeacherId(parts[roleSpecificStart]);
                    ((Teacher) user).setDepartment(parts[roleSpecificStart + 1]);
                    ((Teacher) user).setPosition(parts[roleSpecificStart + 2]);
                    ((Teacher) user).setSalary(Double.parseDouble(parts[roleSpecificStart + 3]));
                    ((Teacher) user).setQualification(parts[roleSpecificStart + 4]);
                    ((Teacher) user).setExperienceYears(Integer.parseInt(parts[roleSpecificStart + 5]));
                }
                break;
            case "STUDENT":
                int studentRequiredFields = avatarIndex == -1 ? 13 : 14;
                if (parts.length >= studentRequiredFields) {
                    user = new Student();
                    ((Student) user).setStudentId(parts[roleSpecificStart]);
                    ((Student) user).setClassName(parts[roleSpecificStart + 1]);
                    ((Student) user).setMajor(parts[roleSpecificStart + 2]);
                    ((Student) user).setYear(Integer.parseInt(parts[roleSpecificStart + 3]));
                }
                break;
        }

        if (user != null) {
            user.setUserId(parts[0]);
            user.setUsername(parts[1]);
            user.setPassword(parts[2]);
            user.setFullName(parts[3]);
            user.setEmail(parts[4]);
            user.setPhone(parts[5]);

            // Safe date parsing
            try {
                if (parts[6] != null && !parts[6].equals("null") && !parts[6].trim().isEmpty()) {
                    user.setDateOfBirth(LocalDate.parse(parts[6]));
                } else {
                    user.setDateOfBirth(LocalDate.of(2000, 1, 1)); // Default date
                }
            } catch (Exception e) {
                System.err.println("⚠️ Error parsing date for user " + parts[0] + ": " + parts[6]);
                user.setDateOfBirth(LocalDate.of(2000, 1, 1)); // Default date
            }

            user.setAddress(parts[7]);

            // Set avatar path if exists (new field, might not exist in old data)
            if (avatarIndex != -1 && parts.length > avatarIndex) {
                String avatarPath = parts[avatarIndex];
                if (!avatarPath.equals("null") && !avatarPath.trim().isEmpty()) {
                    user.setAvatarPath(avatarPath);
                }
            }
        }

        return user;
    }

    private static String userToString(User user) {
        StringBuilder sb = new StringBuilder();
        sb.append(user.getUserId()).append("|")
          .append(user.getUsername()).append("|")
          .append(user.getPassword()).append("|")
          .append(user.getFullName()).append("|")
          .append(user.getEmail()).append("|")
          .append(user.getPhone()).append("|")
          .append(user.getDateOfBirth()).append("|")
          .append(user.getAddress()).append("|")
          .append(user.getRole()).append("|")
          .append(user.getAvatarPath() != null ? user.getAvatarPath() : "null").append("|");

        if (user instanceof Admin) {
            Admin admin = (Admin) user;
            sb.append(admin.getAdminId()).append("|")
              .append(admin.getDepartment()).append("|")
              .append(admin.getAccessLevel());
        } else if (user instanceof Teacher) {
            Teacher teacher = (Teacher) user;
            sb.append(teacher.getTeacherId()).append("|")
              .append(teacher.getDepartment()).append("|")
              .append(teacher.getPosition()).append("|")
              .append(teacher.getSalary()).append("|")
              .append(teacher.getQualification()).append("|")
              .append(teacher.getExperienceYears());
        } else if (user instanceof Student) {
            Student student = (Student) user;
            sb.append(student.getStudentId()).append("|")
              .append(student.getClassName()).append("|")
              .append(student.getMajor()).append("|")
              .append(student.getYear());
        }

        return sb.toString();
    }

    // Course management methods
    public static List<Course> loadCourses() {
        List<Course> courses = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(new FileReader(COURSES_FILE))) {
            String line;
            while ((line = reader.readLine()) != null) {
                Course course = parseCourse(line);
                if (course != null) {
                    courses.add(course);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return courses;
    }

    public static void saveCourses(List<Course> courses) {
        try (PrintWriter writer = new PrintWriter(new FileWriter(COURSES_FILE))) {
            for (Course course : courses) {
                writer.println(courseToString(course));
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static Course parseCourse(String line) {
        String[] parts = line.split("\\|");
        if (parts.length < 8) return null;

        Course course = new Course();
        course.setCourseId(parts[0]);
        course.setCourseName(parts[1]);
        course.setDescription(parts[2]);
        course.setCredits(Integer.parseInt(parts[3]));
        course.setTeacherId(parts[4]);
        course.setTeacherName(parts[5]);
        course.setSchedule(parts[6]);
        course.setClassroom(parts[7]);
        course.setMaxStudents(Integer.parseInt(parts[8]));

        if (parts.length > 9 && !parts[9].isEmpty()) {
            String[] students = parts[9].split(",");
            for (String student : students) {
                if (!student.trim().isEmpty()) {
                    course.getEnrolledStudents().add(student.trim());
                }
            }
        }

        return course;
    }

    private static String courseToString(Course course) {
        StringBuilder sb = new StringBuilder();
        sb.append(course.getCourseId()).append("|")
          .append(course.getCourseName()).append("|")
          .append(course.getDescription()).append("|")
          .append(course.getCredits()).append("|")
          .append(course.getTeacherId()).append("|")
          .append(course.getTeacherName()).append("|")
          .append(course.getSchedule()).append("|")
          .append(course.getClassroom()).append("|")
          .append(course.getMaxStudents()).append("|")
          .append(String.join(",", course.getEnrolledStudents()));

        return sb.toString();
    }
}
