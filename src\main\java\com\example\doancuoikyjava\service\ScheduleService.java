package com.example.doancuoikyjava.service;

import com.example.doancuoikyjava.model.CourseSchedule;
import com.example.doancuoikyjava.util.DatabaseConnection;

import java.sql.*;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Service for managing course schedules
 */
public class ScheduleService {
    
    private boolean useDatabaseStorage;
    
    public ScheduleService() {
        this.useDatabaseStorage = DatabaseConnection.testConnection();
        
        if (useDatabaseStorage) {
            System.out.println("📅 ScheduleService: Using SQL Server Database");
        } else {
            System.out.println("📁 ScheduleService: Database not available");
        }
    }
    
    /**
     * Add new schedule for a course
     */
    public boolean addSchedule(CourseSchedule schedule) {
        if (!useDatabaseStorage) {
            System.err.println("❌ Database not available for schedule operations");
            return false;
        }
        
        // Validate schedule
        if (!validateSchedule(schedule)) {
            return false;
        }
        
        // Check for conflicts
        if (hasScheduleConflict(schedule)) {
            System.err.println("❌ Schedule conflict detected for " + schedule.getFullScheduleText());
            return false;
        }
        
        String sql = """
            INSERT INTO Course_Schedule (course_id, teacher_id, teacher_name, day_of_week, start_time, end_time, classroom,
                                       week_type, semester, academic_year, is_active)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """;

        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, schedule.getCourseId());
            stmt.setString(2, schedule.getTeacherId());
            stmt.setString(3, schedule.getTeacherName());
            stmt.setInt(4, schedule.getDayOfWeek());
            stmt.setTime(5, Time.valueOf(schedule.getStartTime()));
            stmt.setTime(6, Time.valueOf(schedule.getEndTime()));
            stmt.setString(7, schedule.getClassroom());
            stmt.setString(8, schedule.getWeekType());
            stmt.setString(9, schedule.getSemester());
            stmt.setString(10, schedule.getAcademicYear());
            stmt.setBoolean(11, schedule.isActive());
            
            int result = stmt.executeUpdate();
            
            if (result > 0) {
                System.out.println("✅ Added schedule: " + schedule.getFullScheduleText() + " for course " + schedule.getCourseId());
                return true;
            }
            
        } catch (SQLException e) {
            System.err.println("❌ Error adding schedule: " + e.getMessage());
        }
        
        return false;
    }
    
    /**
     * Get all schedules for a course
     */
    public List<CourseSchedule> getSchedulesByCourse(String courseId) {
        if (!useDatabaseStorage) {
            return new ArrayList<>();
        }
        
        String sql = """
            SELECT cs.*, c.course_name
            FROM Course_Schedule cs
            LEFT JOIN Courses c ON cs.course_id = c.course_id
            WHERE cs.course_id = ? AND cs.is_active = 1
            ORDER BY cs.day_of_week, cs.start_time
            """;
        
        List<CourseSchedule> schedules = new ArrayList<>();
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, courseId);
            ResultSet rs = stmt.executeQuery();
            
            while (rs.next()) {
                CourseSchedule schedule = createScheduleFromResultSet(rs);
                schedules.add(schedule);
            }
            
        } catch (SQLException e) {
            System.err.println("❌ Error getting schedules for course " + courseId + ": " + e.getMessage());
        }
        
        return schedules;
    }
    
    /**
     * Get schedules for a teacher
     */
    public List<CourseSchedule> getSchedulesByTeacher(String teacherId) {
        if (!useDatabaseStorage) {
            return new ArrayList<>();
        }
        
        String sql = """
            SELECT cs.*, c.course_name
            FROM Course_Schedule cs
            INNER JOIN Courses c ON cs.course_id = c.course_id
            WHERE c.teacher_id = ? AND cs.is_active = 1
            ORDER BY cs.day_of_week, cs.start_time
            """;
        
        List<CourseSchedule> schedules = new ArrayList<>();
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, teacherId);
            ResultSet rs = stmt.executeQuery();
            
            while (rs.next()) {
                CourseSchedule schedule = createScheduleFromResultSet(rs);
                schedules.add(schedule);
            }
            
        } catch (SQLException e) {
            System.err.println("❌ Error getting schedules for teacher " + teacherId + ": " + e.getMessage());
        }
        
        return schedules;
    }
    
    /**
     * Get schedules for a student (based on enrolled courses)
     */
    public List<CourseSchedule> getSchedulesByStudent(String studentId) {
        if (!useDatabaseStorage) {
            return new ArrayList<>();
        }
        
        String sql = """
            SELECT cs.*, c.course_name
            FROM Course_Schedule cs
            INNER JOIN Courses c ON cs.course_id = c.course_id
            INNER JOIN Course_Enrollments ce ON c.course_id = ce.course_id
            WHERE ce.student_id = ? AND cs.is_active = 1
            ORDER BY cs.day_of_week, cs.start_time
            """;
        
        List<CourseSchedule> schedules = new ArrayList<>();
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, studentId);
            ResultSet rs = stmt.executeQuery();
            
            while (rs.next()) {
                CourseSchedule schedule = createScheduleFromResultSet(rs);
                schedules.add(schedule);
            }
            
        } catch (SQLException e) {
            System.err.println("❌ Error getting schedules for student " + studentId + ": " + e.getMessage());
        }
        
        return schedules;
    }
    
    /**
     * Update schedule
     */
    public boolean updateSchedule(CourseSchedule schedule) {
        if (!useDatabaseStorage) {
            return false;
        }
        
        if (!validateSchedule(schedule)) {
            return false;
        }
        
        String sql = """
            UPDATE Course_Schedule SET
                teacher_id = ?, teacher_name = ?, day_of_week = ?, start_time = ?, end_time = ?, classroom = ?,
                week_type = ?, semester = ?, academic_year = ?, is_active = ?
            WHERE schedule_id = ?
            """;

        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, schedule.getTeacherId());
            stmt.setString(2, schedule.getTeacherName());
            stmt.setInt(3, schedule.getDayOfWeek());
            stmt.setTime(4, Time.valueOf(schedule.getStartTime()));
            stmt.setTime(5, Time.valueOf(schedule.getEndTime()));
            stmt.setString(6, schedule.getClassroom());
            stmt.setString(7, schedule.getWeekType());
            stmt.setString(8, schedule.getSemester());
            stmt.setString(9, schedule.getAcademicYear());
            stmt.setBoolean(10, schedule.isActive());
            stmt.setInt(11, schedule.getScheduleId());
            
            int result = stmt.executeUpdate();
            
            if (result > 0) {
                System.out.println("✅ Updated schedule: " + schedule.getFullScheduleText());
                return true;
            }
            
        } catch (SQLException e) {
            System.err.println("❌ Error updating schedule: " + e.getMessage());
        }
        
        return false;
    }
    
    /**
     * Delete schedule
     */
    public boolean deleteSchedule(int scheduleId) {
        if (!useDatabaseStorage) {
            return false;
        }
        
        String sql = "UPDATE Course_Schedule SET is_active = 0 WHERE schedule_id = ?";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, scheduleId);
            int result = stmt.executeUpdate();
            
            if (result > 0) {
                System.out.println("✅ Deleted schedule with ID: " + scheduleId);
                return true;
            }
            
        } catch (SQLException e) {
            System.err.println("❌ Error deleting schedule: " + e.getMessage());
        }
        
        return false;
    }
    
    /**
     * Get weekly schedule grouped by day
     */
    public Map<Integer, List<CourseSchedule>> getWeeklyScheduleByTeacher(String teacherId) {
        List<CourseSchedule> schedules = getSchedulesByTeacher(teacherId);
        return schedules.stream()
                .collect(Collectors.groupingBy(CourseSchedule::getDayOfWeek));
    }
    
    /**
     * Get weekly schedule grouped by day for student
     */
    public Map<Integer, List<CourseSchedule>> getWeeklyScheduleByStudent(String studentId) {
        List<CourseSchedule> schedules = getSchedulesByStudent(studentId);
        return schedules.stream()
                .collect(Collectors.groupingBy(CourseSchedule::getDayOfWeek));
    }
    
    /**
     * Check for schedule conflicts
     */
    public boolean hasScheduleConflict(CourseSchedule newSchedule) {
        // Get all schedules for the same day and classroom
        String sql = """
            SELECT * FROM Course_Schedule 
            WHERE day_of_week = ? AND classroom = ? AND is_active = 1
            AND schedule_id != ?
            """;
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, newSchedule.getDayOfWeek());
            stmt.setString(2, newSchedule.getClassroom());
            stmt.setInt(3, newSchedule.getScheduleId()); // 0 for new schedules
            
            ResultSet rs = stmt.executeQuery();
            
            while (rs.next()) {
                CourseSchedule existing = createScheduleFromResultSet(rs);
                if (newSchedule.isConflictWith(existing)) {
                    return true;
                }
            }
            
        } catch (SQLException e) {
            System.err.println("❌ Error checking schedule conflicts: " + e.getMessage());
        }
        
        return false;
    }
    
    // Private helper methods
    private boolean validateSchedule(CourseSchedule schedule) {
        if (schedule == null) {
            System.err.println("❌ Schedule is null");
            return false;
        }
        
        if (schedule.getCourseId() == null || schedule.getCourseId().trim().isEmpty()) {
            System.err.println("❌ Course ID is required");
            return false;
        }
        
        if (schedule.getDayOfWeek() < 2 || schedule.getDayOfWeek() > 8) {
            System.err.println("❌ Invalid day of week: " + schedule.getDayOfWeek());
            return false;
        }
        
        if (schedule.getStartTime() == null || schedule.getEndTime() == null) {
            System.err.println("❌ Start time and end time are required");
            return false;
        }
        
        if (!schedule.getStartTime().isBefore(schedule.getEndTime())) {
            System.err.println("❌ Start time must be before end time");
            return false;
        }
        
        return true;
    }
    
    private CourseSchedule createScheduleFromResultSet(ResultSet rs) throws SQLException {
        CourseSchedule schedule = new CourseSchedule();

        schedule.setScheduleId(rs.getInt("schedule_id"));
        schedule.setCourseId(rs.getString("course_id"));
        schedule.setCourseName(rs.getString("course_name"));
        schedule.setTeacherId(rs.getString("teacher_id"));
        schedule.setTeacherName(rs.getString("teacher_name"));
        schedule.setDayOfWeek(rs.getInt("day_of_week"));
        
        Time startTime = rs.getTime("start_time");
        if (startTime != null) {
            schedule.setStartTime(startTime.toLocalTime());
        }
        
        Time endTime = rs.getTime("end_time");
        if (endTime != null) {
            schedule.setEndTime(endTime.toLocalTime());
        }
        
        schedule.setClassroom(rs.getString("classroom"));
        schedule.setWeekType(rs.getString("week_type"));
        schedule.setSemester(rs.getString("semester"));
        schedule.setAcademicYear(rs.getString("academic_year"));
        schedule.setActive(rs.getBoolean("is_active"));
        
        return schedule;
    }
}
