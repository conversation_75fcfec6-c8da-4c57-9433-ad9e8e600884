package com.example.doancuoikyjava.test;

import com.example.doancuoikyjava.model.Student;
import com.example.doancuoikyjava.model.User;
import com.example.doancuoikyjava.service.UserService;

import java.util.List;

public class TestStatistics {
    
    public static void main(String[] args) {
        testStatisticsData();
    }
    
    public static void testStatisticsData() {
        System.out.println("🧪 KIỂM TRA DỮ LIỆU CHO THỐNG KÊ");
        System.out.println("================================");
        
        try {
            UserService userService = new UserService();
            
            // Lấy tất cả sinh viên
            List<User> users = userService.getUsersByRole(User.UserRole.STUDENT);
            
            System.out.println("📊 Tổng số users với role STUDENT: " + (users != null ? users.size() : 0));
            
            if (users != null && !users.isEmpty()) {
                System.out.println("\n📋 DANH SÁCH SINH VIÊN:");
                System.out.println("MSSV\t\tTên\t\t\tLớp\t\tNgành\t\t\tGPA\tXếp loại");
                System.out.println("─────────────────────────────────────────────────────────────────────────────");
                
                int excellent = 0, good = 0, average = 0, weak = 0, poor = 0;
                double totalGpa = 0;
                
                for (User user : users) {
                    if (user instanceof Student) {
                        Student student = (Student) user;
                        double gpa = student.getGpa();
                        totalGpa += gpa;
                        
                        String classification = getGradeClassification(gpa);
                        
                        // Đếm theo xếp loại
                        switch (classification) {
                            case "Giỏi" -> excellent++;
                            case "Khá" -> good++;
                            case "Trung bình" -> average++;
                            case "Yếu" -> weak++;
                            case "Kém" -> poor++;
                        }
                        
                        System.out.printf("%-10s\t%-15s\t%-10s\t%-15s\t%.2f\t%s%n",
                            student.getStudentId(),
                            student.getFullName(),
                            student.getClassName() != null ? student.getClassName() : "N/A",
                            student.getMajor() != null ? student.getMajor() : "N/A",
                            gpa,
                            classification
                        );
                    }
                }
                
                System.out.println("─────────────────────────────────────────────────────────────────────────────");
                System.out.println("\n📈 THỐNG KÊ TỔNG QUAN:");
                System.out.println("🟢 Giỏi (GPA ≥ 3.6):           " + excellent + " sinh viên");
                System.out.println("🔵 Khá (3.2 ≤ GPA < 3.6):      " + good + " sinh viên");
                System.out.println("🟡 Trung bình (2.5 ≤ GPA < 3.2): " + average + " sinh viên");
                System.out.println("🟠 Yếu (2.0 ≤ GPA < 2.5):      " + weak + " sinh viên");
                System.out.println("🔴 Kém (GPA < 2.0):            " + poor + " sinh viên");
                
                double avgGpa = users.size() > 0 ? totalGpa / users.size() : 0;
                System.out.printf("\n📊 GPA trung bình toàn trường: %.2f%n", avgGpa);
                
                // Thống kê theo lớp
                System.out.println("\n📚 THỐNG KÊ THEO LỚP:");
                users.stream()
                    .filter(u -> u instanceof Student)
                    .map(u -> (Student) u)
                    .collect(java.util.stream.Collectors.groupingBy(
                        s -> s.getClassName() != null ? s.getClassName() : "Chưa phân lớp"
                    ))
                    .forEach((className, students) -> {
                        double classAvgGpa = students.stream()
                            .mapToDouble(Student::getGpa)
                            .average()
                            .orElse(0.0);
                        System.out.printf("   %s: %d sinh viên (GPA TB: %.2f)%n", 
                            className, students.size(), classAvgGpa);
                    });
                
                // Thống kê theo ngành
                System.out.println("\n🎓 THỐNG KÊ THEO NGÀNH:");
                users.stream()
                    .filter(u -> u instanceof Student)
                    .map(u -> (Student) u)
                    .collect(java.util.stream.Collectors.groupingBy(
                        s -> s.getMajor() != null ? s.getMajor() : "Chưa xác định"
                    ))
                    .forEach((major, students) -> {
                        double majorAvgGpa = students.stream()
                            .mapToDouble(Student::getGpa)
                            .average()
                            .orElse(0.0);
                        System.out.printf("   %s: %d sinh viên (GPA TB: %.2f)%n", 
                            major, students.size(), majorAvgGpa);
                    });
                
                System.out.println("\n✅ DỮ LIỆU THỐNG KÊ HOẠT ĐỘNG BÌNH THƯỜNG!");
                
            } else {
                System.out.println("❌ KHÔNG TÌM THẤY SINH VIÊN NÀO!");
                System.out.println("💡 Kiểm tra:");
                System.out.println("   1. Database có dữ liệu không?");
                System.out.println("   2. Kết nối database có thành công không?");
                System.out.println("   3. UserService có hoạt động đúng không?");
            }
            
        } catch (Exception e) {
            System.err.println("❌ LỖI KIỂM TRA THỐNG KÊ: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("\n🔚 KẾT THÚC KIỂM TRA");
    }
    
    private static String getGradeClassification(double gpa) {
        if (gpa >= 3.6) return "Giỏi";
        else if (gpa >= 3.2) return "Khá";
        else if (gpa >= 2.5) return "Trung bình";
        else if (gpa >= 2.0) return "Yếu";
        else return "Kém";
    }
}
