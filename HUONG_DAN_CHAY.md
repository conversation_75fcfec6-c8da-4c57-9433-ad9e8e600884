# 🎓 Hướng dẫn chạy Student Management System

## 🚀 Cách chạy ứng dụng

### 1. **Chạy launcher chính (Khuyến nghị)**
```bash
run.bat
```
- Hiển thị menu chọn vai trò
- Tự động xử lý compilation và JavaFX
- Dễ sử dụng nhất

### 2. **Chạy trực tiếp theo vai trò**

#### 🔧 Admin Dashboard
```bash
run-admin.bat
```
- Truy cập trực tiếp vào dashboard admin
- Auto-login với tài khoản: `admin/admin123`
- Quản lý toàn bộ hệ thống

#### 👨‍🏫 Teacher Dashboard  
```bash
run-teacher.bat
```
- Truy cập trực tiếp vào dashboard giảng viên
- Auto-login với tài khoản: `teacher/teacher123`
- Quản lý giảng dạy và lịch học

#### 🎓 Student Dashboard
```bash
run-student.bat
```
- <PERSON><PERSON><PERSON> cập trực tiếp vào dashboard sinh viên
- Auto-login với tài kho<PERSON>n: `student/student123`
- Học tập và đăng ký môn học

#### 🔑 Login System
```bash
run-login.bat
```
- Hệ thống đăng nhập tùy chỉnh
- Nhập tài khoản và mật khẩu riêng
- Chọn vai trò sau khi đăng nhập

## 📋 Tài khoản mặc định

| Vai trò | Username | Password | Mô tả |
|---------|----------|----------|-------|
| 🔧 Admin | `admin` | `admin123` | Quản trị viên hệ thống |
| 👨‍🏫 Teacher | `teacher` | `teacher123` | Giảng viên |
| 🎓 Student | `student` | `student123` | Sinh viên |

## 🔧 Yêu cầu hệ thống

- ✅ **Java 17+** (đã cài đặt và trong PATH)
- ✅ **Maven** (tự động tải dependencies)
- ✅ **JavaFX** (tự động xử lý qua Maven)

## 📁 Cấu trúc file

### Scripts chạy ứng dụng
- `run.bat` - Launcher chính với menu
- `run-admin.bat` - Chạy Admin Dashboard
- `run-teacher.bat` - Chạy Teacher Dashboard  
- `run-student.bat` - Chạy Student Dashboard
- `run-login.bat` - Chạy Login System

### Java Launchers
- `AdminLauncher.java` - Launcher cho Admin
- `TeacherLauncher.java` - Launcher cho Teacher
- `StudentLauncher.java` - Launcher cho Student
- `MainApplication.java` - Login system chính

## 🎯 Tính năng mỗi launcher

### 🔧 AdminLauncher
- ✅ Auto-login as Administrator
- ✅ Direct access to admin functions
- ✅ Full system management
- ✅ User management, reports, statistics

### 👨‍🏫 TeacherLauncher  
- ✅ Auto-login as Teacher
- ✅ Direct access to teaching functions
- ✅ Course management
- ✅ Schedule management, grades

### 🎓 StudentLauncher
- ✅ Auto-login as Student
- ✅ Direct access to learning functions
- ✅ Course enrollment
- ✅ Schedule viewing, grades

### 🔑 MainApplication (Login)
- ✅ Custom authentication
- ✅ Role-based access
- ✅ Secure login system
- ✅ Session management

## ⚠️ Xử lý lỗi

Nếu gặp lỗi JavaFX runtime, scripts sẽ tự động:
1. Thử JavaFX Maven plugin
2. Thử exec Maven plugin  
3. Thử chạy Java trực tiếp với classpath

## 🎨 Giao diện

Tất cả launcher đều sử dụng:
- ✅ JavaFX với giao diện đẹp mắt
- ✅ Responsive design
- ✅ Màu sắc phân biệt theo vai trò
- ✅ Icons và typography hiện đại

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra Java đã cài đặt: `java -version`
2. Kiểm tra Maven: `mvn -version`
3. Thử chạy `run.bat` trước
4. Kiểm tra log trong console
