<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.example.doancuoikyjava.controller.StudentMyCoursesController">
   <top>
      <VBox styleClass="header-section">
         <children>
            <HBox alignment="CENTER_LEFT" spacing="20.0" style="-fx-background-color: linear-gradient(to right, #667eea, #764ba2); -fx-padding: 20;">
               <children>
                  <Button fx:id="backButton" onAction="#goBack" text="← Quay lại" 
                          style="-fx-background-color: rgba(255,255,255,0.2); -fx-text-fill: white; -fx-border-color: white; -fx-border-radius: 5; -fx-background-radius: 5; -fx-font-weight: bold;" />
                  <Label fx:id="welcomeLabel" text="📚 Môn học của tôi" 
                         style="-fx-text-fill: white; -fx-font-size: 24px; -fx-font-weight: bold;" />
               </children>
            </HBox>
         </children>
      </VBox>
   </top>
   <center>
      <VBox spacing="20.0" style="-fx-background-color: linear-gradient(to bottom, #f5f7fa, #c3cfe2);">
         <children>
            <!-- Statistics Cards -->
            <HBox spacing="20.0">
               <children>
                  <VBox spacing="10.0" style="-fx-background-color: white; -fx-background-radius: 15; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2); -fx-min-width: 200;">
                     <children>
                        <Label text="📚 Tổng môn học" style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" />
                        <Label fx:id="totalCoursesLabel" text="0" style="-fx-font-size: 32px; -fx-font-weight: bold; -fx-text-fill: #3498db;" />
                     </children>
                     <padding>
                        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                     </padding>
                  </VBox>
                  
                  <VBox spacing="10.0" style="-fx-background-color: white; -fx-background-radius: 15; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2); -fx-min-width: 200;">
                     <children>
                        <Label text="🎯 Tổng tín chỉ" style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" />
                        <Label fx:id="totalCreditsLabel" text="0" style="-fx-font-size: 32px; -fx-font-weight: bold; -fx-text-fill: #27ae60;" />
                     </children>
                     <padding>
                        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                     </padding>
                  </VBox>
                  
                  <VBox spacing="10.0" style="-fx-background-color: white; -fx-background-radius: 15; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2); -fx-min-width: 200;">
                     <children>
                        <Label text="📊 Điểm TB" style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" />
                        <Label fx:id="averageGradeLabel" text="N/A" style="-fx-font-size: 32px; -fx-font-weight: bold; -fx-text-fill: #f39c12;" />
                     </children>
                     <padding>
                        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                     </padding>
                  </VBox>
                  
                  <VBox spacing="10.0" style="-fx-background-color: white; -fx-background-radius: 15; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2); -fx-min-width: 200;">
                     <children>
                        <Label text="🏆 GPA" style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" />
                        <Label fx:id="gpaLabel" text="0.00" style="-fx-font-size: 32px; -fx-font-weight: bold; -fx-text-fill: #e74c3c;" />
                     </children>
                     <padding>
                        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                     </padding>
                  </VBox>
               </children>
            </HBox>
            
            <!-- Control Panel -->
            <VBox spacing="20.0" style="-fx-background-color: white; -fx-background-radius: 15; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);">
               <children>
                  <Label text="🔧 Bộ lọc và tìm kiếm" style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" />
                  
                  <HBox spacing="20.0" alignment="CENTER_LEFT">
                     <children>
                        <VBox spacing="5.0">
                           <children>
                              <Label text="Trạng thái:" style="-fx-font-weight: bold; -fx-text-fill: #34495e;" />
                              <ComboBox fx:id="statusFilterComboBox" prefWidth="150.0" 
                                        style="-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-radius: 8; -fx-background-radius: 8;" />
                           </children>
                        </VBox>
                        
                        <VBox spacing="5.0">
                           <children>
                              <Label text="Học kỳ:" style="-fx-font-weight: bold; -fx-text-fill: #34495e;" />
                              <ComboBox fx:id="semesterFilterComboBox" prefWidth="150.0" 
                                        style="-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-radius: 8; -fx-background-radius: 8;" />
                           </children>
                        </VBox>
                        
                        <VBox spacing="5.0" HBox.hgrow="ALWAYS">
                           <children>
                              <Label text="Tìm kiếm:" style="-fx-font-weight: bold; -fx-text-fill: #34495e;" />
                              <TextField fx:id="searchField" promptText="Tìm theo tên môn học, mã môn hoặc giáo viên..." 
                                         style="-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-radius: 8; -fx-background-radius: 8;" />
                           </children>
                        </VBox>
                     </children>
                  </HBox>
                  
                  <HBox spacing="15.0" alignment="CENTER_LEFT">
                     <children>
                        <Button fx:id="refreshBtn" onAction="#refreshData" text="🔄 Làm mới" 
                                style="-fx-background-color: linear-gradient(to bottom, #27ae60, #229954); -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 8; -fx-padding: 10 20;" />
                        <Button fx:id="exportBtn" onAction="#exportMyCourses" text="📤 Xuất danh sách" 
                                style="-fx-background-color: linear-gradient(to bottom, #f39c12, #e67e22); -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 8; -fx-padding: 10 20;" />
                     </children>
                  </HBox>
               </children>
               <padding>
                  <Insets bottom="25.0" left="25.0" right="25.0" top="25.0" />
               </padding>
            </VBox>
            
            <!-- My Courses Table -->
            <VBox spacing="15.0" VBox.vgrow="ALWAYS" style="-fx-background-color: white; -fx-background-radius: 15; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);">
               <children>
                  <Label text="📋 Danh sách môn học của tôi" style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" />
                  
                  <TableView fx:id="myCoursesTableView" VBox.vgrow="ALWAYS" style="-fx-background-color: transparent;">
                     <columns>
                        <TableColumn fx:id="courseIdColumn" prefWidth="100.0" text="Mã môn" />
                        <TableColumn fx:id="courseNameColumn" prefWidth="200.0" text="Tên môn học" />
                        <TableColumn fx:id="creditsColumn" prefWidth="80.0" text="Tín chỉ" />
                        <TableColumn fx:id="teacherColumn" prefWidth="150.0" text="Giáo viên" />
                        <TableColumn fx:id="scheduleColumn" prefWidth="150.0" text="Giờ học" />
                        <TableColumn fx:id="gradeColumn" prefWidth="120.0" text="Điểm" />
                        <TableColumn fx:id="statusColumn" prefWidth="100.0" text="Trạng thái" />
                        <TableColumn fx:id="actionsColumn" prefWidth="120.0" text="Thao tác" />
                     </columns>
                  </TableView>
               </children>
               <padding>
                  <Insets bottom="25.0" left="25.0" right="25.0" top="25.0" />
               </padding>
            </VBox>
         </children>
         <padding>
            <Insets bottom="25.0" left="25.0" right="25.0" top="25.0" />
         </padding>
      </VBox>
   </center>
</BorderPane>
