package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.*;
import com.example.doancuoikyjava.service.*;
import com.example.doancuoikyjava.util.SceneManager;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.HBox;

import java.io.IOException;
import java.net.URL;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.stream.Collectors;

public class StudentMyCoursesController implements Initializable {
    
    @FXML private Label welcomeLabel;
    @FXML private Button backButton;
    @FXML private Button refreshBtn;
    @FXML private Button exportBtn;
    
    @FXML private Label totalCoursesLabel;
    @FXML private Label totalCreditsLabel;
    @FXML private Label averageGradeLabel;
    @FXML private Label gpaLabel;
    
    @FXML private ComboBox<String> statusFilterComboBox;
    @FXML private ComboBox<String> semesterFilterComboBox;
    @FXML private TextField searchField;
    
    @FXML private TableView<CourseInfo> myCoursesTableView;
    @FXML private TableColumn<CourseInfo, String> courseIdColumn;
    @FXML private TableColumn<CourseInfo, String> courseNameColumn;
    @FXML private TableColumn<CourseInfo, Integer> creditsColumn;
    @FXML private TableColumn<CourseInfo, String> teacherColumn;
    @FXML private TableColumn<CourseInfo, String> scheduleColumn;
    @FXML private TableColumn<CourseInfo, String> gradeColumn;
    @FXML private TableColumn<CourseInfo, String> statusColumn;
    @FXML private TableColumn<CourseInfo, Void> actionsColumn;
    
    private CourseService courseService;
    private ScheduleService scheduleService;
    private GradeService gradeService;
    private UserService userService;
    private Student currentStudent;
    private ObservableList<CourseInfo> allCourses;
    private ObservableList<CourseInfo> filteredCourses;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        courseService = new CourseService();
        scheduleService = new ScheduleService();
        gradeService = new GradeService();
        userService = new UserService();
        allCourses = FXCollections.observableArrayList();
        filteredCourses = FXCollections.observableArrayList();
        
        setupCurrentStudent();
        setupWelcomeMessage();
        setupTableColumns();
        setupFilters();
        loadMyCourses();
    }
    
    private void setupCurrentStudent() {
        User currentUser = SceneManager.getCurrentUser();
        if (currentUser instanceof Student) {
            currentStudent = (Student) currentUser;
        }
    }
    
    private void setupWelcomeMessage() {
        if (currentStudent != null) {
            welcomeLabel.setText("Môn học của tôi - " + currentStudent.getFullName());
        }
    }
    
    private void setupTableColumns() {
        courseIdColumn.setCellValueFactory(new PropertyValueFactory<>("courseId"));
        courseNameColumn.setCellValueFactory(new PropertyValueFactory<>("courseName"));
        creditsColumn.setCellValueFactory(new PropertyValueFactory<>("credits"));
        teacherColumn.setCellValueFactory(new PropertyValueFactory<>("teacherName"));
        scheduleColumn.setCellValueFactory(cellData -> {
            CourseInfo courseInfo = cellData.getValue();
            return new SimpleStringProperty(extractTimeFromSchedule(courseInfo));
        });
        gradeColumn.setCellValueFactory(new PropertyValueFactory<>("gradeDisplay"));
        statusColumn.setCellValueFactory(new PropertyValueFactory<>("status"));
        
        // Actions column
        actionsColumn.setCellFactory(param -> new TableCell<CourseInfo, Void>() {
            private final Button detailsBtn = new Button("Chi tiết");
            private final Button unenrollBtn = new Button("Hủy ĐK");
            private final HBox buttonBox = new HBox(5);
            
            {
                detailsBtn.setStyle("-fx-background-color: #2196F3; -fx-text-fill: white; -fx-font-size: 10px;");
                unenrollBtn.setStyle("-fx-background-color: #f44336; -fx-text-fill: white; -fx-font-size: 10px;");
                
                detailsBtn.setOnAction(e -> {
                    CourseInfo courseInfo = getTableView().getItems().get(getIndex());
                    showCourseDetails(courseInfo);
                });
                
                unenrollBtn.setOnAction(e -> {
                    CourseInfo courseInfo = getTableView().getItems().get(getIndex());
                    unenrollFromCourse(courseInfo);
                });
                
                buttonBox.getChildren().addAll(detailsBtn, unenrollBtn);
            }
            
            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    CourseInfo courseInfo = getTableView().getItems().get(getIndex());
                    // Chỉ cho phép hủy đăng ký nếu chưa có điểm
                    unenrollBtn.setDisable(courseInfo.hasGrade());
                    setGraphic(buttonBox);
                }
            }
        });
        
        myCoursesTableView.setItems(filteredCourses);
    }
    
    private void setupFilters() {
        // Status filter
        statusFilterComboBox.getItems().addAll(
            "Tất cả trạng thái", "Đang học", "Đã có điểm", "Chưa có điểm"
        );
        statusFilterComboBox.setValue("Tất cả trạng thái");
        
        // Semester filter
        semesterFilterComboBox.getItems().addAll(
            "Tất cả học kỳ", "Học kỳ 1", "Học kỳ 2", "Học kỳ hè"
        );
        semesterFilterComboBox.setValue("Tất cả học kỳ");
        
        // Search functionality
        searchField.textProperty().addListener((observable, oldValue, newValue) -> filterCourses());
        statusFilterComboBox.setOnAction(e -> filterCourses());
        semesterFilterComboBox.setOnAction(e -> filterCourses());
    }
    
    private void loadMyCourses() {
        if (currentStudent == null) return;
        
        // Get all courses
        List<Course> allAvailableCourses = courseService.getAllCourses();
        
        // Filter courses that student is enrolled in
        List<Course> enrolledCourses = allAvailableCourses.stream()
                .filter(course -> course.getEnrolledStudents() != null && 
                                course.getEnrolledStudents().contains(currentStudent.getStudentId()))
                .collect(Collectors.toList());
        
        // Get grades for student
        List<Grade> studentGrades = gradeService.getGradesByStudent(currentStudent.getStudentId());
        
        // Create CourseInfo objects
        List<CourseInfo> courseInfos = enrolledCourses.stream()
                .map(course -> {
                    CourseInfo info = new CourseInfo(course);
                    
                    // Find grade for this course
                    Optional<Grade> grade = studentGrades.stream()
                            .filter(g -> g.getCourseId().equals(course.getCourseId()))
                            .findFirst();
                    
                    if (grade.isPresent()) {
                        info.setGrade(grade.get());
                        info.setStatus("Đã có điểm");
                    } else {
                        info.setStatus("Đang học");
                    }
                    
                    return info;
                })
                .collect(Collectors.toList());
        
        allCourses.setAll(courseInfos);
        
        filterCourses();
        updateStatistics();
    }
    
    private void filterCourses() {
        String searchText = searchField.getText().toLowerCase();
        String selectedStatus = statusFilterComboBox.getValue();
        String selectedSemester = semesterFilterComboBox.getValue();
        
        List<CourseInfo> filtered = allCourses.stream()
                .filter(courseInfo -> {
                    boolean matchesSearch = searchText.isEmpty() || 
                            courseInfo.getCourseName().toLowerCase().contains(searchText) ||
                            courseInfo.getCourseId().toLowerCase().contains(searchText) ||
                            courseInfo.getTeacherName().toLowerCase().contains(searchText);
                    
                    boolean matchesStatus = selectedStatus == null || selectedStatus.equals("Tất cả trạng thái") ||
                            courseInfo.getStatus().equals(selectedStatus) ||
                            (selectedStatus.equals("Chưa có điểm") && !courseInfo.hasGrade());
                    
                    // For now, we don't have semester info in Course model
                    boolean matchesSemester = selectedSemester == null || 
                            selectedSemester.equals("Tất cả học kỳ");
                    
                    return matchesSearch && matchesStatus && matchesSemester;
                })
                .collect(Collectors.toList());
        
        filteredCourses.setAll(filtered);
    }
    
    private void updateStatistics() {
        if (currentStudent == null) return;
        
        totalCoursesLabel.setText(String.valueOf(allCourses.size()));
        
        int totalCredits = allCourses.stream()
                .mapToInt(CourseInfo::getCredits)
                .sum();
        totalCreditsLabel.setText(String.valueOf(totalCredits));
        
        // Calculate average grade for courses with grades
        List<CourseInfo> coursesWithGrades = allCourses.stream()
                .filter(CourseInfo::hasGrade)
                .collect(Collectors.toList());
        
        if (!coursesWithGrades.isEmpty()) {
            double avgGrade = coursesWithGrades.stream()
                    .mapToDouble(c -> c.getGrade().getScore())
                    .average()
                    .orElse(0.0);
            averageGradeLabel.setText(String.format("%.2f", avgGrade));
        } else {
            averageGradeLabel.setText("N/A");
        }
        
        // Display current GPA
        gpaLabel.setText(String.format("%.2f", currentStudent.getGpa()));
    }
    
    private void showCourseDetails(CourseInfo courseInfo) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Chi tiết môn học");
        alert.setHeaderText(courseInfo.getCourseName() + " (" + courseInfo.getCourseId() + ")");
        
        StringBuilder content = new StringBuilder();
        content.append("📚 Tên môn học: ").append(courseInfo.getCourseName()).append("\n");
        content.append("🆔 Mã môn học: ").append(courseInfo.getCourseId()).append("\n");
        content.append("📖 Mô tả: ").append(courseInfo.getDescription()).append("\n");
        content.append("🎯 Số tín chỉ: ").append(courseInfo.getCredits()).append("\n");
        content.append("👨‍🏫 Giáo viên: ").append(courseInfo.getTeacherName()).append("\n");
        content.append("📅 Lịch học: ").append(courseInfo.getSchedule()).append("\n");
        content.append("🏫 Phòng học: ").append(courseInfo.getClassroom()).append("\n");
        content.append("📊 Trạng thái: ").append(courseInfo.getStatus()).append("\n");
        
        if (courseInfo.hasGrade()) {
            Grade grade = courseInfo.getGrade();
            content.append("\n📝 THÔNG TIN ĐIỂM:\n");
            content.append("Điểm số: ").append(grade.getScore()).append("\n");
            content.append("Điểm chữ: ").append(grade.getLetterGrade()).append("\n");
            content.append("Điểm hệ số: ").append(grade.getGradePoint()).append("\n");
        } else {
            content.append("\n📝 Chưa có điểm cho môn học này.\n");
        }
        
        TextArea textArea = new TextArea(content.toString());
        textArea.setEditable(false);
        textArea.setWrapText(true);
        textArea.setPrefSize(500, 400);
        
        alert.getDialogPane().setContent(textArea);
        alert.showAndWait();
    }
    
    private void unenrollFromCourse(CourseInfo courseInfo) {
        if (courseInfo.hasGrade()) {
            showAlert("Không thể hủy", "Không thể hủy đăng ký môn học đã có điểm!");
            return;
        }
        
        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("Xác nhận hủy đăng ký");
        confirmAlert.setHeaderText("Hủy đăng ký môn học");
        confirmAlert.setContentText("Bạn có chắc chắn muốn hủy đăng ký môn học \"" + 
                                   courseInfo.getCourseName() + "\"?");
        
        Optional<ButtonType> result = confirmAlert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            try {
                boolean success = courseService.unenrollStudent(courseInfo.getCourseId(), currentStudent.getStudentId());
                
                if (success) {
                    showAlert("Thành công", "Đã hủy đăng ký môn học thành công!");
                    loadMyCourses(); // Reload data
                } else {
                    showAlert("Lỗi", "Không thể hủy đăng ký môn học. Vui lòng thử lại!");
                }
            } catch (Exception e) {
                showAlert("Lỗi", "Có lỗi xảy ra: " + e.getMessage());
            }
        }
    }
    
    @FXML
    private void goBack() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/student-dashboard.fxml", 
                                   "Sinh viên - " + SceneManager.getCurrentUser().getFullName());
        } catch (IOException e) {
            showAlert("Lỗi", "Không thể quay lại trang chính: " + e.getMessage());
        }
    }
    
    @FXML
    private void refreshData() {
        loadMyCourses();
        showAlert("Thành công", "Dữ liệu đã được làm mới!");
    }
    
    @FXML
    private void exportMyCourses() {
        if (filteredCourses.isEmpty()) {
            showAlert("Thông báo", "Không có môn học để xuất!");
            return;
        }
        
        StringBuilder report = new StringBuilder();
        report.append("DANH SÁCH MÔN HỌC CỦA TÔI\n");
        report.append("===========================\n\n");
        report.append("Sinh viên: ").append(currentStudent.getFullName()).append("\n");
        report.append("MSSV: ").append(currentStudent.getStudentId()).append("\n");
        report.append("Lớp: ").append(currentStudent.getClassName()).append("\n");
        report.append("Ngày xuất: ").append(LocalDate.now()).append("\n\n");
        
        report.append("THỐNG KÊ TỔNG QUAN:\n");
        report.append("===================\n");
        report.append("Tổng số môn học: ").append(allCourses.size()).append("\n");
        report.append("Tổng số tín chỉ: ").append(
            allCourses.stream().mapToInt(CourseInfo::getCredits).sum()
        ).append("\n");
        report.append("GPA hiện tại: ").append(String.format("%.2f", currentStudent.getGpa())).append("\n\n");
        
        report.append("DANH SÁCH CHI TIẾT:\n");
        report.append("===================\n");
        
        for (CourseInfo courseInfo : filteredCourses) {
            report.append("📚 ").append(courseInfo.getCourseName()).append(" (").append(courseInfo.getCourseId()).append(")\n");
            report.append("   👨‍🏫 Giáo viên: ").append(courseInfo.getTeacherName()).append("\n");
            report.append("   📅 Lịch học: ").append(courseInfo.getSchedule()).append("\n");
            report.append("   🎯 Tín chỉ: ").append(courseInfo.getCredits()).append("\n");
            report.append("   📊 Trạng thái: ").append(courseInfo.getStatus()).append("\n");
            
            if (courseInfo.hasGrade()) {
                Grade grade = courseInfo.getGrade();
                report.append("   📝 Điểm: ").append(grade.getScore()).append(" (").append(grade.getLetterGrade()).append(")\n");
            } else {
                report.append("   📝 Điểm: Chưa có\n");
            }
            report.append("\n");
        }
        
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Xuất danh sách môn học");
        alert.setHeaderText("Danh sách môn học của tôi");
        
        TextArea textArea = new TextArea(report.toString());
        textArea.setEditable(false);
        textArea.setWrapText(true);
        textArea.setPrefSize(700, 600);
        
        alert.getDialogPane().setContent(textArea);
        alert.showAndWait();
    }
    
    /**
     * Extract time information from course schedule
     */
    private String extractTimeFromSchedule(CourseInfo courseInfo) {
        try {
            // Try to get detailed schedule information first
            List<CourseSchedule> schedules = scheduleService.getSchedulesByCourse(courseInfo.getCourseId());
            if (!schedules.isEmpty()) {
                // Get the first schedule's time range
                return schedules.get(0).getTimeRange();
            }

            // Fallback to parsing from course.getSchedule() string
            String schedule = courseInfo.getSchedule();
            if (schedule != null && !schedule.trim().isEmpty()) {
                // Parse format like "Thứ 2, 4, 6 - 7:30-9:30"
                if (schedule.contains("-")) {
                    String[] parts = schedule.split("-");
                    if (parts.length >= 2) {
                        // Look for time pattern like "7:30-9:30"
                        String timePart = parts[parts.length - 1].trim();
                        if (timePart.matches(".*\\d{1,2}:\\d{2}.*")) {
                            return timePart;
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("❌ Error extracting time from schedule: " + e.getMessage());
        }

        return "Chưa xếp";
    }

    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    // Inner class to hold course information with grade
    public static class CourseInfo {
        private Course course;
        private Grade grade;
        private String status;
        
        public CourseInfo(Course course) {
            this.course = course;
        }
        
        // Getters for table columns
        public String getCourseId() { return course.getCourseId(); }
        public String getCourseName() { return course.getCourseName(); }
        public int getCredits() { return course.getCredits(); }
        public String getTeacherName() { return course.getTeacherName(); }
        public String getSchedule() { return course.getSchedule(); }
        public String getClassroom() { return course.getClassroom(); }
        public String getDescription() { return course.getDescription(); }
        
        public String getGradeDisplay() {
            return hasGrade() ? String.format("%.2f (%s)", grade.getScore(), grade.getLetterGrade()) : "Chưa có";
        }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public Grade getGrade() { return grade; }
        public void setGrade(Grade grade) { this.grade = grade; }
        
        public boolean hasGrade() { return grade != null; }
    }
}
