/* Light Theme Styles - Original Clean Version */

.root {
    /* Light Theme Color Variables */
    -fx-primary: #2196F3;
    -fx-secondary: #FFC107;
    -fx-success: #4CAF50;
    -fx-warning: #FF9800;
    -fx-error: #F44336;
    -fx-background: #FAFAFA;
    -fx-surface: #FFFFFF;
    -fx-text: #212121;
    -fx-text-secondary: #757575;
    -fx-border: #E0E0E0;
    
    /* Apply background */
    -fx-background-color: #FAFAFA;
}

/* Main Container Backgrounds */
.main-container {
    -fx-background-color: #FAFAFA;
}

.content-area {
    -fx-background-color: #FFFFFF;
}

/* Sidebar Light Theme */
.sidebar {
    -fx-background-color: #FFFFFF;
    -fx-border-color: #E0E0E0;
    -fx-border-width: 0 1px 0 0;
}

.sidebar-button {
    -fx-background-color: transparent;
    -fx-text-fill: #212121;
}

.sidebar-button:hover {
    -fx-background-color: rgba(33,150,243,0.08);
}

.sidebar-button.active {
    -fx-background-color: #2196F3;
    -fx-text-fill: #FFFFFF;
}

/* Text Field Light Theme */
.text-field {
    -fx-background-color: #FFFFFF;
    -fx-border-color: #E0E0E0;
    -fx-text-fill: #212121;
    -fx-prompt-text-fill: #757575;
}

.text-field:focused {
    -fx-border-color: #2196F3;
}

/* ComboBox Light Theme */
.combo-box {
    -fx-background-color: #FFFFFF;
    -fx-border-color: #E0E0E0;
    -fx-text-fill: #212121;
}

.combo-box:focused {
    -fx-border-color: #2196F3;
}

/* Card Light Theme */
.enhanced-card {
    -fx-background-color: #FFFFFF;
    -fx-border-color: #E0E0E0;
    -fx-border-width: 1px;
}

.stat-card {
    -fx-background-color: #2196F3;
}

/* Table Light Theme */
.enhanced-table {
    -fx-background-color: #FFFFFF;
    -fx-border-color: #E0E0E0;
}

.enhanced-table .column-header {
    -fx-background-color: #F5F5F5;
    -fx-text-fill: #212121;
    -fx-border-color: #E0E0E0;
}

.enhanced-table .table-row-cell {
    -fx-background-color: #FFFFFF;
    -fx-text-fill: #212121;
}

.enhanced-table .table-row-cell:odd {
    -fx-background-color: #FAFAFA;
}

.enhanced-table .table-row-cell:hover {
    -fx-background-color: rgba(33,150,243,0.08);
}

.enhanced-table .table-row-cell:selected {
    -fx-background-color: rgba(33,150,243,0.12);
}

/* Button Light Theme */
.button {
    -fx-background-color: #FFFFFF;
    -fx-border-color: #E0E0E0;
    -fx-text-fill: #212121;
}

.button:hover {
    -fx-background-color: #F5F5F5;
}

.button:pressed {
    -fx-background-color: #EEEEEE;
}

/* Primary Button */
.button.btn-primary {
    -fx-background-color: #2196F3;
    -fx-text-fill: #FFFFFF;
    -fx-border-color: transparent;
}

.button.btn-primary:hover {
    -fx-background-color: #1976D2;
}

/* Success Button */
.button.btn-success {
    -fx-background-color: #4CAF50;
    -fx-text-fill: #FFFFFF;
    -fx-border-color: transparent;
}

.button.btn-success:hover {
    -fx-background-color: #388E3C;
}

/* Warning Button */
.button.btn-warning {
    -fx-background-color: #FF9800;
    -fx-text-fill: #FFFFFF;
    -fx-border-color: transparent;
}

.button.btn-warning:hover {
    -fx-background-color: #F57C00;
}

/* Error Button */
.button.btn-danger {
    -fx-background-color: #F44336;
    -fx-text-fill: #FFFFFF;
    -fx-border-color: transparent;
}

.button.btn-danger:hover {
    -fx-background-color: #D32F2F;
}

/* Label Light Theme */
.label {
    -fx-text-fill: #212121;
}

.label.label-title {
    -fx-text-fill: #212121;
    -fx-font-weight: bold;
}

.label.label-subtitle {
    -fx-text-fill: #757575;
}

.label.label-secondary {
    -fx-text-fill: #757575;
}

/* Header Light Theme */
.header-container {
    -fx-background-color: #FFFFFF;
    -fx-border-color: #E0E0E0;
    -fx-border-width: 0 0 1px 0;
}

.header-title {
    -fx-text-fill: #212121;
}

.header-subtitle {
    -fx-text-fill: #757575;
}

/* Menu Light Theme */
.menu-bar {
    -fx-background-color: #FFFFFF;
    -fx-border-color: #E0E0E0;
}

.menu {
    -fx-text-fill: #212121;
}

.menu-item {
    -fx-background-color: #FFFFFF;
    -fx-text-fill: #212121;
}

.menu-item:hover {
    -fx-background-color: rgba(33,150,243,0.08);
}

/* Dialog Light Theme */
.dialog-pane {
    -fx-background-color: #FFFFFF;
}

.dialog-pane .header-panel {
    -fx-background-color: #F5F5F5;
    -fx-text-fill: #212121;
}

.dialog-pane .content {
    -fx-background-color: #FFFFFF;
    -fx-text-fill: #212121;
}

/* Progress Bar Light Theme */
.progress-bar {
    -fx-background-color: #E0E0E0;
}

.progress-bar .bar {
    -fx-background-color: #2196F3;
}

/* Separator Light Theme */
.separator .line {
    -fx-border-color: #E0E0E0;
}

/* Tooltip Light Theme */
.tooltip {
    -fx-background-color: rgba(33,33,33,0.9);
    -fx-text-fill: #FFFFFF;
    -fx-background-radius: 4px;
    -fx-font-size: 12px;
}

/* ScrollPane Light Theme */
.scroll-pane {
    -fx-background-color: #FFFFFF;
}

.scroll-pane .corner {
    -fx-background-color: #FFFFFF;
}

/* ListView Light Theme */
.list-view {
    -fx-background-color: #FFFFFF;
    -fx-border-color: #E0E0E0;
}

.list-cell {
    -fx-background-color: #FFFFFF;
    -fx-text-fill: #212121;
}

.list-cell:hover {
    -fx-background-color: rgba(33,150,243,0.08);
}

.list-cell:selected {
    -fx-background-color: rgba(33,150,243,0.12);
}
