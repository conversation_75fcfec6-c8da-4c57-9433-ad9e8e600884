package com.example.doancuoikyjava.test;

import com.example.doancuoikyjava.model.Student;
import com.example.doancuoikyjava.model.Teacher;
import com.example.doancuoikyjava.model.User;
import com.example.doancuoikyjava.service.UserService;

import java.time.LocalDate;

public class TestAddUser {
    
    public static void main(String[] args) {
        System.out.println("🧪 Testing Add User Functionality...");
        
        UserService userService = new UserService();
        
        // Test 1: Thêm sinh viên
        System.out.println("\n📚 Test 1: Thêm sinh viên mới");
        testAddStudent(userService);
        
        // Test 2: Thêm giáo viên
        System.out.println("\n👨‍🏫 Test 2: Thêm giáo viên mới");
        testAddTeacher(userService);
        
        // Test 3: Kiểm tra duplicate username
        System.out.println("\n🔍 Test 3: <PERSON><PERSON><PERSON> tra duplicate username");
        testDuplicateUsername(userService);
        
        System.out.println("\n✅ Test completed!");
    }
    
    private static void testAddStudent(UserService userService) {
        try {
            Student student = new Student();
            
            // Generate new ID
            String studentId = userService.generateNextUserId(User.UserRole.STUDENT);
            student.setUserId(studentId);
            student.setStudentId(studentId);
            
            // Set basic info
            student.setUsername("test_student_" + System.currentTimeMillis());
            student.setPassword("password123");
            student.setFullName("Nguyễn Văn Test");
            student.setEmail("<EMAIL>");
            student.setPhone("0123456789");
            student.setAddress("123 Test Street");
            student.setDateOfBirth(LocalDate.of(2000, 1, 1));
            student.setRole(User.UserRole.STUDENT);
            
            // Set student specific info
            student.setClassName("CNTT2024");
            student.setMajor("Công nghệ thông tin");
            student.setYear(2024);
            student.setGpa(3.5);
            
            System.out.println("📝 Thông tin sinh viên:");
            System.out.println("  - ID: " + student.getStudentId());
            System.out.println("  - Username: " + student.getUsername());
            System.out.println("  - Full Name: " + student.getFullName());
            System.out.println("  - Class: " + student.getClassName());
            
            boolean result = userService.addUser(student);
            
            if (result) {
                System.out.println("✅ Thêm sinh viên thành công!");
            } else {
                System.out.println("❌ Thêm sinh viên thất bại!");
                
                // Check specific reasons
                if (userService.isUsernameExists(student.getUsername())) {
                    System.out.println("   Lý do: Username đã tồn tại");
                }
                if (userService.isUserIdExists(student.getUserId())) {
                    System.out.println("   Lý do: User ID đã tồn tại");
                }
            }
            
        } catch (Exception e) {
            System.out.println("❌ Exception khi thêm sinh viên: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testAddTeacher(UserService userService) {
        try {
            Teacher teacher = new Teacher();
            
            // Generate new ID
            String teacherId = userService.generateNextUserId(User.UserRole.TEACHER);
            teacher.setUserId(teacherId);
            teacher.setTeacherId(teacherId);
            
            // Set basic info
            teacher.setUsername("test_teacher_" + System.currentTimeMillis());
            teacher.setPassword("password123");
            teacher.setFullName("Trần Thị Test");
            teacher.setEmail("<EMAIL>");
            teacher.setPhone("0987654321");
            teacher.setAddress("456 Test Avenue");
            teacher.setDateOfBirth(LocalDate.of(1980, 5, 15));
            teacher.setRole(User.UserRole.TEACHER);
            
            // Set teacher specific info
            teacher.setDepartment("Khoa CNTT");
            teacher.setPosition("Giảng viên");
            teacher.setSalary(15000000.0);
            teacher.setQualification("Thạc sĩ");
            teacher.setExperienceYears(5);
            
            System.out.println("📝 Thông tin giáo viên:");
            System.out.println("  - ID: " + teacher.getTeacherId());
            System.out.println("  - Username: " + teacher.getUsername());
            System.out.println("  - Full Name: " + teacher.getFullName());
            System.out.println("  - Department: " + teacher.getDepartment());
            
            boolean result = userService.addUser(teacher);
            
            if (result) {
                System.out.println("✅ Thêm giáo viên thành công!");
            } else {
                System.out.println("❌ Thêm giáo viên thất bại!");
                
                // Check specific reasons
                if (userService.isUsernameExists(teacher.getUsername())) {
                    System.out.println("   Lý do: Username đã tồn tại");
                }
                if (userService.isUserIdExists(teacher.getUserId())) {
                    System.out.println("   Lý do: User ID đã tồn tại");
                }
            }
            
        } catch (Exception e) {
            System.out.println("❌ Exception khi thêm giáo viên: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testDuplicateUsername(UserService userService) {
        try {
            Student student = new Student();
            
            // Use existing username
            student.setUserId("STU999");
            student.setStudentId("STU999");
            student.setUsername("student"); // This should already exist
            student.setPassword("password123");
            student.setFullName("Test Duplicate");
            student.setRole(User.UserRole.STUDENT);
            
            System.out.println("📝 Testing duplicate username: " + student.getUsername());
            
            boolean result = userService.addUser(student);
            
            if (result) {
                System.out.println("❌ Unexpected: Thêm user với username duplicate thành công!");
            } else {
                System.out.println("✅ Expected: Thêm user với username duplicate thất bại!");
            }
            
        } catch (Exception e) {
            System.out.println("❌ Exception khi test duplicate: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
