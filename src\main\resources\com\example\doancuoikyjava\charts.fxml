<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.example.doancuoikyjava.controller.ChartController">
   <top>
      <VBox styleClass="header-section">
         <children>
            <HBox alignment="CENTER_LEFT" spacing="20.0" style="-fx-background-color: linear-gradient(to right, #667eea, #764ba2); -fx-padding: 20;">
               <children>
                  <Button fx:id="backButton" onAction="#goBack" text="← Quay lại" 
                          style="-fx-background-color: rgba(255,255,255,0.2); -fx-text-fill: white; -fx-border-color: white; -fx-border-radius: 5; -fx-background-radius: 5; -fx-font-weight: bold;" />
                  <Label fx:id="welcomeLabel" text="📊 Thống kê Biểu đồ" 
                         style="-fx-text-fill: white; -fx-font-size: 24px; -fx-font-weight: bold;" />
               </children>
            </HBox>
         </children>
      </VBox>
   </top>
   <center>
      <VBox spacing="20.0" style="-fx-background-color: linear-gradient(to bottom, #f5f7fa, #c3cfe2);">
         <children>
            <!-- Control Panel -->
            <VBox spacing="20.0" style="-fx-background-color: white; -fx-background-radius: 15; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);">
               <children>
                  <Label text="🎛️ Điều khiển biểu đồ" style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" />
                  
                  <HBox alignment="CENTER_LEFT" spacing="20.0">
                     <children>
                        <Label text="Loại biểu đồ:" style="-fx-font-weight: bold; -fx-text-fill: #34495e; -fx-min-width: 100;" />
                        <ComboBox fx:id="chartTypeComboBox" prefWidth="300.0" 
                                  style="-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-radius: 8; -fx-background-radius: 8;" />
                     </children>
                  </HBox>
                  
                  <HBox alignment="CENTER_LEFT" spacing="15.0">
                     <children>
                        <Button fx:id="generateChartButton" onAction="#generateChart" text="📊 Tạo biểu đồ" 
                                style="-fx-background-color: linear-gradient(to bottom, #3498db, #2980b9); -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 8; -fx-padding: 10 20;" />
                        <Button fx:id="refreshButton" onAction="#refreshData" text="🔄 Làm mới" 
                                style="-fx-background-color: linear-gradient(to bottom, #27ae60, #229954); -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 8; -fx-padding: 10 20;" />
                        <Button onAction="#exportChart" text="📤 Xuất biểu đồ" 
                                style="-fx-background-color: linear-gradient(to bottom, #f39c12, #e67e22); -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 8; -fx-padding: 10 20;" />
                        <Button onAction="#showChartDetails" text="📋 Chi tiết" 
                                style="-fx-background-color: linear-gradient(to bottom, #9b59b6, #8e44ad); -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 8; -fx-padding: 10 20;" />
                     </children>
                  </HBox>
                  
                  <Label fx:id="chartInfoLabel" text="💡 Chọn loại biểu đồ và nhấn 'Tạo biểu đồ' để xem thống kê trực quan" 
                         style="-fx-font-size: 12px; -fx-text-fill: #7f8c8d; -fx-font-style: italic;" wrapText="true" />
               </children>
               <padding>
                  <Insets bottom="25.0" left="25.0" right="25.0" top="25.0" />
               </padding>
            </VBox>
            
            <!-- Chart Display Area -->
            <VBox fx:id="chartContainer" spacing="15.0" VBox.vgrow="ALWAYS" 
                  style="-fx-background-color: white; -fx-background-radius: 15; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2); -fx-min-height: 600;">
               <children>
                  <HBox alignment="CENTER_LEFT" spacing="10.0">
                     <children>
                        <Label text="📊 Biểu đồ thống kê" style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" />
                        <Region HBox.hgrow="ALWAYS" />
                        <Label text="💡 Tip: Click chuột phải vào biểu đồ để xuất file" style="-fx-font-size: 12px; -fx-text-fill: #7f8c8d; -fx-font-style: italic;" />
                     </children>
                  </HBox>
                  
                  <!-- Chart sẽ được thêm vào đây bằng code -->
               </children>
               <padding>
                  <Insets bottom="25.0" left="25.0" right="25.0" top="25.0" />
               </padding>
            </VBox>
         </children>
         <padding>
            <Insets bottom="25.0" left="25.0" right="25.0" top="25.0" />
         </padding>
      </VBox>
   </center>
</BorderPane>
