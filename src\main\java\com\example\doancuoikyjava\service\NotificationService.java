package com.example.doancuoikyjava.service;

import com.example.doancuoikyjava.model.Notification;
import com.example.doancuoikyjava.model.User;
import com.example.doancuoikyjava.util.DatabaseConnection;

import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class NotificationService {
    
    public NotificationService() {
        initializeNotificationTable();
    }
    
    /**
     * Khởi tạo bảng notifications nếu chưa tồn tại
     */
    private void initializeNotificationTable() {
        String createTableSQL = """
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Notifications' AND xtype='U')
            CREATE TABLE Notifications (
                notification_id NVARCHAR(50) PRIMARY KEY,
                title NVARCHAR(255) NOT NULL,
                content NTEXT NOT NULL,
                type NVARCHAR(20) NOT NULL,
                target_audience NVARCHAR(20) NOT NULL,
                priority NVARCHAR(10) NOT NULL,
                created_by NVARCHAR(50) NOT NULL,
                created_at DATETIME NOT NULL,
                expiry_date DATETIME NULL,
                is_active BIT NOT NULL DEFAULT 1,
                attachment_path NVARCHAR(500) NULL,
                view_count INT NOT NULL DEFAULT 0
            )
        """;
        
        try {
            Connection conn = DatabaseConnection.getConnection();
            Statement stmt = conn.createStatement();
            stmt.execute(createTableSQL);
            System.out.println("✅ Notifications table initialized");
        } catch (SQLException e) {
            System.err.println("❌ Error initializing notifications table: " + e.getMessage());
        }
    }
    
    /**
     * Tạo ID mới cho notification
     */
    public String generateNotificationId() {
        String sql = "SELECT COUNT(*) FROM Notifications";
        
        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                int count = rs.getInt(1);
                return "NOTIF" + String.format("%04d", count + 1);
            }
        } catch (SQLException e) {
            System.err.println("❌ Error generating notification ID: " + e.getMessage());
        }
        
        return "NOTIF0001";
    }
    
    /**
     * Thêm thông báo mới
     */
    public boolean addNotification(Notification notification) {
        String sql = """
            INSERT INTO Notifications (notification_id, title, content, type, target_audience, 
                                     priority, created_by, created_at, expiry_date, is_active, 
                                     attachment_path, view_count)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """;
        
        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            
            stmt.setString(1, notification.getNotificationId());
            stmt.setString(2, notification.getTitle());
            stmt.setString(3, notification.getContent());
            stmt.setString(4, notification.getType().toString());
            stmt.setString(5, notification.getTargetAudience().toString());
            stmt.setString(6, notification.getPriority().toString());
            stmt.setString(7, notification.getCreatedBy());
            stmt.setTimestamp(8, Timestamp.valueOf(notification.getCreatedAt()));
            stmt.setTimestamp(9, notification.getExpiryDate() != null ? 
                             Timestamp.valueOf(notification.getExpiryDate()) : null);
            stmt.setBoolean(10, notification.isActive());
            stmt.setString(11, notification.getAttachmentPath());
            stmt.setInt(12, notification.getViewCount());
            
            int result = stmt.executeUpdate();
            System.out.println("✅ Notification added: " + notification.getTitle());
            return result > 0;
            
        } catch (SQLException e) {
            System.err.println("❌ Error adding notification: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Lấy tất cả thông báo
     */
    public List<Notification> getAllNotifications() {
        List<Notification> notifications = new ArrayList<>();
        String sql = "SELECT * FROM Notifications ORDER BY created_at DESC";
        
        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            ResultSet rs = stmt.executeQuery();
            
            while (rs.next()) {
                notifications.add(mapResultSetToNotification(rs));
            }
            
        } catch (SQLException e) {
            System.err.println("❌ Error getting notifications: " + e.getMessage());
        }
        
        return notifications;
    }
    
    /**
     * Lấy thông báo theo đối tượng
     */
    public List<Notification> getNotificationsByTarget(User.UserRole userRole) {
        List<Notification> notifications = new ArrayList<>();
        String sql = """
            SELECT * FROM Notifications 
            WHERE is_active = 1 
            AND (expiry_date IS NULL OR expiry_date > GETDATE())
            AND (target_audience = 'ALL' OR target_audience = ?)
            ORDER BY priority DESC, created_at DESC
        """;
        
        String targetAudience = switch (userRole) {
            case STUDENT -> "STUDENTS";
            case TEACHER -> "TEACHERS";
            default -> "ALL";
        };
        
        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, targetAudience);
            ResultSet rs = stmt.executeQuery();
            
            while (rs.next()) {
                notifications.add(mapResultSetToNotification(rs));
            }
            
        } catch (SQLException e) {
            System.err.println("❌ Error getting notifications by target: " + e.getMessage());
        }
        
        return notifications;
    }
    
    /**
     * Cập nhật thông báo
     */
    public boolean updateNotification(Notification notification) {
        String sql = """
            UPDATE Notifications SET 
            title = ?, content = ?, type = ?, target_audience = ?, priority = ?,
            expiry_date = ?, is_active = ?, attachment_path = ?
            WHERE notification_id = ?
        """;
        
        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            
            stmt.setString(1, notification.getTitle());
            stmt.setString(2, notification.getContent());
            stmt.setString(3, notification.getType().toString());
            stmt.setString(4, notification.getTargetAudience().toString());
            stmt.setString(5, notification.getPriority().toString());
            stmt.setTimestamp(6, notification.getExpiryDate() != null ? 
                             Timestamp.valueOf(notification.getExpiryDate()) : null);
            stmt.setBoolean(7, notification.isActive());
            stmt.setString(8, notification.getAttachmentPath());
            stmt.setString(9, notification.getNotificationId());
            
            int result = stmt.executeUpdate();
            return result > 0;
            
        } catch (SQLException e) {
            System.err.println("❌ Error updating notification: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Xóa thông báo
     */
    public boolean deleteNotification(String notificationId) {
        String sql = "DELETE FROM Notifications WHERE notification_id = ?";
        
        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, notificationId);
            
            int result = stmt.executeUpdate();
            return result > 0;
            
        } catch (SQLException e) {
            System.err.println("❌ Error deleting notification: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Tăng số lượt xem
     */
    public void incrementViewCount(String notificationId) {
        String sql = "UPDATE Notifications SET view_count = view_count + 1 WHERE notification_id = ?";
        
        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, notificationId);
            stmt.executeUpdate();
            
        } catch (SQLException e) {
            System.err.println("❌ Error incrementing view count: " + e.getMessage());
        }
    }
    
    /**
     * Lấy thông báo theo ID
     */
    public Notification getNotificationById(String notificationId) {
        String sql = "SELECT * FROM Notifications WHERE notification_id = ?";
        
        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, notificationId);
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                return mapResultSetToNotification(rs);
            }
            
        } catch (SQLException e) {
            System.err.println("❌ Error getting notification by ID: " + e.getMessage());
        }
        
        return null;
    }
    
    /**
     * Map ResultSet to Notification object
     */
    private Notification mapResultSetToNotification(ResultSet rs) throws SQLException {
        Notification notification = new Notification();
        
        notification.setNotificationId(rs.getString("notification_id"));
        notification.setTitle(rs.getString("title"));
        notification.setContent(rs.getString("content"));
        notification.setType(Notification.NotificationType.valueOf(rs.getString("type")));
        notification.setTargetAudience(Notification.TargetAudience.valueOf(rs.getString("target_audience")));
        notification.setPriority(Notification.Priority.valueOf(rs.getString("priority")));
        notification.setCreatedBy(rs.getString("created_by"));
        notification.setCreatedAt(rs.getTimestamp("created_at").toLocalDateTime());
        
        Timestamp expiryTimestamp = rs.getTimestamp("expiry_date");
        if (expiryTimestamp != null) {
            notification.setExpiryDate(expiryTimestamp.toLocalDateTime());
        }
        
        notification.setActive(rs.getBoolean("is_active"));
        notification.setAttachmentPath(rs.getString("attachment_path"));
        notification.setViewCount(rs.getInt("view_count"));
        
        return notification;
    }
    
    /**
     * Lấy số lượng thông báo chưa đọc cho user
     */
    public int getUnreadNotificationCount(User.UserRole userRole) {
        return getNotificationsByTarget(userRole).size();
    }

    /**
     * Tắt/bật thông báo
     */
    public boolean toggleNotificationStatus(String notificationId) {
        String sql = "UPDATE Notifications SET is_active = CASE WHEN is_active = 1 THEN 0 ELSE 1 END WHERE notification_id = ?";

        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, notificationId);

            int result = stmt.executeUpdate();
            return result > 0;

        } catch (SQLException e) {
            System.err.println("❌ Error toggling notification status: " + e.getMessage());
            return false;
        }
    }
}
