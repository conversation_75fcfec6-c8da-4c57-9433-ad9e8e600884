package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.*;
import com.example.doancuoikyjava.service.*;
import com.example.doancuoikyjava.util.SceneManager;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;

import java.io.IOException;
import java.net.URL;
import java.util.List;
import java.util.ResourceBundle;
import java.util.stream.Collectors;

public class TeacherCoursesController implements Initializable {
    
    @FXML private Label welcomeLabel;
    @FXML private Button backButton;
    @FXML private Button refreshBtn;
    @FXML private Button exportBtn;
    
    @FXML private Label totalCoursesLabel;
    @FXML private Label totalStudentsLabel;
    @FXML private Label totalCreditsLabel;
    
    @FXML private ComboBox<String> semesterFilterComboBox;
    @FXML private TextField searchField;
    
    @FXML private TableView<Course> coursesTableView;
    @FXML private TableColumn<Course, String> courseIdColumn;
    @FXML private TableColumn<Course, String> courseNameColumn;
    @FXML private TableColumn<Course, Integer> creditsColumn;
    @FXML private TableColumn<Course, String> scheduleColumn;
    @FXML private TableColumn<Course, String> classroomColumn;
    @FXML private TableColumn<Course, String> enrolledColumn;
    @FXML private TableColumn<Course, String> maxStudentsColumn;
    @FXML private TableColumn<Course, Void> actionsColumn;
    
    private CourseService courseService;
    private ScheduleService scheduleService;
    private UserService userService;
    private Teacher currentTeacher;
    private ObservableList<Course> allCourses;
    private ObservableList<Course> filteredCourses;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        courseService = new CourseService();
        scheduleService = new ScheduleService();
        userService = new UserService();
        allCourses = FXCollections.observableArrayList();
        filteredCourses = FXCollections.observableArrayList();
        
        setupCurrentTeacher();
        setupWelcomeMessage();
        setupTableColumns();
        setupFilters();
        loadCourses();
    }
    
    private void setupCurrentTeacher() {
        User currentUser = SceneManager.getCurrentUser();
        if (currentUser instanceof Teacher) {
            currentTeacher = (Teacher) currentUser;
        }
    }
    
    private void setupWelcomeMessage() {
        if (currentTeacher != null) {
            welcomeLabel.setText("Môn học giảng dạy - " + currentTeacher.getFullName());
        }
    }
    
    private void setupTableColumns() {
        courseIdColumn.setCellValueFactory(new PropertyValueFactory<>("courseId"));
        courseNameColumn.setCellValueFactory(new PropertyValueFactory<>("courseName"));
        creditsColumn.setCellValueFactory(new PropertyValueFactory<>("credits"));
        scheduleColumn.setCellValueFactory(cellData -> {
            Course course = cellData.getValue();
            return new SimpleStringProperty(extractTimeFromSchedule(course));
        });
        classroomColumn.setCellValueFactory(new PropertyValueFactory<>("classroom"));
        
        enrolledColumn.setCellValueFactory(cellData -> {
            Course course = cellData.getValue();
            int enrolled = course.getEnrolledStudents() != null ? course.getEnrolledStudents().size() : 0;
            return new SimpleStringProperty(String.valueOf(enrolled));
        });
        
        maxStudentsColumn.setCellValueFactory(cellData -> {
            Course course = cellData.getValue();
            return new SimpleStringProperty(String.valueOf(course.getMaxStudents()));
        });
        
        // Actions column
        actionsColumn.setCellFactory(param -> new TableCell<Course, Void>() {
            private final Button detailsBtn = new Button("Chi tiết");
            
            {
                detailsBtn.setStyle("-fx-background-color: #2196F3; -fx-text-fill: white; -fx-font-size: 10px;");
                detailsBtn.setOnAction(e -> {
                    Course course = getTableView().getItems().get(getIndex());
                    showCourseDetails(course);
                });
            }
            
            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(detailsBtn);
                }
            }
        });
        
        coursesTableView.setItems(filteredCourses);
    }
    
    private void setupFilters() {
        // Semester filter
        semesterFilterComboBox.getItems().addAll(
            "Tất cả học kỳ", "Học kỳ 1", "Học kỳ 2", "Học kỳ hè"
        );
        semesterFilterComboBox.setValue("Tất cả học kỳ");
        
        // Search functionality
        searchField.textProperty().addListener((observable, oldValue, newValue) -> filterCourses());
        semesterFilterComboBox.setOnAction(e -> filterCourses());
    }
    
    private void loadCourses() {
        if (currentTeacher == null) return;
        
        List<Course> teacherCourses = courseService.getCoursesByTeacher(currentTeacher.getTeacherId());
        allCourses.setAll(teacherCourses);
        
        filterCourses();
        updateStatistics();
    }
    
    private void filterCourses() {
        String searchText = searchField.getText().toLowerCase();
        String selectedSemester = semesterFilterComboBox.getValue();
        
        List<Course> filtered = allCourses.stream()
                .filter(course -> {
                    boolean matchesSearch = searchText.isEmpty() || 
                            course.getCourseName().toLowerCase().contains(searchText) ||
                            course.getCourseId().toLowerCase().contains(searchText);
                    
                    // For now, we don't have semester info in Course model
                    // This can be extended later
                    boolean matchesSemester = selectedSemester == null || 
                            selectedSemester.equals("Tất cả học kỳ");
                    
                    return matchesSearch && matchesSemester;
                })
                .collect(Collectors.toList());
        
        filteredCourses.setAll(filtered);
    }
    
    private void updateStatistics() {
        if (currentTeacher == null) return;
        
        totalCoursesLabel.setText(String.valueOf(allCourses.size()));
        
        int totalStudents = allCourses.stream()
                .mapToInt(course -> course.getEnrolledStudents() != null ? course.getEnrolledStudents().size() : 0)
                .sum();
        totalStudentsLabel.setText(String.valueOf(totalStudents));
        
        int totalCredits = allCourses.stream()
                .mapToInt(Course::getCredits)
                .sum();
        totalCreditsLabel.setText(String.valueOf(totalCredits));
    }
    
    private void showCourseDetails(Course course) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Chi tiết môn học");
        alert.setHeaderText(course.getCourseName() + " (" + course.getCourseId() + ")");
        
        StringBuilder content = new StringBuilder();
        content.append("📚 Tên môn học: ").append(course.getCourseName()).append("\n");
        content.append("🆔 Mã môn học: ").append(course.getCourseId()).append("\n");
        content.append("📖 Mô tả: ").append(course.getDescription()).append("\n");
        content.append("🎯 Số tín chỉ: ").append(course.getCredits()).append("\n");
        content.append("📅 Lịch học: ").append(course.getSchedule()).append("\n");
        content.append("🏫 Phòng học: ").append(course.getClassroom()).append("\n");
        content.append("👥 Sinh viên đã đăng ký: ").append(
            course.getEnrolledStudents() != null ? course.getEnrolledStudents().size() : 0
        ).append("/").append(course.getMaxStudents()).append("\n");
        
        if (course.getEnrolledStudents() != null && !course.getEnrolledStudents().isEmpty()) {
            content.append("\n📋 Danh sách sinh viên:\n");
            for (String studentId : course.getEnrolledStudents()) {
                User student = userService.getUserById(studentId);
                if (student != null) {
                    content.append("• ").append(student.getFullName()).append(" (").append(studentId).append(")\n");
                }
            }
        }
        
        TextArea textArea = new TextArea(content.toString());
        textArea.setEditable(false);
        textArea.setWrapText(true);
        textArea.setPrefSize(500, 400);
        
        alert.getDialogPane().setContent(textArea);
        alert.showAndWait();
    }
    
    @FXML
    private void goBack() {
        try {
            SceneManager.switchScene("/com/example/doancuoikyjava/teacher-dashboard.fxml", 
                                   "Giáo viên - " + SceneManager.getCurrentUser().getFullName());
        } catch (IOException e) {
            showAlert("Lỗi", "Không thể quay lại trang chính: " + e.getMessage());
        }
    }
    
    @FXML
    private void refreshData() {
        loadCourses();
        showAlert("Thành công", "Dữ liệu đã được làm mới!");
    }
    
    @FXML
    private void searchCourses() {
        filterCourses();
    }
    
    @FXML
    private void exportData() {
        if (filteredCourses.isEmpty()) {
            showAlert("Thông báo", "Không có dữ liệu để xuất!");
            return;
        }
        
        StringBuilder report = new StringBuilder();
        report.append("BÁO CÁO MÔN HỌC GIẢNG DẠY\n");
        report.append("==========================\n\n");
        report.append("Giáo viên: ").append(currentTeacher.getFullName()).append("\n");
        report.append("Ngày xuất: ").append(java.time.LocalDate.now()).append("\n\n");
        
        for (Course course : filteredCourses) {
            report.append("📚 ").append(course.getCourseName()).append(" (").append(course.getCourseId()).append(")\n");
            report.append("   Tín chỉ: ").append(course.getCredits()).append("\n");
            report.append("   Lịch học: ").append(course.getSchedule()).append("\n");
            report.append("   Phòng: ").append(course.getClassroom()).append("\n");
            report.append("   Sinh viên: ").append(
                course.getEnrolledStudents() != null ? course.getEnrolledStudents().size() : 0
            ).append("/").append(course.getMaxStudents()).append("\n\n");
        }
        
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Xuất báo cáo");
        alert.setHeaderText("Báo cáo môn học giảng dạy");
        
        TextArea textArea = new TextArea(report.toString());
        textArea.setEditable(false);
        textArea.setWrapText(true);
        textArea.setPrefSize(600, 500);
        
        alert.getDialogPane().setContent(textArea);
        alert.showAndWait();
    }
    
    /**
     * Extract time information from course schedule
     */
    private String extractTimeFromSchedule(Course course) {
        try {
            // Try to get detailed schedule information first
            List<CourseSchedule> schedules = scheduleService.getSchedulesByCourse(course.getCourseId());
            if (!schedules.isEmpty()) {
                // Get the first schedule's time range
                return schedules.get(0).getTimeRange();
            }

            // Fallback to parsing from course.getSchedule() string
            String schedule = course.getSchedule();
            if (schedule != null && !schedule.trim().isEmpty()) {
                // Parse format like "Thứ 2, 4, 6 - 7:30-9:30"
                if (schedule.contains("-")) {
                    String[] parts = schedule.split("-");
                    if (parts.length >= 2) {
                        // Look for time pattern like "7:30-9:30"
                        String timePart = parts[parts.length - 1].trim();
                        if (timePart.matches(".*\\d{1,2}:\\d{2}.*")) {
                            return timePart;
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("❌ Error extracting time from schedule: " + e.getMessage());
        }

        return "Chưa xếp";
    }

    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
