package com.example.doancuoikyjava.service;

import com.example.doancuoikyjava.model.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ReportService {
    
    private UserService userService;
    private CourseService courseService;
    private GradeService gradeService;
    
    public ReportService() {
        this.userService = new UserService();
        this.courseService = new CourseService();
        this.gradeService = new GradeService();
    }
    
    /**
     * Tạo báo cáo thống kê sinh viên toàn diện
     */
    public String generateStudentStatisticsReport() {
        StringBuilder report = new StringBuilder();
        
        // Header đẹp
        report.append("╔══════════════════════════════════════════════════════════════════════════════╗\n");
        report.append("║                         📊 BÁO CÁO SINH VIÊN                                 ║\n");
        report.append("╚══════════════════════════════════════════════════════════════════════════════╝\n\n");

        // Thông tin báo cáo
        report.append("📅 Ngày tạo: ").append(LocalDateTime.now().format(
            DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"))).append("\n");
        report.append("👤 Người tạo: Admin\n");
        report.append("🏫 Hệ thống: Quản lý Sinh viên và Giáo viên v1.0\n");
        report.append("📍 Trường: Đại học ABC\n\n");
        
        // Lấy dữ liệu
        List<User> users = userService.getUsersByRole(User.UserRole.STUDENT);
        List<Student> students = users.stream()
                .filter(u -> u instanceof Student)
                .map(u -> (Student) u)
                .collect(Collectors.toList());

        // Kiểm tra có dữ liệu không
        if (students.isEmpty()) {
            report.append("⚠️ THÔNG BÁO:\n");
            report.append("═══════════════\n");
            report.append("Không có dữ liệu sinh viên trong hệ thống.\n");
            report.append("Vui lòng thêm sinh viên trước khi tạo báo cáo.\n\n");
            return report.toString();
        }

        // Thống kê tổng quan
        addOverallStatistics(report, students);
        
        // Thống kê theo lớp
        addClassStatistics(report, students);
        
        // Thống kê theo ngành
        addMajorStatistics(report, students);
        
        // Top sinh viên
        addTopStudents(report, students);
        
        // Sinh viên cần hỗ trợ
        addStudentsNeedSupport(report, students);
        
        // Khuyến nghị
        addRecommendations(report, students);

        String result = report.toString();
        System.out.println("📊 Student report generated with length: " + result.length());
        return result;
    }
    
    /**
     * Tạo báo cáo thống kê môn học
     */
    public String generateCourseStatisticsReport() {
        StringBuilder report = new StringBuilder();
        
        report.append("╔══════════════════════════════════════════════════════════════════════════════╗\n");
        report.append("║                        📚 BÁO CÁO THỐNG KÊ MÔN HỌC                           ║\n");
        report.append("╚══════════════════════════════════════════════════════════════════════════════╝\n\n");

        report.append("📅 Ngày tạo: ").append(LocalDateTime.now().format(
            DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"))).append("\n");
        report.append("👤 Người tạo: Admin\n");
        report.append("🏫 Hệ thống: Quản lý Sinh viên và Giáo viên v1.0\n\n");
        
        List<Course> courses = courseService.getAllCourses();

        // Kiểm tra có dữ liệu không
        if (courses.isEmpty()) {
            report.append("⚠️ THÔNG BÁO:\n");
            report.append("═══════════════\n");
            report.append("Không có dữ liệu môn học trong hệ thống.\n");
            report.append("Vui lòng thêm môn học trước khi tạo báo cáo.\n\n");
            return report.toString();
        }

        report.append("📚 TỔNG QUAN MÔN HỌC:\n");
        report.append("═══════════════════════\n");
        report.append("Tổng số môn học: ").append(courses.size()).append("\n");
        
        // Thống kê theo tín chỉ
        Map<Integer, Long> creditStats = courses.stream()
                .collect(Collectors.groupingBy(Course::getCredits, Collectors.counting()));
        
        report.append("\nPhân bố theo tín chỉ:\n");
        creditStats.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> report.append("- ").append(entry.getKey())
                        .append(" tín chỉ: ").append(entry.getValue()).append(" môn\n"));
        
        // Thống kê đăng ký
        report.append("\n📊 THỐNG KÊ ĐĂNG KÝ:\n");
        report.append("═══════════════════════\n");
        
        for (Course course : courses) {
            int enrolled = course.getEnrolledStudents().size();
            int maxStudents = course.getMaxStudents();
            double fillRate = (enrolled * 100.0) / maxStudents;
            
            report.append(String.format("%-30s: %d/%d (%.1f%%)\n",
                course.getCourseName(), enrolled, maxStudents, fillRate));
        }
        
        return report.toString();
    }
    
    /**
     * Tạo báo cáo điểm số theo giáo viên
     */
    public String generateTeacherGradeReport(String teacherId) {
        StringBuilder report = new StringBuilder();
        
        // Lấy thông tin giáo viên
        User teacher = userService.getUserById(teacherId);
        if (teacher == null) {
            return "Không tìm thấy giáo viên với ID: " + teacherId;
        }
        
        report.append("BÁO CÁO ĐIỂM SỐ THEO GIÁO VIÊN\n");
        report.append("===============================\n\n");
        
        report.append("👨‍🏫 Giáo viên: ").append(teacher.getFullName()).append("\n");
        report.append("📅 Ngày tạo: ").append(LocalDateTime.now().format(
            DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"))).append("\n\n");
        
        // Lấy môn học của giáo viên
        List<Course> teacherCourses = courseService.getCoursesByTeacher(teacherId);
        
        report.append("📚 MÔN HỌC ĐANG DẠY:\n");
        report.append("═══════════════════════\n");
        
        for (Course course : teacherCourses) {
            report.append("🔹 ").append(course.getCourseName()).append(" (").append(course.getCourseId()).append(")\n");
            
            // Lấy điểm của môn học
            List<Grade> courseGrades = gradeService.getGradesByCourse(course.getCourseId());
            
            if (courseGrades.isEmpty()) {
                report.append("   Chưa có điểm nào được nhập\n\n");
                continue;
            }
            
            // Thống kê điểm
            double avgScore = courseGrades.stream()
                    .mapToDouble(Grade::getScore)
                    .average()
                    .orElse(0.0);
            
            long excellentCount = courseGrades.stream()
                    .filter(g -> g.getScore() >= 8.5)
                    .count();
            
            long goodCount = courseGrades.stream()
                    .filter(g -> g.getScore() >= 7.0 && g.getScore() < 8.5)
                    .count();
            
            long averageCount = courseGrades.stream()
                    .filter(g -> g.getScore() >= 5.5 && g.getScore() < 7.0)
                    .count();
            
            long weakCount = courseGrades.stream()
                    .filter(g -> g.getScore() >= 4.0 && g.getScore() < 5.5)
                    .count();
            
            long failCount = courseGrades.stream()
                    .filter(g -> g.getScore() < 4.0)
                    .count();
            
            report.append("   - Số sinh viên có điểm: ").append(courseGrades.size()).append("\n");
            report.append("   - Điểm trung bình: ").append(String.format("%.2f", avgScore)).append("\n");
            report.append("   - Giỏi (≥8.5): ").append(excellentCount).append("\n");
            report.append("   - Khá (7.0-8.4): ").append(goodCount).append("\n");
            report.append("   - Trung bình (5.5-6.9): ").append(averageCount).append("\n");
            report.append("   - Yếu (4.0-5.4): ").append(weakCount).append("\n");
            report.append("   - Kém (<4.0): ").append(failCount).append("\n\n");
        }
        
        return report.toString();
    }
    
    /**
     * Tạo báo cáo bảng điểm sinh viên
     */
    public String generateStudentTranscript(String studentId) {
        StringBuilder report = new StringBuilder();
        
        // Lấy thông tin sinh viên
        User user = userService.getUserById(studentId);
        if (!(user instanceof Student)) {
            return "Không tìm thấy sinh viên với ID: " + studentId;
        }
        
        Student student = (Student) user;
        
        report.append("BẢNG ĐIỂM SINH VIÊN\n");
        report.append("===================\n\n");
        
        report.append("👨‍🎓 Sinh viên: ").append(student.getFullName()).append("\n");
        report.append("🆔 MSSV: ").append(student.getStudentId()).append("\n");
        report.append("🏫 Lớp: ").append(student.getClassName()).append("\n");
        report.append("📖 Ngành: ").append(student.getMajor()).append("\n");
        report.append("📅 Ngày in: ").append(LocalDateTime.now().format(
            DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"))).append("\n\n");
        
        // Lấy điểm của sinh viên
        List<Grade> studentGrades = gradeService.getGradesByStudent(studentId);
        
        if (studentGrades.isEmpty()) {
            report.append("Chưa có điểm nào được ghi nhận.\n");
            return report.toString();
        }
        
        report.append("📊 BẢNG ĐIỂM CHI TIẾT:\n");
        report.append("═══════════════════════\n");
        report.append("Mã MH\t\tTên môn học\t\t\tTín chỉ\tĐiểm\tĐiểm chữ\tHệ số\n");
        report.append("─────────────────────────────────────────────────────────────────────\n");
        
        double totalGradePoints = 0;
        int totalCredits = 0;
        
        for (Grade grade : studentGrades) {
            report.append(String.format("%-10s\t%-25s\t%d\t%.2f\t%s\t\t%.2f\n",
                grade.getCourseId(),
                grade.getCourseName(),
                grade.getCredits(),
                grade.getScore(),
                grade.getLetterGrade(),
                grade.getGradePoint()
            ));
            
            totalGradePoints += grade.getGradePoint() * grade.getCredits();
            totalCredits += grade.getCredits();
        }
        
        double gpa = totalCredits > 0 ? totalGradePoints / totalCredits : 0;
        
        report.append("─────────────────────────────────────────────────────────────────────\n");
        report.append("Tổng tín chỉ tích lũy: ").append(totalCredits).append("\n");
        report.append("GPA: ").append(String.format("%.2f", gpa)).append("\n");
        report.append("Xếp loại: ").append(getGradeClassification(gpa)).append("\n");
        
        return report.toString();
    }
    
    // Helper methods
    private void addOverallStatistics(StringBuilder report, List<Student> students) {
        report.append("📊 THỐNG KÊ TỔNG QUAN:\n");
        report.append("═══════════════════════\n");
        
        int excellent = 0, good = 0, average = 0, weak = 0, poor = 0;
        double totalGpa = 0;
        
        for (Student student : students) {
            double gpa = student.getGpa();
            totalGpa += gpa;
            
            if (gpa >= 3.6) excellent++;
            else if (gpa >= 3.2) good++;
            else if (gpa >= 2.5) average++;
            else if (gpa >= 2.0) weak++;
            else poor++;
        }
        
        report.append("Tổng số sinh viên: ").append(students.size()).append("\n");

        if (students.size() > 0) {
            report.append("🟢 Giỏi (GPA ≥ 3.6): ").append(excellent).append(" (").append(String.format("%.1f", excellent * 100.0 / students.size())).append("%)\n");
            report.append("🔵 Khá (3.2-3.6): ").append(good).append(" (").append(String.format("%.1f", good * 100.0 / students.size())).append("%)\n");
            report.append("🟡 Trung bình (2.5-3.2): ").append(average).append(" (").append(String.format("%.1f", average * 100.0 / students.size())).append("%)\n");
            report.append("🟠 Yếu (2.0-2.5): ").append(weak).append(" (").append(String.format("%.1f", weak * 100.0 / students.size())).append("%)\n");
            report.append("🔴 Kém (<2.0): ").append(poor).append(" (").append(String.format("%.1f", poor * 100.0 / students.size())).append("%)\n");
        } else {
            report.append("🟢 Giỏi (GPA ≥ 3.6): 0 (0.0%)\n");
            report.append("🔵 Khá (3.2-3.6): 0 (0.0%)\n");
            report.append("🟡 Trung bình (2.5-3.2): 0 (0.0%)\n");
            report.append("🟠 Yếu (2.0-2.5): 0 (0.0%)\n");
            report.append("🔴 Kém (<2.0): 0 (0.0%)\n");
        }
        
        double avgGpa = students.isEmpty() ? 0 : totalGpa / students.size();
        report.append("📈 GPA trung bình: ").append(String.format("%.2f", avgGpa)).append("\n\n");
    }
    
    private void addClassStatistics(StringBuilder report, List<Student> students) {
        report.append("📚 THỐNG KÊ THEO LỚP:\n");
        report.append("═══════════════════════\n");
        
        Map<String, List<Student>> classByStudents = students.stream()
                .filter(s -> s.getClassName() != null)
                .collect(Collectors.groupingBy(Student::getClassName));
        
        for (Map.Entry<String, List<Student>> entry : classByStudents.entrySet()) {
            String className = entry.getKey();
            List<Student> classStudents = entry.getValue();
            
            double avgGpa = classStudents.stream()
                    .mapToDouble(Student::getGpa)
                    .average()
                    .orElse(0.0);
            
            report.append("🏫 Lớp ").append(className).append(": ");
            report.append(classStudents.size()).append(" sinh viên, ");
            report.append("GPA TB: ").append(String.format("%.2f", avgGpa)).append("\n");
        }
        report.append("\n");
    }
    
    private void addMajorStatistics(StringBuilder report, List<Student> students) {
        report.append("🎓 THỐNG KÊ THEO NGÀNH:\n");
        report.append("═══════════════════════\n");
        
        Map<String, List<Student>> majorByStudents = students.stream()
                .filter(s -> s.getMajor() != null)
                .collect(Collectors.groupingBy(Student::getMajor));
        
        for (Map.Entry<String, List<Student>> entry : majorByStudents.entrySet()) {
            String majorName = entry.getKey();
            List<Student> majorStudents = entry.getValue();
            
            double avgGpa = majorStudents.stream()
                    .mapToDouble(Student::getGpa)
                    .average()
                    .orElse(0.0);
            
            report.append("📖 Ngành ").append(majorName).append(": ");
            report.append(majorStudents.size()).append(" sinh viên, ");
            report.append("GPA TB: ").append(String.format("%.2f", avgGpa)).append("\n");
        }
        report.append("\n");
    }
    
    private void addTopStudents(StringBuilder report, List<Student> students) {
        report.append("🏆 TOP 10 SINH VIÊN XUẤT SẮC:\n");
        report.append("═══════════════════════════════\n");
        
        List<Student> topStudents = students.stream()
                .sorted((s1, s2) -> Double.compare(s2.getGpa(), s1.getGpa()))
                .limit(10)
                .collect(Collectors.toList());
        
        int rank = 1;
        for (Student student : topStudents) {
            report.append(String.format("%d. %-20s (%-10s) - GPA: %.2f\n",
                rank++, student.getFullName(), student.getStudentId(), student.getGpa()));
        }
        report.append("\n");
    }
    
    private void addStudentsNeedSupport(StringBuilder report, List<Student> students) {
        report.append("⚠️ SINH VIÊN CẦN HỖ TRỢ (GPA < 2.0):\n");
        report.append("═══════════════════════════════════════\n");
        
        List<Student> needSupport = students.stream()
                .filter(s -> s.getGpa() < 2.0)
                .sorted((s1, s2) -> Double.compare(s1.getGpa(), s2.getGpa()))
                .collect(Collectors.toList());
        
        if (needSupport.isEmpty()) {
            report.append("✅ Không có sinh viên nào cần hỗ trợ đặc biệt.\n\n");
        } else {
            for (Student student : needSupport) {
                report.append(String.format("- %-20s (%-10s) - GPA: %.2f - Lớp: %s\n",
                    student.getFullName(), student.getStudentId(), 
                    student.getGpa(), student.getClassName()));
            }
            report.append("\n");
        }
    }
    
    private void addRecommendations(StringBuilder report, List<Student> students) {
        report.append("💡 KHUYẾN NGHỊ:\n");
        report.append("═══════════════\n");
        
        long excellentCount = students.stream().filter(s -> s.getGpa() >= 3.6).count();
        long poorCount = students.stream().filter(s -> s.getGpa() < 2.0).count();
        
        double excellentPercent = (excellentCount * 100.0) / students.size();
        double poorPercent = (poorCount * 100.0) / students.size();
        
        if (excellentPercent < 20) {
            report.append("• Tăng cường chương trình học bổng khuyến khích\n");
        }
        if (poorPercent > 15) {
            report.append("• Thiết lập chương trình hỗ trợ học tập\n");
        }
        report.append("• Cải thiện phương pháp giảng dạy\n");
        report.append("• Tăng cường hoạt động ngoại khóa\n\n");
    }
    
    private String getGradeClassification(double gpa) {
        if (gpa >= 3.6) return "Giỏi";
        else if (gpa >= 3.2) return "Khá";
        else if (gpa >= 2.5) return "Trung bình";
        else if (gpa >= 2.0) return "Yếu";
        else return "Kém";
    }
}
