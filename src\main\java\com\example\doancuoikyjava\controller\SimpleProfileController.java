package com.example.doancuoikyjava.controller;

import com.example.doancuoikyjava.model.*;
import com.example.doancuoikyjava.service.*;
import com.example.doancuoikyjava.util.SceneManager;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;

import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;

public class SimpleProfileController implements Initializable {
    
    @FXML private Label welcomeLabel;
    @FXML private Button backButton;
    @FXML private Button editButton;
    @FXML private Button saveButton;
    @FXML private Button cancelButton;
    
    // Common fields
    @FXML private TextField userIdField;
    @FXML private TextField fullNameField;
    @FXML private TextField emailField;
    @FXML private TextField phoneField;
    @FXML private TextArea addressArea;
    @FXML private DatePicker birthDatePicker;
    
    // Student specific fields
    @FXML private TextField classNameField;
    @FXML private TextField majorField;
    @FXML private TextField gpaField;
    @FXML private TextField enrollmentYearField;
    
    // Teacher specific fields
    @FXML private TextField departmentField;
    @FXML private TextField positionField;
    @FXML private TextField experienceField;
    @FXML private TextField salaryField;
    
    // Labels for sections
    @FXML private Label studentSectionLabel;
    @FXML private Label teacherSectionLabel;
    
    private UserService userService;
    private User currentUser;
    private boolean isEditMode = false;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        try {
            System.out.println("SimpleProfileController: Bắt đầu initialize");
            userService = new UserService();
            currentUser = SceneManager.getCurrentUser();
            
            if (currentUser == null) {
                System.out.println("SimpleProfileController: currentUser is null!");
                showAlert("Lỗi", "Không thể lấy thông tin người dùng hiện tại!");
                return;
            }
            
            System.out.println("SimpleProfileController: currentUser = " + currentUser.getFullName());
            
            setupUI();
            loadUserData();
            setEditMode(false);
            
            System.out.println("SimpleProfileController: Initialize thành công");
        } catch (Exception e) {
            System.out.println("SimpleProfileController: Lỗi initialize: " + e.getMessage());
            e.printStackTrace();
            showAlert("Lỗi", "Không thể khởi tạo trang thông tin cá nhân: " + e.getMessage());
        }
    }
    
    private void setupUI() {
        if (currentUser != null) {
            welcomeLabel.setText("Thông tin cá nhân - " + currentUser.getFullName());
        }
        
        // Show/hide sections based on user type
        if (currentUser instanceof Student) {
            if (studentSectionLabel != null) studentSectionLabel.setVisible(true);
            if (classNameField != null) classNameField.setVisible(true);
            if (majorField != null) majorField.setVisible(true);
            if (gpaField != null) gpaField.setVisible(true);
            if (enrollmentYearField != null) enrollmentYearField.setVisible(true);
            
            if (teacherSectionLabel != null) teacherSectionLabel.setVisible(false);
            if (departmentField != null) departmentField.setVisible(false);
            if (positionField != null) positionField.setVisible(false);
            if (experienceField != null) experienceField.setVisible(false);
            if (salaryField != null) salaryField.setVisible(false);
        } else if (currentUser instanceof Teacher) {
            if (studentSectionLabel != null) studentSectionLabel.setVisible(false);
            if (classNameField != null) classNameField.setVisible(false);
            if (majorField != null) majorField.setVisible(false);
            if (gpaField != null) gpaField.setVisible(false);
            if (enrollmentYearField != null) enrollmentYearField.setVisible(false);
            
            if (teacherSectionLabel != null) teacherSectionLabel.setVisible(true);
            if (departmentField != null) departmentField.setVisible(true);
            if (positionField != null) positionField.setVisible(true);
            if (experienceField != null) experienceField.setVisible(true);
            if (salaryField != null) salaryField.setVisible(true);
        }
    }
    
    private void loadUserData() {
        if (currentUser == null) return;
        
        // Load common data
        if (userIdField != null) userIdField.setText(currentUser.getUserId());
        if (fullNameField != null) fullNameField.setText(currentUser.getFullName());
        if (emailField != null) emailField.setText(currentUser.getEmail());
        if (phoneField != null) phoneField.setText(currentUser.getPhone());
        if (addressArea != null) addressArea.setText(currentUser.getAddress());
        
        if (currentUser.getDateOfBirth() != null && birthDatePicker != null) {
            birthDatePicker.setValue(currentUser.getDateOfBirth());
        }
        
        // Load specific data based on user type
        if (currentUser instanceof Student) {
            Student student = (Student) currentUser;
            if (classNameField != null) classNameField.setText(student.getClassName());
            if (majorField != null) majorField.setText(student.getMajor());
            if (gpaField != null) gpaField.setText(String.format("%.2f", student.getGpa()));
            if (enrollmentYearField != null) enrollmentYearField.setText(String.valueOf(student.getYear()));
        } else if (currentUser instanceof Teacher) {
            Teacher teacher = (Teacher) currentUser;
            if (departmentField != null) departmentField.setText(teacher.getDepartment());
            if (positionField != null) positionField.setText(teacher.getPosition());
            if (experienceField != null) experienceField.setText(String.valueOf(teacher.getExperienceYears()));
            if (salaryField != null) salaryField.setText(String.format("%.0f", teacher.getSalary()));
        }
    }
    
    private void setEditMode(boolean editMode) {
        isEditMode = editMode;
        
        // Enable/disable fields
        if (fullNameField != null) fullNameField.setEditable(editMode);
        if (emailField != null) emailField.setEditable(editMode);
        if (phoneField != null) phoneField.setEditable(editMode);
        if (addressArea != null) addressArea.setEditable(editMode);
        if (birthDatePicker != null) birthDatePicker.setDisable(!editMode);
        
        if (currentUser instanceof Student) {
            if (classNameField != null) classNameField.setEditable(editMode);
            if (majorField != null) majorField.setEditable(editMode);
            // GPA and enrollment year should not be editable by student
            if (gpaField != null) gpaField.setEditable(false);
            if (enrollmentYearField != null) enrollmentYearField.setEditable(false);
        } else if (currentUser instanceof Teacher) {
            if (departmentField != null) departmentField.setEditable(editMode);
            if (positionField != null) positionField.setEditable(editMode);
            if (experienceField != null) experienceField.setEditable(editMode);
            // Salary should not be editable by teacher
            if (salaryField != null) salaryField.setEditable(false);
        }
        
        // Show/hide buttons
        if (editButton != null) editButton.setVisible(!editMode);
        if (saveButton != null) saveButton.setVisible(editMode);
        if (cancelButton != null) cancelButton.setVisible(editMode);
        
        // User ID should never be editable
        if (userIdField != null) userIdField.setEditable(false);
    }
    
    @FXML
    private void editProfile() {
        setEditMode(true);
    }
    
    @FXML
    private void saveProfile() {
        try {
            // Validate input
            if (fullNameField.getText().trim().isEmpty()) {
                showAlert("Lỗi", "Họ tên không được để trống!");
                return;
            }
            
            if (emailField.getText().trim().isEmpty()) {
                showAlert("Lỗi", "Email không được để trống!");
                return;
            }
            
            // Update user data
            currentUser.setFullName(fullNameField.getText().trim());
            currentUser.setEmail(emailField.getText().trim());
            currentUser.setPhone(phoneField.getText().trim());
            currentUser.setAddress(addressArea.getText().trim());
            currentUser.setDateOfBirth(birthDatePicker.getValue());
            
            // Update specific data based on user type
            if (currentUser instanceof Student) {
                Student student = (Student) currentUser;
                student.setClassName(classNameField.getText().trim());
                student.setMajor(majorField.getText().trim());
            } else if (currentUser instanceof Teacher) {
                Teacher teacher = (Teacher) currentUser;
                teacher.setDepartment(departmentField.getText().trim());
                teacher.setPosition(positionField.getText().trim());
                
                try {
                    int experience = Integer.parseInt(experienceField.getText().trim());
                    teacher.setExperienceYears(experience);
                } catch (NumberFormatException e) {
                    showAlert("Lỗi", "Kinh nghiệm phải là số nguyên!");
                    return;
                }
            }
            
            // Save to database/file
            boolean success = userService.updateUser(currentUser);
            
            if (success) {
                showAlert("Thành công", "Cập nhật thông tin cá nhân thành công!");
                setEditMode(false);
                
                // Update current user in SceneManager
                SceneManager.setCurrentUser(currentUser);
            } else {
                showAlert("Lỗi", "Không thể cập nhật thông tin. Vui lòng thử lại!");
            }
            
        } catch (Exception e) {
            showAlert("Lỗi", "Có lỗi xảy ra: " + e.getMessage());
        }
    }
    
    @FXML
    private void cancelEdit() {
        loadUserData(); // Reload original data
        setEditMode(false);
    }
    
    @FXML
    private void goBack() {
        try {
            if (currentUser instanceof Student) {
                SceneManager.switchScene("/com/example/doancuoikyjava/student-dashboard.fxml", 
                                       "Sinh viên - " + currentUser.getFullName());
            } else if (currentUser instanceof Teacher) {
                SceneManager.switchScene("/com/example/doancuoikyjava/teacher-dashboard.fxml", 
                                       "Giáo viên - " + currentUser.getFullName());
            }
        } catch (IOException e) {
            showAlert("Lỗi", "Không thể quay lại trang chính: " + e.getMessage());
        }
    }
    
    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
