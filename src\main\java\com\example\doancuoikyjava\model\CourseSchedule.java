package com.example.doancuoikyjava.model;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * Model class for course schedule
 */
public class CourseSchedule {
    private int scheduleId;
    private String courseId;
    private String courseName; // For display purposes
    private String teacherId; // Teacher ID
    private String teacherName; // For display purposes
    private int dayOfWeek; // 2=Monday, 3=Tuesday, ..., 7=Saturday, 8=Sunday
    private LocalTime startTime;
    private LocalTime endTime;
    private String classroom;
    private String weekType; // ALL, ODD, EVEN
    private String semester;
    private String academicYear;
    private boolean isActive;
    
    // Constructors
    public CourseSchedule() {
        this.weekType = "ALL";
        this.isActive = true;
    }
    
    public CourseSchedule(String courseId, int dayOfWeek, LocalTime startTime, LocalTime endTime, String classroom) {
        this();
        this.courseId = courseId;
        this.dayOfWeek = dayOfWeek;
        this.startTime = startTime;
        this.endTime = endTime;
        this.classroom = classroom;
    }
    
    // Getters and Setters
    public int getScheduleId() {
        return scheduleId;
    }
    
    public void setScheduleId(int scheduleId) {
        this.scheduleId = scheduleId;
    }
    
    public String getCourseId() {
        return courseId;
    }
    
    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }
    
    public String getCourseName() {
        return courseName;
    }
    
    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }

    public String getTeacherId() {
        return teacherId;
    }

    public void setTeacherId(String teacherId) {
        this.teacherId = teacherId;
    }

    public String getTeacherName() {
        return teacherName;
    }

    public void setTeacherName(String teacherName) {
        this.teacherName = teacherName;
    }

    public int getDayOfWeek() {
        return dayOfWeek;
    }
    
    public void setDayOfWeek(int dayOfWeek) {
        this.dayOfWeek = dayOfWeek;
    }
    
    public LocalTime getStartTime() {
        return startTime;
    }
    
    public void setStartTime(LocalTime startTime) {
        this.startTime = startTime;
    }
    
    public LocalTime getEndTime() {
        return endTime;
    }
    
    public void setEndTime(LocalTime endTime) {
        this.endTime = endTime;
    }
    
    public String getClassroom() {
        return classroom;
    }
    
    public void setClassroom(String classroom) {
        this.classroom = classroom;
    }
    
    public String getWeekType() {
        return weekType;
    }
    
    public void setWeekType(String weekType) {
        this.weekType = weekType;
    }
    
    public String getSemester() {
        return semester;
    }
    
    public void setSemester(String semester) {
        this.semester = semester;
    }
    
    public String getAcademicYear() {
        return academicYear;
    }
    
    public void setAcademicYear(String academicYear) {
        this.academicYear = academicYear;
    }
    
    public boolean isActive() {
        return isActive;
    }
    
    public void setActive(boolean active) {
        isActive = active;
    }
    
    // Utility methods
    public String getDayOfWeekText() {
        return switch (dayOfWeek) {
            case 2 -> "Thứ 2";
            case 3 -> "Thứ 3";
            case 4 -> "Thứ 4";
            case 5 -> "Thứ 5";
            case 6 -> "Thứ 6";
            case 7 -> "Thứ 7";
            case 8 -> "Chủ nhật";
            default -> "Không xác định";
        };
    }
    
    public String getTimeRange() {
        if (startTime != null && endTime != null) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
            return startTime.format(formatter) + " - " + endTime.format(formatter);
        }
        return "";
    }
    
    public String getWeekTypeText() {
        return switch (weekType) {
            case "ALL" -> "Tất cả tuần";
            case "ODD" -> "Tuần lẻ";
            case "EVEN" -> "Tuần chẵn";
            default -> weekType;
        };
    }
    
    public String getFullScheduleText() {
        return getDayOfWeekText() + " " + getTimeRange() + 
               (classroom != null && !classroom.isEmpty() ? " - " + classroom : "");
    }
    
    public boolean isConflictWith(CourseSchedule other) {
        if (other == null || this.dayOfWeek != other.dayOfWeek) {
            return false;
        }
        
        // Check time overlap
        return !(this.endTime.isBefore(other.startTime) || this.startTime.isAfter(other.endTime));
    }
    
    public int getDurationMinutes() {
        if (startTime != null && endTime != null) {
            return (int) java.time.Duration.between(startTime, endTime).toMinutes();
        }
        return 0;
    }
    
    @Override
    public String toString() {
        return "CourseSchedule{" +
                "scheduleId=" + scheduleId +
                ", courseId='" + courseId + '\'' +
                ", courseName='" + courseName + '\'' +
                ", dayOfWeek=" + dayOfWeek +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", classroom='" + classroom + '\'' +
                ", weekType='" + weekType + '\'' +
                ", semester='" + semester + '\'' +
                ", academicYear='" + academicYear + '\'' +
                ", isActive=" + isActive +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        CourseSchedule that = (CourseSchedule) o;
        
        if (scheduleId != that.scheduleId) return false;
        if (dayOfWeek != that.dayOfWeek) return false;
        if (!courseId.equals(that.courseId)) return false;
        if (!startTime.equals(that.startTime)) return false;
        return endTime.equals(that.endTime);
    }
    
    @Override
    public int hashCode() {
        int result = scheduleId;
        result = 31 * result + courseId.hashCode();
        result = 31 * result + dayOfWeek;
        result = 31 * result + startTime.hashCode();
        result = 31 * result + endTime.hashCode();
        return result;
    }
}
