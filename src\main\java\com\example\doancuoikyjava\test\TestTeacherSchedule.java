package com.example.doancuoikyjava.test;

import com.example.doancuoikyjava.model.Teacher;
import com.example.doancuoikyjava.model.User;
import com.example.doancuoikyjava.model.Course;
import com.example.doancuoikyjava.service.DatabaseUserService;
import com.example.doancuoikyjava.service.CourseService;
import com.example.doancuoikyjava.util.DatabaseConnection;
import com.example.doancuoikyjava.util.SceneManager;

import java.util.List;

public class TestTeacherSchedule {
    
    public static void main(String[] args) {
        System.out.println("🧪 Testing Teacher Schedule Functionality");
        System.out.println("==========================================");
        
        // Test database connection
        if (!DatabaseConnection.testConnection()) {
            System.err.println("❌ Không thể kết nối database!");
            return;
        }
        
        DatabaseUserService userService = new DatabaseUserService();
        CourseService courseService = new CourseService();
        
        // Test 1: Load teacher và courses
        testLoadTeacherAndCourses(userService, courseService);
        
        // Test 2: Test SceneManager current user
        testSceneManagerCurrentUser(userService);
        
        System.out.println("\n✅ Tất cả test đã hoàn thành!");
    }
    
    private static void testLoadTeacherAndCourses(DatabaseUserService userService, CourseService courseService) {
        System.out.println("\n📝 Test 1: Load teacher và courses");
        System.out.println("----------------------------------");
        
        try {
            // Get all teachers
            List<User> teachers = userService.getUsersByRole(User.UserRole.TEACHER);
            System.out.println("📋 Found " + teachers.size() + " teachers");
            
            if (!teachers.isEmpty()) {
                Teacher teacher = (Teacher) teachers.get(0);
                System.out.println("👨‍🏫 Testing with teacher: " + teacher.getFullName() + " (ID: " + teacher.getTeacherId() + ")");
                
                // Get courses for this teacher
                List<Course> teacherCourses = courseService.getCoursesByTeacher(teacher.getTeacherId());
                System.out.println("📚 Found " + teacherCourses.size() + " courses for teacher");
                
                for (Course course : teacherCourses) {
                    System.out.println("  - " + course.getCourseName() + " (Schedule: " + course.getSchedule() + ")");
                }
                
                if (teacherCourses.isEmpty()) {
                    System.out.println("⚠️ No courses found for teacher - this might be the issue!");
                }
                
            } else {
                System.out.println("❌ No teachers found in database!");
            }
            
        } catch (Exception e) {
            System.err.println("❌ Error in test: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testSceneManagerCurrentUser(DatabaseUserService userService) {
        System.out.println("\n📖 Test 2: Test SceneManager current user");
        System.out.println("------------------------------------------");
        
        try {
            // Get a teacher to set as current user
            List<User> teachers = userService.getUsersByRole(User.UserRole.TEACHER);
            
            if (!teachers.isEmpty()) {
                Teacher teacher = (Teacher) teachers.get(0);
                
                // Set current user in SceneManager
                SceneManager.setCurrentUser(teacher);
                System.out.println("✅ Set current user: " + teacher.getFullName());
                
                // Get current user back
                User currentUser = SceneManager.getCurrentUser();
                if (currentUser != null) {
                    System.out.println("✅ Retrieved current user: " + currentUser.getFullName());
                    
                    if (currentUser instanceof Teacher) {
                        Teacher currentTeacher = (Teacher) currentUser;
                        System.out.println("✅ Current user is Teacher: " + currentTeacher.getTeacherId());
                    } else {
                        System.out.println("❌ Current user is not Teacher!");
                    }
                } else {
                    System.out.println("❌ Current user is null!");
                }
                
            } else {
                System.out.println("❌ No teachers found to test with!");
            }
            
        } catch (Exception e) {
            System.err.println("❌ Error in SceneManager test: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
