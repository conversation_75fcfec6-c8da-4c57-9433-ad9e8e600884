<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.example.doancuoikyjava.controller.AdminController">
   <top>
      <HBox alignment="CENTER_LEFT" styleClass="nav-bar" spacing="20.0">
         <children>
            <Label styleClass="nav-button" text="🏠 QUẢN TRỊ VIÊN">
               <font>
                  <Font name="System Bold" size="16.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Label fx:id="welcomeLabel" styleClass="nav-button" text="Xin chào, <PERSON>min">
               <font>
                  <Font size="14.0" />
               </font>
            </Label>

            <Button fx:id="logoutButton" onAction="#handleLogout" styleClass="nav-button" text="Đăng xuất" />
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </HBox>
   </top>
   <left>
      <VBox styleClass="sidebar" spacing="10.0">
         <children>
            <Label styleClass="sidebar-button,active" text="📊 Tổng quan">
               <font>
                  <Font name="System Bold" size="14.0" />
               </font>
            </Label>
            <Button fx:id="manageStudentsBtn" onAction="#showStudentManagement" styleClass="sidebar-button" text="👨‍🎓 Quản lý Sinh viên" />
            <Button fx:id="manageTeachersBtn" onAction="#showTeacherManagement" styleClass="sidebar-button" text="👨‍🏫 Quản lý Giáo viên" />
            <Button fx:id="manageCoursesBtn" onAction="#showCourseManagement" styleClass="sidebar-button" text="📚 Quản lý Môn học" />
            <Button fx:id="manageSchedulesBtn" onAction="#showScheduleManagement" styleClass="sidebar-button" text="🕒 Quản lý Lịch học" />
            <Button fx:id="notificationsBtn" onAction="#showNotifications" styleClass="sidebar-button" text="📢 Quản lý Thông báo" />

            <Button fx:id="chartsBtn" onAction="#showCharts" styleClass="sidebar-button" text="📈 Biểu đồ" />
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>
      </VBox>
   </left>
   <center>
      <ScrollPane fitToWidth="true" styleClass="content-container">
         <content>
            <VBox fx:id="contentArea" styleClass="dashboard-container" spacing="20.0">
               <children>
                  <Label styleClass="label-title" text="Tổng quan hệ thống">
                     <font>
                        <Font name="System Bold" size="24.0" />
                     </font>
                  </Label>
                  
                  <HBox spacing="20.0">
                     <children>
                        <VBox styleClass="stats-card">
                           <children>
                              <Label fx:id="totalStudentsLabel" styleClass="stats-number" text="0">
                                 <font>
                                    <Font name="System Bold" size="36.0" />
                                 </font>
                              </Label>
                              <Label styleClass="stats-label" text="SINH VIÊN">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                           </children>
                        </VBox>
                        
                        <VBox styleClass="stats-card">
                           <children>
                              <Label fx:id="totalTeachersLabel" styleClass="stats-number" text="0">
                                 <font>
                                    <Font name="System Bold" size="36.0" />
                                 </font>
                              </Label>
                              <Label styleClass="stats-label" text="GIÁO VIÊN">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                           </children>
                        </VBox>
                        
                        <VBox styleClass="stats-card">
                           <children>
                              <Label fx:id="totalCoursesLabel" styleClass="stats-number" text="0">
                                 <font>
                                    <Font name="System Bold" size="36.0" />
                                 </font>
                              </Label>
                              <Label styleClass="stats-label" text="MÔN HỌC">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                           </children>
                        </VBox>
                     </children>
                  </HBox>
                  
                  <VBox styleClass="card" spacing="15.0">
                     <children>
                        <Label styleClass="card-header" text="Hoạt động gần đây">
                           <font>
                              <Font name="System Bold" size="18.0" />
                           </font>
                        </Label>
                        <ListView fx:id="recentActivitiesListView" prefHeight="200.0" />
                     </children>
                  </VBox>
                  
                  <HBox spacing="20.0">
                     <children>
                        <VBox styleClass="card" spacing="15.0" HBox.hgrow="ALWAYS">
                           <children>
                              <Label styleClass="card-header" text="Thống kê nhanh">
                                 <font>
                                    <Font name="System Bold" size="18.0" />
                                 </font>
                              </Label>
                              <VBox spacing="10.0">
                                 <children>
                                    <HBox alignment="CENTER_LEFT" spacing="10.0">
                                       <children>
                                          <Label text="Sinh viên mới tuần này:" />
                                          <Region HBox.hgrow="ALWAYS" />
                                          <Label fx:id="newStudentsLabel" styleClass="stats-number" text="0" textFill="#4CAF50">
                                             <font>
                                                <Font name="System Bold" size="16.0" />
                                             </font>
                                          </Label>
                                       </children>
                                    </HBox>
                                    <HBox alignment="CENTER_LEFT" spacing="10.0">
                                       <children>
                                          <Label text="Môn học được mở:" />
                                          <Region HBox.hgrow="ALWAYS" />
                                          <Label fx:id="activeCoursesLabel" styleClass="stats-number" text="0" textFill="#2196F3">
                                             <font>
                                                <Font name="System Bold" size="16.0" />
                                             </font>
                                          </Label>
                                       </children>
                                    </HBox>
                                 </children>
                              </VBox>
                           </children>
                        </VBox>
                        
                        <VBox styleClass="card" spacing="15.0" HBox.hgrow="ALWAYS">
                           <children>
                              <Label styleClass="card-header" text="Thao tác nhanh">
                                 <font>
                                    <Font name="System Bold" size="18.0" />
                                 </font>
                              </Label>
                              <VBox spacing="10.0">
                                 <children>
                                    <Button onAction="#showStudentManagement" styleClass="btn,btn-primary,full-width" text="+ Thêm sinh viên mới" />
                                    <Button onAction="#showTeacherManagement" styleClass="btn,btn-success,full-width" text="+ Thêm giáo viên mới" />
                                    <Button onAction="#showCourseManagement" styleClass="btn,btn-warning,full-width" text="+ Tạo môn học mới" />
                                 </children>
                              </VBox>
                           </children>
                        </VBox>
                     </children>
                  </HBox>
               </children>
               <padding>
                  <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
               </padding>
            </VBox>
         </content>
      </ScrollPane>
   </center>
</BorderPane>
