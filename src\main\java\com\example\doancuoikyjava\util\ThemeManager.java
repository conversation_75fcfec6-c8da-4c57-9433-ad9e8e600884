package com.example.doancuoikyjava.util;

import javafx.scene.Scene;
import javafx.stage.Stage;
import java.util.ArrayList;
import java.util.List;

/**
 * Advanced Theme Manager with Dark/Light mode support
 */
public class ThemeManager {
    
    public enum Theme {
        LIGHT("Light Theme", "/com/example/doancuoikyjava/styles/light-theme.css"),
        DARK("Dark Theme", "/com/example/doancuoikyjava/styles/dark-theme.css"),
        BLUE("Blue Theme", "/com/example/doancuoikyjava/styles/blue-theme.css"),
        GREEN("Green Theme", "/com/example/doancuoikyjava/styles/green-theme.css"),
        RAINBOW("Rainbow", "/com/example/doancuoikyjava/styles/rainbow-theme.css");
        
        private final String displayName;
        private final String cssPath;
        
        Theme(String displayName, String cssPath) {
            this.displayName = displayName;
            this.cssPath = cssPath;
        }
        
        public String getDisplayName() { return displayName; }
        public String getCssPath() { return cssPath; }
    }
    
    private static Theme currentTheme = Theme.LIGHT;
    private static final List<Scene> registeredScenes = new ArrayList<>();
    private static final List<ThemeChangeListener> listeners = new ArrayList<>();
    
    // Light theme colors
    public static final String LIGHT_PRIMARY = "#2196F3";
    public static final String LIGHT_SECONDARY = "#FFC107";
    public static final String LIGHT_SUCCESS = "#4CAF50";
    public static final String LIGHT_WARNING = "#FF9800";
    public static final String LIGHT_ERROR = "#F44336";
    public static final String LIGHT_BACKGROUND = "#FAFAFA";
    public static final String LIGHT_SURFACE = "#FFFFFF";
    public static final String LIGHT_TEXT = "#212121";
    public static final String LIGHT_TEXT_SECONDARY = "#757575";
    public static final String LIGHT_BORDER = "#E0E0E0";
    
    // Dark theme colors
    public static final String DARK_PRIMARY = "#1976D2";
    public static final String DARK_SECONDARY = "#FFA000";
    public static final String DARK_SUCCESS = "#388E3C";
    public static final String DARK_WARNING = "#F57C00";
    public static final String DARK_ERROR = "#D32F2F";
    public static final String DARK_BACKGROUND = "#121212";
    public static final String DARK_SURFACE = "#1E1E1E";
    public static final String DARK_TEXT = "#FFFFFF";
    public static final String DARK_TEXT_SECONDARY = "#AAAAAA";
    public static final String DARK_BORDER = "#424242";
    
    /**
     * Interface for theme change listeners
     */
    public interface ThemeChangeListener {
        void onThemeChanged(Theme newTheme);
    }
    
    /**
     * Set current theme and apply to all registered scenes
     */
    public static void setTheme(Theme theme) {
        Theme oldTheme = currentTheme;
        currentTheme = theme;
        
        // Apply theme to all registered scenes
        for (Scene scene : registeredScenes) {
            applyThemeToScene(scene, theme);
        }
        
        // Notify listeners
        for (ThemeChangeListener listener : listeners) {
            listener.onThemeChanged(theme);
        }
        
        System.out.println("🎨 Theme changed from " + oldTheme.getDisplayName() + " to " + theme.getDisplayName());
    }
    
    /**
     * Get current theme
     */
    public static Theme getCurrentTheme() {
        return currentTheme;
    }
    
    /**
     * Register scene for theme management
     */
    public static void registerScene(Scene scene) {
        if (!registeredScenes.contains(scene)) {
            registeredScenes.add(scene);
            applyThemeToScene(scene, currentTheme);
        }
    }
    
    /**
     * Unregister scene
     */
    public static void unregisterScene(Scene scene) {
        registeredScenes.remove(scene);
    }
    
    /**
     * Apply theme to specific scene
     */
    private static void applyThemeToScene(Scene scene, Theme theme) {
        // Clear existing stylesheets
        scene.getStylesheets().clear();
        
        // Add base stylesheet
        scene.getStylesheets().add(
            ThemeManager.class.getResource("/com/example/doancuoikyjava/styles/base-styles.css").toExternalForm()
        );
        
        // Add theme-specific stylesheet
        try {
            String themeStylesheet = ThemeManager.class.getResource(theme.getCssPath()).toExternalForm();
            scene.getStylesheets().add(themeStylesheet);
        } catch (Exception e) {
            System.err.println("⚠️ Could not load theme stylesheet: " + theme.getCssPath());
            // Fallback to light theme
            if (theme != Theme.LIGHT) {
                try {
                    String lightStylesheet = ThemeManager.class.getResource(Theme.LIGHT.getCssPath()).toExternalForm();
                    scene.getStylesheets().add(lightStylesheet);
                } catch (Exception ex) {
                    System.err.println("❌ Could not load fallback theme stylesheet");
                }
            }
        }
    }
    
    /**
     * Add theme change listener
     */
    public static void addThemeChangeListener(ThemeChangeListener listener) {
        listeners.add(listener);
    }
    
    /**
     * Remove theme change listener
     */
    public static void removeThemeChangeListener(ThemeChangeListener listener) {
        listeners.remove(listener);
    }
    
    /**
     * Get current theme colors
     */
    public static String getPrimaryColor() {
        return currentTheme == Theme.DARK ? DARK_PRIMARY : LIGHT_PRIMARY;
    }
    
    public static String getSecondaryColor() {
        return currentTheme == Theme.DARK ? DARK_SECONDARY : LIGHT_SECONDARY;
    }
    
    public static String getSuccessColor() {
        return currentTheme == Theme.DARK ? DARK_SUCCESS : LIGHT_SUCCESS;
    }
    
    public static String getWarningColor() {
        return currentTheme == Theme.DARK ? DARK_WARNING : LIGHT_WARNING;
    }
    
    public static String getErrorColor() {
        return currentTheme == Theme.DARK ? DARK_ERROR : LIGHT_ERROR;
    }
    
    public static String getBackgroundColor() {
        return currentTheme == Theme.DARK ? DARK_BACKGROUND : LIGHT_BACKGROUND;
    }
    
    public static String getSurfaceColor() {
        return currentTheme == Theme.DARK ? DARK_SURFACE : LIGHT_SURFACE;
    }
    
    public static String getTextColor() {
        return currentTheme == Theme.DARK ? DARK_TEXT : LIGHT_TEXT;
    }
    
    public static String getTextSecondaryColor() {
        return currentTheme == Theme.DARK ? DARK_TEXT_SECONDARY : LIGHT_TEXT_SECONDARY;
    }
    
    public static String getBorderColor() {
        return currentTheme == Theme.DARK ? DARK_BORDER : LIGHT_BORDER;
    }
    
    /**
     * Toggle between light and dark theme
     */
    public static void toggleTheme() {
        Theme newTheme = (currentTheme == Theme.LIGHT) ? Theme.DARK : Theme.LIGHT;
        setTheme(newTheme);
    }
    
    /**
     * Check if current theme is dark
     */
    public static boolean isDarkTheme() {
        return currentTheme == Theme.DARK;
    }
    
    /**
     * Get all available themes
     */
    public static Theme[] getAvailableThemes() {
        return Theme.values();
    }
    
    /**
     * Apply theme to stage
     */
    public static void applyThemeToStage(Stage stage) {
        if (stage.getScene() != null) {
            registerScene(stage.getScene());
        }
    }
    
    /**
     * Create theme-aware style string
     */
    public static String createStyle(String baseStyle) {
        return baseStyle
            .replace("{{PRIMARY}}", getPrimaryColor())
            .replace("{{SECONDARY}}", getSecondaryColor())
            .replace("{{SUCCESS}}", getSuccessColor())
            .replace("{{WARNING}}", getWarningColor())
            .replace("{{ERROR}}", getErrorColor())
            .replace("{{BACKGROUND}}", getBackgroundColor())
            .replace("{{SURFACE}}", getSurfaceColor())
            .replace("{{TEXT}}", getTextColor())
            .replace("{{TEXT_SECONDARY}}", getTextSecondaryColor())
            .replace("{{BORDER}}", getBorderColor());
    }
}
