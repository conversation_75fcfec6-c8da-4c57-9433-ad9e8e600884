<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.example.doancuoikyjava.controller.StudentGradesController">
   <top>
      <HBox alignment="CENTER_LEFT" styleClass="nav-bar" spacing="20.0">
         <children>
            <Button fx:id="backButton" onAction="#goBack" styleClass="nav-button" text="← Quay lại" />
            <Label styleClass="nav-button" text="📝 XEM ĐIỂM SỐ">
               <font>
                  <Font name="System Bold" size="16.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Label fx:id="welcomeLabel" styleClass="nav-button" text="Xin chào, Sinh viên">
               <font>
                  <Font size="14.0" />
               </font>
            </Label>
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </HBox>
   </top>
   <center>
      <VBox styleClass="content-container" spacing="20.0">
         <children>
            <HBox alignment="CENTER_LEFT" spacing="20.0">
               <children>
                  <Label styleClass="label-title" text="Bảng điểm của tôi">
                     <font>
                        <Font name="System Bold" size="24.0" />
                     </font>
                  </Label>
                  <Region HBox.hgrow="ALWAYS" />
                  <Button fx:id="refreshBtn" onAction="#refreshData" styleClass="btn,btn-secondary" text="🔄 Làm mới" />
               </children>
            </HBox>
            
            <HBox spacing="20.0">
               <children>
                  <VBox styleClass="stats-card">
                     <children>
                        <Label fx:id="totalCreditsLabel" styleClass="stats-number" text="0">
                           <font>
                              <Font name="System Bold" size="36.0" />
                           </font>
                        </Label>
                        <Label styleClass="stats-label" text="TỔNG TÍN CHỈ">
                           <font>
                              <Font name="System Bold" size="14.0" />
                           </font>
                        </Label>
                     </children>
                  </VBox>
                  
                  <VBox styleClass="stats-card">
                     <children>
                        <Label fx:id="gpaLabel" styleClass="stats-number" text="0.00">
                           <font>
                              <Font name="System Bold" size="36.0" />
                           </font>
                        </Label>
                        <Label styleClass="stats-label" text="GPA">
                           <font>
                              <Font name="System Bold" size="14.0" />
                           </font>
                        </Label>
                     </children>
                  </VBox>
                  
                  <VBox styleClass="stats-card">
                     <children>
                        <Label fx:id="averageScoreLabel" styleClass="stats-number" text="0.00">
                           <font>
                              <Font name="System Bold" size="36.0" />
                           </font>
                        </Label>
                        <Label styleClass="stats-label" text="ĐIỂM TRUNG BÌNH">
                           <font>
                              <Font name="System Bold" size="14.0" />
                           </font>
                        </Label>
                     </children>
                  </VBox>
               </children>
            </HBox>
            
            <HBox spacing="15.0">
               <children>
                  <ComboBox fx:id="semesterFilterComboBox" promptText="Lọc theo học kỳ" />
                  <ComboBox fx:id="gradeFilterComboBox" promptText="Lọc theo xếp loại" />
                  <TextField fx:id="searchField" promptText="Tìm kiếm môn học..." HBox.hgrow="ALWAYS" />
                  <Button fx:id="searchBtn" onAction="#searchGrades" styleClass="btn,btn-warning" text="Tìm kiếm" />
               </children>
            </HBox>
            
            <TableView fx:id="gradesTableView" VBox.vgrow="ALWAYS">
               <columns>
                  <TableColumn fx:id="courseIdColumn" prefWidth="100.0" text="Mã MH" />
                  <TableColumn fx:id="courseNameColumn" prefWidth="250.0" text="Tên môn học" />
                  <TableColumn fx:id="creditsColumn" prefWidth="80.0" text="Tín chỉ" />
                  <TableColumn fx:id="scoreColumn" prefWidth="80.0" text="Điểm số" />
                  <TableColumn fx:id="letterGradeColumn" prefWidth="80.0" text="Xếp loại" />
                  <TableColumn fx:id="gradePointColumn" prefWidth="80.0" text="Điểm 4" />
                  <TableColumn fx:id="semesterColumn" prefWidth="100.0" text="Học kỳ" />
                  <TableColumn fx:id="dateColumn" prefWidth="120.0" text="Ngày nhập" />
               </columns>
            </TableView>
            
            <HBox alignment="CENTER_LEFT" spacing="20.0">
               <children>
                  <Label fx:id="totalGradesLabel" text="Tổng số môn: 0" />
                  <Label fx:id="passedSubjectsLabel" text="Môn đã qua: 0" />
                  <Label fx:id="failedSubjectsLabel" text="Môn chưa qua: 0" />
                  <Region HBox.hgrow="ALWAYS" />
                  <Button fx:id="exportBtn" onAction="#exportGrades" styleClass="btn,btn-success" text="📊 Xuất bảng điểm" />
               </children>
            </HBox>
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>
      </VBox>
   </center>
</BorderPane>
