package com.example.doancuoikyjava.service;

import com.example.doancuoikyjava.model.Course;
import com.example.doancuoikyjava.util.DataManager;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * Enhanced Course Service with improved performance, caching, and features
 */
public class EnhancedCourseService extends EnhancedBaseService {
    
    private static final String CACHE_ALL_COURSES = "all_courses";
    private static final String CACHE_TEACHER_COURSES = "teacher_courses_";
    private static final String CACHE_COURSE_BY_ID = "course_by_id_";
    
    @Override
    protected String getServiceName() {
        return "EnhancedCourseService";
    }
    
    /**
     * Get all courses with caching
     */
    public List<Course> getAllCourses() {
        return getFromCacheOrExecute(CACHE_ALL_COURSES, () -> {
            if (useDatabaseStorage) {
                return getAllCoursesFromDatabase();
            } else {
                return DataManager.loadCourses();
            }
        });
    }
    
    /**
     * Get all courses asynchronously
     */
    public CompletableFuture<List<Course>> getAllCoursesAsync() {
        return executeQueryAsync(
            """
            SELECT c.*,
                   (SELECT COUNT(*) FROM Course_Enrollments ce WHERE ce.course_id = c.course_id) as enrolled_count
            FROM Courses c
            ORDER BY c.course_name
            """,
            this::createCourseFromResultSet
        );
    }
    
    /**
     * Get course by ID with caching
     */
    public Course getCourseById(String courseId) {
        if (!validateInput(courseId)) return null;
        
        String cacheKey = CACHE_COURSE_BY_ID + courseId;
        return getFromCacheOrExecute(cacheKey, () -> {
            if (useDatabaseStorage) {
                return getCourseByIdFromDatabase(courseId);
            } else {
                return DataManager.loadCourses().stream()
                        .filter(course -> course.getCourseId().equals(courseId))
                        .findFirst()
                        .orElse(null);
            }
        });
    }
    
    /**
     * Get courses by teacher with caching
     */
    public List<Course> getCoursesByTeacher(String teacherId) {
        if (!validateInput(teacherId)) return List.of();
        
        String cacheKey = CACHE_TEACHER_COURSES + teacherId;
        return getFromCacheOrExecute(cacheKey, () -> {
            if (useDatabaseStorage) {
                return getCoursesByTeacherFromDatabase(teacherId);
            } else {
                return DataManager.loadCourses().stream()
                        .filter(course -> course.getTeacherId().equals(teacherId))
                        .collect(Collectors.toList());
            }
        });
    }
    
    /**
     * Search courses by name or ID
     */
    public List<Course> searchCourses(String searchTerm) {
        if (!validateInput(searchTerm)) return List.of();
        
        String sql = """
            SELECT c.*,
                   (SELECT COUNT(*) FROM Course_Enrollments ce WHERE ce.course_id = c.course_id) as enrolled_count
            FROM Courses c
            WHERE c.course_name LIKE ? OR c.course_id LIKE ? OR c.description LIKE ?
            ORDER BY c.course_name
            """;
        
        String searchPattern = "%" + searchTerm + "%";
        return executeQuery(sql, this::createCourseFromResultSet, 
                          searchPattern, searchPattern, searchPattern);
    }
    
    /**
     * Get courses by credits
     */
    public List<Course> getCoursesByCredits(int credits) {
        String sql = """
            SELECT c.*,
                   (SELECT COUNT(*) FROM Course_Enrollments ce WHERE ce.course_id = c.course_id) as enrolled_count
            FROM Courses c
            WHERE c.credits = ?
            ORDER BY c.course_name
            """;
        
        return executeQuery(sql, this::createCourseFromResultSet, credits);
    }
    
    /**
     * Get available courses (not full)
     */
    public List<Course> getAvailableCourses() {
        String sql = """
            SELECT c.*,
                   (SELECT COUNT(*) FROM Course_Enrollments ce WHERE ce.course_id = c.course_id) as enrolled_count
            FROM Courses c
            WHERE (SELECT COUNT(*) FROM Course_Enrollments ce WHERE ce.course_id = c.course_id) < c.max_students
            ORDER BY c.course_name
            """;
        
        return executeQuery(sql, this::createCourseFromResultSet);
    }
    
    /**
     * Add course with enhanced validation and sync
     */
    public boolean addCourse(Course course) {
        if (!validateCourse(course)) return false;
        
        if (useDatabaseStorage) {
            boolean result = addCourseToDatabase(course);
            if (result) {
                clearRelatedCache();
                syncTeacherCourse(course.getTeacherId(), course.getCourseName());
            }
            return result;
        } else {
            List<Course> courses = DataManager.loadCourses();
            
            if (courses.stream().anyMatch(c -> c.getCourseId().equals(course.getCourseId()))) {
                System.err.println("❌ Course ID already exists: " + course.getCourseId());
                return false;
            }
            
            courses.add(course);
            DataManager.saveCourses(courses);
            clearRelatedCache();
            return true;
        }
    }
    
    /**
     * Update course with validation
     */
    public boolean updateCourse(Course course) {
        if (!validateCourse(course)) return false;
        
        String sql = """
            UPDATE Courses SET 
                course_name = ?, description = ?, credits = ?, 
                teacher_id = ?, teacher_name = ?, schedule = ?, 
                classroom = ?, max_students = ?
            WHERE course_id = ?
            """;
        
        boolean result = executeUpdate(sql,
            course.getCourseName(), course.getDescription(), course.getCredits(),
            course.getTeacherId(), course.getTeacherName(), course.getSchedule(),
            course.getClassroom(), course.getMaxStudents(), course.getCourseId()
        );
        
        if (result) {
            clearCache(CACHE_COURSE_BY_ID + course.getCourseId());
            syncTeacherCourse(course.getTeacherId(), course.getCourseName());
        }
        
        return result;
    }
    
    /**
     * Delete course with cascade handling
     */
    public boolean deleteCourse(String courseId) {
        if (!validateInput(courseId)) return false;
        
        // Check if course has enrolled students
        String checkSql = "SELECT COUNT(*) FROM Course_Enrollments WHERE course_id = ?";
        List<Integer> enrollmentCount = executeQuery(checkSql, rs -> {
            try {
                return rs.getInt(1);
            } catch (SQLException e) {
                return 0;
            }
        }, courseId);
        
        if (!enrollmentCount.isEmpty() && enrollmentCount.get(0) > 0) {
            System.err.println("❌ Cannot delete course with enrolled students: " + courseId);
            return false;
        }
        
        String sql = "DELETE FROM Courses WHERE course_id = ?";
        boolean result = executeUpdate(sql, courseId);
        
        if (result) {
            clearCache(CACHE_COURSE_BY_ID + courseId);
            clearRelatedCache();
        }
        
        return result;
    }
    
    /**
     * Enroll student in course with validation
     */
    public boolean enrollStudent(String courseId, String studentId) {
        if (!validateInput(courseId, studentId)) return false;
        
        // Check if course exists and has space
        Course course = getCourseById(courseId);
        if (course == null) {
            System.err.println("❌ Course not found: " + courseId);
            return false;
        }
        
        if (course.getEnrolledStudents().size() >= course.getMaxStudents()) {
            System.err.println("❌ Course is full: " + courseId);
            return false;
        }
        
        if (course.getEnrolledStudents().contains(studentId)) {
            System.err.println("❌ Student already enrolled: " + studentId);
            return false;
        }
        
        String sql = "INSERT INTO Course_Enrollments (course_id, student_id) VALUES (?, ?)";
        boolean result = executeUpdate(sql, courseId, studentId);
        
        if (result) {
            clearCache(CACHE_COURSE_BY_ID + courseId);
            clearRelatedCache();
        }
        
        return result;
    }
    
    /**
     * Get enrollment statistics
     */
    public EnrollmentStats getEnrollmentStats() {
        String sql = """
            SELECT 
                COUNT(DISTINCT c.course_id) as total_courses,
                COUNT(DISTINCT ce.student_id) as total_enrolled_students,
                AVG(CAST(enrolled_count.count as FLOAT)) as avg_enrollment_per_course,
                MAX(enrolled_count.count) as max_enrollment,
                MIN(enrolled_count.count) as min_enrollment
            FROM Courses c
            LEFT JOIN Course_Enrollments ce ON c.course_id = ce.course_id
            CROSS APPLY (
                SELECT COUNT(*) as count 
                FROM Course_Enrollments ce2 
                WHERE ce2.course_id = c.course_id
            ) enrolled_count
            """;
        
        List<EnrollmentStats> stats = executeQuery(sql, rs -> {
            try {
                return new EnrollmentStats(
                    rs.getInt("total_courses"),
                    rs.getInt("total_enrolled_students"),
                    rs.getDouble("avg_enrollment_per_course"),
                    rs.getInt("max_enrollment"),
                    rs.getInt("min_enrollment")
                );
            } catch (SQLException e) {
                return null;
            }
        });
        
        return stats.isEmpty() ? new EnrollmentStats(0, 0, 0.0, 0, 0) : stats.get(0);
    }
    
    // Private helper methods
    private List<Course> getAllCoursesFromDatabase() {
        String sql = """
            SELECT c.*,
                   (SELECT COUNT(*) FROM Course_Enrollments ce WHERE ce.course_id = c.course_id) as enrolled_count
            FROM Courses c
            ORDER BY c.course_name
            """;
        
        List<Course> courses = executeQuery(sql, this::createCourseFromResultSet);
        
        // Load enrolled students for each course
        for (Course course : courses) {
            loadEnrolledStudents(course);
        }
        
        return courses;
    }
    
    private Course getCourseByIdFromDatabase(String courseId) {
        String sql = """
            SELECT c.*,
                   (SELECT COUNT(*) FROM Course_Enrollments ce WHERE ce.course_id = c.course_id) as enrolled_count
            FROM Courses c
            WHERE c.course_id = ?
            """;
        
        List<Course> courses = executeQuery(sql, this::createCourseFromResultSet, courseId);
        
        if (!courses.isEmpty()) {
            Course course = courses.get(0);
            loadEnrolledStudents(course);
            return course;
        }
        
        return null;
    }
    
    private List<Course> getCoursesByTeacherFromDatabase(String teacherId) {
        String sql = """
            SELECT c.*,
                   (SELECT COUNT(*) FROM Course_Enrollments ce WHERE ce.course_id = c.course_id) as enrolled_count
            FROM Courses c
            WHERE c.teacher_id = ?
            ORDER BY c.course_name
            """;
        
        List<Course> courses = executeQuery(sql, this::createCourseFromResultSet, teacherId);
        
        for (Course course : courses) {
            loadEnrolledStudents(course);
        }
        
        return courses;
    }
    
    private Course createCourseFromResultSet(ResultSet rs) {
        try {
            Course course = new Course();
            course.setCourseId(rs.getString("course_id"));
            course.setCourseName(rs.getString("course_name"));
            course.setDescription(rs.getString("description"));
            course.setCredits(rs.getInt("credits"));
            course.setTeacherId(rs.getString("teacher_id"));
            course.setTeacherName(rs.getString("teacher_name"));
            course.setSchedule(rs.getString("schedule"));
            course.setClassroom(rs.getString("classroom"));
            course.setMaxStudents(rs.getInt("max_students"));
            return course;
        } catch (SQLException e) {
            System.err.println("❌ Error creating course from ResultSet: " + e.getMessage());
            return null;
        }
    }
    
    private void loadEnrolledStudents(Course course) {
        String sql = "SELECT student_id FROM Course_Enrollments WHERE course_id = ?";
        List<String> studentIds = executeQuery(sql, rs -> {
            try {
                return rs.getString("student_id");
            } catch (SQLException e) {
                return null;
            }
        }, course.getCourseId());
        
        course.setEnrolledStudents(studentIds);
    }
    
    private boolean addCourseToDatabase(Course course) {
        String sql = """
            INSERT INTO Courses (course_id, course_name, description, credits, teacher_id, teacher_name,
                               schedule, classroom, max_students)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """;
        
        return executeUpdate(sql,
            course.getCourseId(), course.getCourseName(), course.getDescription(),
            course.getCredits(), course.getTeacherId(), course.getTeacherName(),
            course.getSchedule(), course.getClassroom(), course.getMaxStudents()
        );
    }
    
    private boolean validateCourse(Course course) {
        if (course == null) {
            System.err.println("❌ Course is null");
            return false;
        }
        
        if (!validateInput(course.getCourseId(), course.getCourseName())) {
            System.err.println("❌ Course ID or name is invalid");
            return false;
        }
        
        if (course.getCredits() <= 0 || course.getCredits() > 10) {
            System.err.println("❌ Invalid credits: " + course.getCredits());
            return false;
        }
        
        if (course.getMaxStudents() <= 0 || course.getMaxStudents() > 200) {
            System.err.println("❌ Invalid max students: " + course.getMaxStudents());
            return false;
        }
        
        return true;
    }
    
    private void syncTeacherCourse(String teacherId, String courseName) {
        if (!validateInput(teacherId, courseName)) return;
        
        String sql = """
            IF NOT EXISTS (SELECT 1 FROM Teacher_Courses WHERE teacher_id = ? AND course_name = ?)
            INSERT INTO Teacher_Courses (teacher_id, course_name) VALUES (?, ?)
            """;
        
        executeUpdate(sql, teacherId, courseName, teacherId, courseName);
    }
    
    @Override
    protected void clearRelatedCache() {
        cache.entrySet().removeIf(entry -> 
            entry.getKey().startsWith(CACHE_ALL_COURSES) ||
            entry.getKey().startsWith(CACHE_TEACHER_COURSES) ||
            entry.getKey().startsWith(CACHE_COURSE_BY_ID)
        );
    }
    
    /**
     * Enrollment statistics class
     */
    public static class EnrollmentStats {
        private final int totalCourses;
        private final int totalEnrolledStudents;
        private final double avgEnrollmentPerCourse;
        private final int maxEnrollment;
        private final int minEnrollment;
        
        public EnrollmentStats(int totalCourses, int totalEnrolledStudents, 
                             double avgEnrollmentPerCourse, int maxEnrollment, int minEnrollment) {
            this.totalCourses = totalCourses;
            this.totalEnrolledStudents = totalEnrolledStudents;
            this.avgEnrollmentPerCourse = avgEnrollmentPerCourse;
            this.maxEnrollment = maxEnrollment;
            this.minEnrollment = minEnrollment;
        }
        
        // Getters
        public int getTotalCourses() { return totalCourses; }
        public int getTotalEnrolledStudents() { return totalEnrolledStudents; }
        public double getAvgEnrollmentPerCourse() { return avgEnrollmentPerCourse; }
        public int getMaxEnrollment() { return maxEnrollment; }
        public int getMinEnrollment() { return minEnrollment; }
        
        @Override
        public String toString() {
            return String.format(
                "Enrollment Statistics:\n" +
                "- Total Courses: %d\n" +
                "- Total Enrolled Students: %d\n" +
                "- Average Enrollment per Course: %.2f\n" +
                "- Maximum Enrollment: %d\n" +
                "- Minimum Enrollment: %d",
                totalCourses, totalEnrolledStudents, avgEnrollmentPerCourse, 
                maxEnrollment, minEnrollment
            );
        }
    }
}
