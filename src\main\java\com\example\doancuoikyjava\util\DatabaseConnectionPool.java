package com.example.doancuoikyjava.util;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * Simple Database Connection Pool implementation
 * Provides basic connection pooling with monitoring
 */
public class DatabaseConnectionPool {

    private static final BlockingQueue<Connection> connectionPool = new LinkedBlockingQueue<>();
    private static final AtomicInteger activeConnections = new AtomicInteger(0);
    private static final AtomicInteger totalConnectionsCreated = new AtomicInteger(0);
    private static long initializationTime;
    private static boolean initialized = false;
    
    // Database configuration
    private static final String SERVER = "LAPTOP-1HR3G05C";
    private static final String DATABASE = "StudentManagementDB";
    private static final String USERNAME = "sa";
    private static final String PASSWORD = "123456789";
    private static final int PORT = 1434;
    
    // Connection pool configuration
    private static final int MINIMUM_IDLE = 5;
    private static final int MAXIMUM_POOL_SIZE = 20;
    private static final long CONNECTION_TIMEOUT = 30000; // 30 seconds
    private static final long IDLE_TIMEOUT = 600000; // 10 minutes
    private static final long MAX_LIFETIME = 1800000; // 30 minutes
    
    static {
        initializePool();
    }
    
    /**
     * Initialize the connection pool
     */
    private static void initializePool() {
        if (initialized) return;

        try {
            long startTime = System.currentTimeMillis();

            // Create initial connections
            for (int i = 0; i < MINIMUM_IDLE; i++) {
                Connection conn = createNewConnection();
                if (conn != null) {
                    connectionPool.offer(conn);
                }
            }

            initializationTime = System.currentTimeMillis() - startTime;
            initialized = true;

            System.out.println("🏊 Database Connection Pool initialized successfully!");
            System.out.println("📊 Pool Configuration:");
            System.out.println("   • Min Idle: " + MINIMUM_IDLE);
            System.out.println("   • Max Pool Size: " + MAXIMUM_POOL_SIZE);
            System.out.println("   • Connection Timeout: " + CONNECTION_TIMEOUT + "ms");
            System.out.println("   • Initialization Time: " + initializationTime + "ms");
            System.out.println("   • Initial Connections: " + connectionPool.size());

        } catch (Exception e) {
            System.err.println("❌ Failed to initialize connection pool: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Database connection pool initialization failed", e);
        }
    }

    /**
     * Create a new database connection
     */
    private static Connection createNewConnection() throws SQLException {
        String jdbcUrl = String.format(
            "********************************************************************************;",
            SERVER, PORT, DATABASE
        );

        try {
            Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
            return DriverManager.getConnection(jdbcUrl, USERNAME, PASSWORD);
        } catch (ClassNotFoundException e) {
            throw new SQLException("SQL Server JDBC Driver not found", e);
        }
    }
    
    /**
     * Get connection from pool
     */
    public static Connection getConnection() throws SQLException {
        if (!initialized) {
            throw new SQLException("Connection pool not initialized");
        }

        try {
            // Try to get existing connection from pool
            Connection connection = connectionPool.poll(CONNECTION_TIMEOUT, TimeUnit.MILLISECONDS);

            if (connection == null || connection.isClosed()) {
                // Create new connection if pool is empty or connection is closed
                if (activeConnections.get() < MAXIMUM_POOL_SIZE) {
                    connection = createNewConnection();
                } else {
                    throw new SQLException("Maximum pool size reached");
                }
            }

            activeConnections.incrementAndGet();
            totalConnectionsCreated.incrementAndGet();

            // Log connection statistics periodically
            if (totalConnectionsCreated.get() % 50 == 0) {
                logPoolStatistics();
            }

            return new ConnectionWrapper(connection);

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new SQLException("Interrupted while waiting for connection", e);
        } catch (SQLException e) {
            System.err.println("❌ Failed to get connection from pool: " + e.getMessage());
            throw e;
        }
    }
    
    /**
     * Connection wrapper to track connection usage
     */
    private static class ConnectionWrapper implements Connection {
        private final Connection delegate;
        private boolean closed = false;
        
        public ConnectionWrapper(Connection delegate) {
            this.delegate = delegate;
        }
        
        @Override
        public void close() throws SQLException {
            if (!closed) {
                delegate.close();
                activeConnections.decrementAndGet();
                closed = true;
            }
        }
        
        // Delegate all other methods to the wrapped connection
        @Override
        public java.sql.Statement createStatement() throws SQLException {
            return delegate.createStatement();
        }
        
        @Override
        public java.sql.PreparedStatement prepareStatement(String sql) throws SQLException {
            return delegate.prepareStatement(sql);
        }
        
        @Override
        public java.sql.CallableStatement prepareCall(String sql) throws SQLException {
            return delegate.prepareCall(sql);
        }
        
        @Override
        public String nativeSQL(String sql) throws SQLException {
            return delegate.nativeSQL(sql);
        }
        
        @Override
        public void setAutoCommit(boolean autoCommit) throws SQLException {
            delegate.setAutoCommit(autoCommit);
        }
        
        @Override
        public boolean getAutoCommit() throws SQLException {
            return delegate.getAutoCommit();
        }
        
        @Override
        public void commit() throws SQLException {
            delegate.commit();
        }
        
        @Override
        public void rollback() throws SQLException {
            delegate.rollback();
        }
        
        @Override
        public boolean isClosed() throws SQLException {
            return closed || delegate.isClosed();
        }
        
        @Override
        public java.sql.DatabaseMetaData getMetaData() throws SQLException {
            return delegate.getMetaData();
        }
        
        @Override
        public void setReadOnly(boolean readOnly) throws SQLException {
            delegate.setReadOnly(readOnly);
        }
        
        @Override
        public boolean isReadOnly() throws SQLException {
            return delegate.isReadOnly();
        }
        
        @Override
        public void setCatalog(String catalog) throws SQLException {
            delegate.setCatalog(catalog);
        }
        
        @Override
        public String getCatalog() throws SQLException {
            return delegate.getCatalog();
        }
        
        @Override
        public void setTransactionIsolation(int level) throws SQLException {
            delegate.setTransactionIsolation(level);
        }
        
        @Override
        public int getTransactionIsolation() throws SQLException {
            return delegate.getTransactionIsolation();
        }
        
        @Override
        public java.sql.SQLWarning getWarnings() throws SQLException {
            return delegate.getWarnings();
        }
        
        @Override
        public void clearWarnings() throws SQLException {
            delegate.clearWarnings();
        }
        
        @Override
        public java.sql.Statement createStatement(int resultSetType, int resultSetConcurrency) throws SQLException {
            return delegate.createStatement(resultSetType, resultSetConcurrency);
        }
        
        @Override
        public java.sql.PreparedStatement prepareStatement(String sql, int resultSetType, int resultSetConcurrency) throws SQLException {
            return delegate.prepareStatement(sql, resultSetType, resultSetConcurrency);
        }
        
        @Override
        public java.sql.CallableStatement prepareCall(String sql, int resultSetType, int resultSetConcurrency) throws SQLException {
            return delegate.prepareCall(sql, resultSetType, resultSetConcurrency);
        }
        
        @Override
        public java.util.Map<String, Class<?>> getTypeMap() throws SQLException {
            return delegate.getTypeMap();
        }
        
        @Override
        public void setTypeMap(java.util.Map<String, Class<?>> map) throws SQLException {
            delegate.setTypeMap(map);
        }
        
        @Override
        public void setHoldability(int holdability) throws SQLException {
            delegate.setHoldability(holdability);
        }
        
        @Override
        public int getHoldability() throws SQLException {
            return delegate.getHoldability();
        }
        
        @Override
        public java.sql.Savepoint setSavepoint() throws SQLException {
            return delegate.setSavepoint();
        }
        
        @Override
        public java.sql.Savepoint setSavepoint(String name) throws SQLException {
            return delegate.setSavepoint(name);
        }
        
        @Override
        public void rollback(java.sql.Savepoint savepoint) throws SQLException {
            delegate.rollback(savepoint);
        }
        
        @Override
        public void releaseSavepoint(java.sql.Savepoint savepoint) throws SQLException {
            delegate.releaseSavepoint(savepoint);
        }
        
        @Override
        public java.sql.Statement createStatement(int resultSetType, int resultSetConcurrency, int resultSetHoldability) throws SQLException {
            return delegate.createStatement(resultSetType, resultSetConcurrency, resultSetHoldability);
        }
        
        @Override
        public java.sql.PreparedStatement prepareStatement(String sql, int resultSetType, int resultSetConcurrency, int resultSetHoldability) throws SQLException {
            return delegate.prepareStatement(sql, resultSetType, resultSetConcurrency, resultSetHoldability);
        }
        
        @Override
        public java.sql.CallableStatement prepareCall(String sql, int resultSetType, int resultSetConcurrency, int resultSetHoldability) throws SQLException {
            return delegate.prepareCall(sql, resultSetType, resultSetConcurrency, resultSetHoldability);
        }
        
        @Override
        public java.sql.PreparedStatement prepareStatement(String sql, int autoGeneratedKeys) throws SQLException {
            return delegate.prepareStatement(sql, autoGeneratedKeys);
        }
        
        @Override
        public java.sql.PreparedStatement prepareStatement(String sql, int[] columnIndexes) throws SQLException {
            return delegate.prepareStatement(sql, columnIndexes);
        }
        
        @Override
        public java.sql.PreparedStatement prepareStatement(String sql, String[] columnNames) throws SQLException {
            return delegate.prepareStatement(sql, columnNames);
        }
        
        @Override
        public java.sql.Clob createClob() throws SQLException {
            return delegate.createClob();
        }
        
        @Override
        public java.sql.Blob createBlob() throws SQLException {
            return delegate.createBlob();
        }
        
        @Override
        public java.sql.NClob createNClob() throws SQLException {
            return delegate.createNClob();
        }
        
        @Override
        public java.sql.SQLXML createSQLXML() throws SQLException {
            return delegate.createSQLXML();
        }
        
        @Override
        public boolean isValid(int timeout) throws SQLException {
            return delegate.isValid(timeout);
        }
        
        @Override
        public void setClientInfo(String name, String value) throws java.sql.SQLClientInfoException {
            delegate.setClientInfo(name, value);
        }
        
        @Override
        public void setClientInfo(java.util.Properties properties) throws java.sql.SQLClientInfoException {
            delegate.setClientInfo(properties);
        }
        
        @Override
        public String getClientInfo(String name) throws SQLException {
            return delegate.getClientInfo(name);
        }
        
        @Override
        public java.util.Properties getClientInfo() throws SQLException {
            return delegate.getClientInfo();
        }
        
        @Override
        public java.sql.Array createArrayOf(String typeName, Object[] elements) throws SQLException {
            return delegate.createArrayOf(typeName, elements);
        }
        
        @Override
        public java.sql.Struct createStruct(String typeName, Object[] attributes) throws SQLException {
            return delegate.createStruct(typeName, attributes);
        }
        
        @Override
        public void setSchema(String schema) throws SQLException {
            delegate.setSchema(schema);
        }
        
        @Override
        public String getSchema() throws SQLException {
            return delegate.getSchema();
        }
        
        @Override
        public void abort(java.util.concurrent.Executor executor) throws SQLException {
            delegate.abort(executor);
        }
        
        @Override
        public void setNetworkTimeout(java.util.concurrent.Executor executor, int milliseconds) throws SQLException {
            delegate.setNetworkTimeout(executor, milliseconds);
        }
        
        @Override
        public int getNetworkTimeout() throws SQLException {
            return delegate.getNetworkTimeout();
        }
        
        @Override
        public <T> T unwrap(Class<T> iface) throws SQLException {
            return delegate.unwrap(iface);
        }
        
        @Override
        public boolean isWrapperFor(Class<?> iface) throws SQLException {
            return delegate.isWrapperFor(iface);
        }
    }
    
    /**
     * Get pool statistics
     */
    public static String getPoolStatistics() {
        if (!initialized) {
            return "Pool not initialized";
        }

        return String.format(
            "Pool Stats - Active: %d, Idle: %d, Total Created: %d",
            activeConnections.get(),
            connectionPool.size(),
            totalConnectionsCreated.get()
        );
    }

    /**
     * Log pool statistics
     */
    private static void logPoolStatistics() {
        if (initialized) {
            System.out.println("📊 " + getPoolStatistics());
            System.out.println("📈 Total connections created: " + totalConnectionsCreated.get());
        }
    }

    /**
     * Check if pool is healthy
     */
    public static boolean isPoolHealthy() {
        if (!initialized) return false;

        try (Connection conn = getConnection()) {
            return conn.isValid(5);
        } catch (SQLException e) {
            return false;
        }
    }

    /**
     * Shutdown the pool
     */
    public static void shutdown() {
        if (initialized) {
            // Close all connections in pool
            Connection conn;
            while ((conn = connectionPool.poll()) != null) {
                try {
                    conn.close();
                } catch (SQLException e) {
                    System.err.println("Error closing connection: " + e.getMessage());
                }
            }
            initialized = false;
            System.out.println("🔒 Database connection pool shutdown completed");
        }
    }
    
    /**
     * Get initialization time
     */
    public static long getInitializationTime() {
        return initializationTime;
    }
    
    /**
     * Get active connections count
     */
    public static int getActiveConnectionsCount() {
        return activeConnections.get();
    }
    
    /**
     * Get total connections created
     */
    public static int getTotalConnectionsCreated() {
        return totalConnectionsCreated.get();
    }
}
