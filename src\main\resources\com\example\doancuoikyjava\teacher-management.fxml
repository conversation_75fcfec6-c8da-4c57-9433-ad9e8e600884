<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.example.doancuoikyjava.controller.TeacherManagementController">
   <top>
      <HBox alignment="CENTER_LEFT" styleClass="nav-bar" spacing="20.0">
         <children>
            <Button fx:id="backButton" onAction="#goBack" styleClass="nav-button" text="← Quay lại" />
            <Label styleClass="nav-button" text="👨‍🏫 QUẢN LÝ GIÁO VIÊN">
               <font>
                  <Font name="System Bold" size="16.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Label fx:id="welcomeLabel" styleClass="nav-button" text="Xin chào, Admin">
               <font>
                  <Font size="14.0" />
               </font>
            </Label>
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </HBox>
   </top>
   <center>
      <VBox styleClass="content-container" spacing="20.0">
         <children>
            <HBox alignment="CENTER_LEFT" spacing="20.0">
               <children>
                  <Label styleClass="label-title" text="Danh sách giáo viên">
                     <font>
                        <Font name="System Bold" size="24.0" />
                     </font>
                  </Label>
                  <Region HBox.hgrow="ALWAYS" />
                  <Button fx:id="addTeacherBtn" onAction="#showAddTeacherDialog" styleClass="btn,btn-primary" text="+ Thêm giáo viên" />
                  <Button fx:id="refreshBtn" onAction="#refreshData" styleClass="btn,btn-secondary" text="🔄 Làm mới" />
               </children>
            </HBox>
            
            <HBox spacing="15.0">
               <children>
                  <TextField fx:id="searchField" promptText="Tìm kiếm theo tên, mã GV..." HBox.hgrow="ALWAYS" />
                  <ComboBox fx:id="departmentFilterComboBox" promptText="Lọc theo khoa" />
                  <ComboBox fx:id="positionFilterComboBox" promptText="Lọc theo chức vụ" />
                  <Button fx:id="searchBtn" onAction="#searchTeachers" styleClass="btn,btn-warning" text="Tìm kiếm" />
               </children>
            </HBox>
            
            <TableView fx:id="teachersTableView" VBox.vgrow="ALWAYS">
               <columns>
                  <TableColumn fx:id="teacherIdColumn" prefWidth="100.0" text="Mã GV" />
                  <TableColumn fx:id="fullNameColumn" prefWidth="200.0" text="Họ và tên" />
                  <TableColumn fx:id="departmentColumn" prefWidth="150.0" text="Khoa" />
                  <TableColumn fx:id="positionColumn" prefWidth="120.0" text="Chức vụ" />
                  <TableColumn fx:id="qualificationColumn" prefWidth="120.0" text="Trình độ" />

                  <TableColumn fx:id="teachingCoursesColumn" prefWidth="200.0" text="Môn giảng dạy" />
                  <TableColumn fx:id="emailColumn" prefWidth="200.0" text="Email" />
                  <TableColumn fx:id="phoneColumn" prefWidth="120.0" text="Điện thoại" />
                  <TableColumn fx:id="actionsColumn" prefWidth="150.0" text="Thao tác" />
               </columns>
            </TableView>
            
            <HBox alignment="CENTER_LEFT" spacing="20.0">
               <children>
                  <Label fx:id="totalTeachersLabel" text="Tổng số giáo viên: 0" />
                  <Region HBox.hgrow="ALWAYS" />
                  <Button fx:id="exportBtn" onAction="#exportData" styleClass="btn,btn-success" text="📊 Xuất Excel" />
                  <Button fx:id="deleteSelectedBtn" onAction="#deleteSelectedTeachers" styleClass="btn,btn-danger" text="🗑 Xóa đã chọn" />
               </children>
            </HBox>
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>
      </VBox>
   </center>
</BorderPane>
